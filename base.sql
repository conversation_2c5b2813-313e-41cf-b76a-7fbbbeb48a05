/*
 Navicat Premium Dump SQL

 Source Server         : 新从库只读
 Source Server Type    : MySQL
 Source Server Version : 50743 (5.7.43-log)
 Source Host           : **************:3336
 Source Schema         : new_gaoxiaojob

 Target Server Type    : MySQL
 Target Server Version : 50743 (5.7.43-log)
 File Encoding         : 65001

 Date: 15/07/2025 23:14:32
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for activity
-- ----------------------------
DROP TABLE IF EXISTS `activity`;
CREATE TABLE `activity`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `name`        varchar(256) NOT NULL DEFAULT '' COMMENT '活动的名称',
    `begin_time`  datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '开始时间',
    `end_time`    datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结束时间',
    `token`       varchar(64)  NOT NULL DEFAULT '' COMMENT '活动token',
    `view`        varchar(256) NOT NULL DEFAULT '' COMMENT '模板view名称',
    `form_rule`   text         NOT NULL COMMENT '表单规则(json保存,里面有key,有name,有是否必填和正则)',
    `remark`      text         NOT NULL COMMENT '备注，可以临时放一些东西进去，方便以后扩增',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 61
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动';

-- ----------------------------
-- Table structure for activity_form
-- ----------------------------
DROP TABLE IF EXISTS `activity_form`;
CREATE TABLE `activity_form`
(
    `id`                                        int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                                  datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                               datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                                    tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `activity_id`                               int(11)       NOT NULL DEFAULT '0' COMMENT '活动id',
    `name`                                      varchar(128)  NOT NULL DEFAULT '' COMMENT '活动表单名称',
    `code`                                      varchar(128)  NOT NULL DEFAULT '' COMMENT '表单代码',
    `link`                                      varchar(256)  NOT NULL DEFAULT '' COMMENT '报名链接',
    `background_url`                            varchar(256)  NOT NULL DEFAULT '' COMMENT '背景图',
    `introduction`                              mediumtext COMMENT '导语',
    `conclusion`                                mediumtext COMMENT '结束语',
    `remark`                                    varchar(128)  NOT NULL DEFAULT '' COMMENT '备注',
    `intent_explain`                            varchar(256)  NOT NULL DEFAULT '' COMMENT '意向说明',
    `option_ids`                                varchar(2048) NOT NULL DEFAULT '' COMMENT '选项ids',
    `show_message`                              varchar(1024) NOT NULL DEFAULT '' COMMENT '显示题目信息',
    `upload_instructions`                       varchar(256)  NOT NULL DEFAULT '' COMMENT '简历附件上传说明',
    `wechat_instructions`                       varchar(256)  NOT NULL DEFAULT '' COMMENT '微信填写说明',
    `reference_instructions`                    varchar(256)  NOT NULL DEFAULT '' COMMENT '推荐人说明',
    `success_tips`                              varchar(512)  NOT NULL DEFAULT '' COMMENT '成功文案',
    `community_file_ids`                        varchar(2048) NOT NULL DEFAULT '' COMMENT '社区引导图ids',
    `option_item_ids`                           varchar(2048) NOT NULL DEFAULT '' COMMENT '显示过的选项ids',
    `postdoctor_institution_instructions`       varchar(512)  NOT NULL DEFAULT '' COMMENT '博士后培养机构填写说明',
    `postdoctor_overseas_duration_instructions` varchar(512)  NOT NULL DEFAULT '' COMMENT '博士毕业后的海外连续工作时长填写说明：',
    `max_count`                                 int(11)       NOT NULL DEFAULT '0' COMMENT '最多报名场次',
    PRIMARY KEY (`id`),
    KEY `idx_activity_id` (`activity_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 84
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动表单';

-- ----------------------------
-- Table structure for activity_form_intention_option
-- ----------------------------
DROP TABLE IF EXISTS `activity_form_intention_option`;
CREATE TABLE `activity_form_intention_option`
(
    `id`               int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`         datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`           tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `activity_form_id` int(11)       NOT NULL DEFAULT '0' COMMENT '表单ID',
    `title`            varchar(128)  NOT NULL DEFAULT '' COMMENT '选项文案',
    `type`             tinyint(2)    NOT NULL DEFAULT '0' COMMENT '选项类型',
    `sort`             int(3)        NOT NULL DEFAULT '0' COMMENT '排序',
    `is_show`          tinyint(1)    NOT NULL DEFAULT '2' COMMENT '显示隐藏，1:显示，2:隐藏',
    `version`          varchar(128)  NOT NULL DEFAULT '' COMMENT '文本文案',
    `link`             varchar(512)  NOT NULL DEFAULT '' COMMENT '链接',
    `handle_id`        int(11)       NOT NULL DEFAULT '0' COMMENT '操作人员id',
    `link_version`     varchar(32)   NOT NULL DEFAULT '' COMMENT '链接文本文案',
    `sign_desc`        varchar(1024) NOT NULL DEFAULT '' COMMENT '签到说明',
    `sign_url`         varchar(512)  NOT NULL DEFAULT '' COMMENT '签到链接',
    `sign_code_url`    varchar(512)  NOT NULL DEFAULT '' COMMENT '签到url',
    PRIMARY KEY (`id`),
    KEY `idx_activity_form_id` (`activity_form_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1544
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动表单报名意向选项表';

-- ----------------------------
-- Table structure for activity_form_option_sign
-- ----------------------------
DROP TABLE IF EXISTS `activity_form_option_sign`;
CREATE TABLE `activity_form_option_sign`
(
    `id`                   int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`             datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `activity_form_id`     int(11)    NOT NULL DEFAULT '0' COMMENT '活动表单id',
    `option_id`            int(11)    NOT NULL DEFAULT '0' COMMENT '活动选项场次id',
    `registration_form_id` int(11)    NOT NULL DEFAULT '0' COMMENT '报名表id',
    `resume_id`            int(11)    NOT NULL DEFAULT '0' COMMENT '人才id',
    `serial_number`        int(11)    NOT NULL DEFAULT '0' COMMENT '序列号',
    `is_sign`              tinyint(1) NOT NULL DEFAULT '2' COMMENT '是否已签到，1:是；2:否',
    `sign_time`            datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '签到时间',
    PRIMARY KEY (`id`),
    KEY `idx_activity_form_id` (`activity_form_id`) USING BTREE,
    KEY `idx_option_id` (`option_id`) USING BTREE,
    KEY `idx_registration_form_id` (`registration_form_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 78061
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动表单场次签到';

-- ----------------------------
-- Table structure for activity_form_registration_form
-- ----------------------------
DROP TABLE IF EXISTS `activity_form_registration_form`;
CREATE TABLE `activity_form_registration_form`
(
    `id`                           int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                     datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                  datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                       tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `option_ids`                   varchar(1024) NOT NULL DEFAULT '' COMMENT '报名意向ids',
    `activity_form_id`             int(11)       NOT NULL DEFAULT '0' COMMENT '活动表单ID',
    `resume_id`                    int(11)       NOT NULL DEFAULT '0' COMMENT '用户id',
    `name`                         varchar(32)   NOT NULL DEFAULT '' COMMENT '姓名',
    `gender`                       tinyint(1)    NOT NULL DEFAULT '1' COMMENT '性别，1:男；2:女',
    `birthday`                     date          NOT NULL DEFAULT '0000-00-00' COMMENT '出生日期',
    `household_register_id`        int(10)       NOT NULL DEFAULT '0' COMMENT '户籍/国籍',
    `mobile`                       varchar(16)   NOT NULL DEFAULT '' COMMENT '手机',
    `email`                        varchar(256)  NOT NULL DEFAULT '' COMMENT '邮箱',
    `political_status_id`          tinyint(3)    NOT NULL DEFAULT '0' COMMENT '政治面貌',
    `title_id`                     varchar(32)   NOT NULL DEFAULT '' COMMENT '职称',
    `school`                       varchar(128)  NOT NULL DEFAULT '' COMMENT '学校',
    `college`                      varchar(128)  NOT NULL DEFAULT '' COMMENT '二级院校',
    `education_id`                 tinyint(2)    NOT NULL DEFAULT '0' COMMENT '学历水平',
    `begin_date`                   date          NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间',
    `end_date`                     date          NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间',
    `major_id`                     varchar(64)   NOT NULL DEFAULT '' COMMENT '专业id',
    `major_custom`                 varchar(128)  NOT NULL DEFAULT '' COMMENT '自定义专业',
    `is_recruitment`               tinyint(1)    NOT NULL DEFAULT '0' COMMENT '是否统招,1:是；2否',
    `is_project_school`            tinyint(1)    NOT NULL DEFAULT '0' COMMENT '是否985/211，1:是；2:否',
    `is_abroad`                    tinyint(1)    NOT NULL DEFAULT '0' COMMENT '是否留学，1:是；2:否',
    `work_status`                  tinyint(1)    NOT NULL DEFAULT '0' COMMENT '求职状态',
    `arrive_date_type`             tinyint(2)    NOT NULL DEFAULT '0' COMMENT '到岗时间类型',
    `job_category_id`              int(64)       NOT NULL DEFAULT '0' COMMENT '意向职位ID',
    `nature_type`                  int(11)       NOT NULL DEFAULT '0' COMMENT '工作性质',
    `area_id`                      varchar(128)  NOT NULL DEFAULT '' COMMENT '意向城市ID',
    `wage_type`                    tinyint(1)    NOT NULL DEFAULT '0' COMMENT '期望月薪',
    `channels`                     varchar(128)  NOT NULL DEFAULT '' COMMENT '了解渠道',
    `unit_type`                    varchar(128)  NOT NULL DEFAULT '' COMMENT '就业单位类型',
    `is_share`                     tinyint(1)    NOT NULL DEFAULT '0' COMMENT '是否推荐，1:是；2:否',
    `file_token`                   varchar(512)  NOT NULL DEFAULT '' COMMENT '附件简历',
    `wechat_number`                varchar(64)   NOT NULL DEFAULT '' COMMENT '微信号',
    `reference`                    varchar(256)  NOT NULL DEFAULT '' COMMENT '推荐人',
    `employment_status`            tinyint(1)    NOT NULL DEFAULT '0' COMMENT '您目前的学业/就业状态是（1～8）',
    `postdoctor_institution`       varchar(256)  NOT NULL DEFAULT '' COMMENT '博士后培养机构',
    `postdoctor_overseas_duration` tinyint(1)    NOT NULL DEFAULT '0' COMMENT '博士毕业后的海外连续工作时长（含海外博士后）（1~8）',
    `residence`                    int(11)       NOT NULL DEFAULT '0' COMMENT '现居住地',
    PRIMARY KEY (`id`),
    KEY `idx_activity_form_id` (`activity_form_id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_household_register_id` (`household_register_id`) USING BTREE,
    KEY `idx_political_status_id` (`political_status_id`) USING BTREE,
    KEY `idx_education_id` (`education_id`) USING BTREE,
    KEY `idx_job_category_id` (`job_category_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 35096
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动表单报名表';

-- ----------------------------
-- Table structure for activity_form_registration_form_log
-- ----------------------------
DROP TABLE IF EXISTS `activity_form_registration_form_log`;
CREATE TABLE `activity_form_registration_form_log`
(
    `id`                           int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                     datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                  datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                       tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`                    int(11)       NOT NULL DEFAULT '0' COMMENT '用户id',
    `activity_form_id`             varchar(32)   NOT NULL DEFAULT '' COMMENT '关联报名id',
    `option_information`           varchar(1024) NOT NULL DEFAULT '' COMMENT '报名意向信息',
    `base_information`             varchar(1024) NOT NULL DEFAULT '' COMMENT '基本信息',
    `top_education`                varchar(1024) NOT NULL DEFAULT '' COMMENT '最高教育经历信息',
    `job_intention`                varchar(1024) NOT NULL DEFAULT '' COMMENT '求职意向信息',
    `unit_type`                    varchar(1024) NOT NULL DEFAULT '' COMMENT '就业单位类型信息',
    `channels`                     varchar(128)  NOT NULL DEFAULT '' COMMENT '了解渠道',
    `is_share`                     tinyint(1)    NOT NULL DEFAULT '0' COMMENT '是否推荐',
    `file_token`                   varchar(512)  NOT NULL DEFAULT '' COMMENT '附件简历',
    `wechat_number`                varchar(64)   NOT NULL DEFAULT '' COMMENT '微信号码',
    `reference`                    varchar(256)  NOT NULL DEFAULT '0' COMMENT '推荐人',
    `employment_status`            tinyint(1)    NOT NULL DEFAULT '0' COMMENT '您目前的学业/就业状态是（1～8）',
    `postdoctor_institution`       varchar(256)  NOT NULL DEFAULT '' COMMENT '博士后培养机构',
    `postdoctor_overseas_duration` tinyint(1)    NOT NULL DEFAULT '0' COMMENT '博士毕业后的海外连续工作时长（含海外博士后）（1~8）',
    `residence`                    int(11)       NOT NULL DEFAULT '0' COMMENT '现居住地',
    PRIMARY KEY (`id`),
    KEY `idx_activity_form_id` (`activity_form_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 33554
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动表单报名记录表';

-- ----------------------------
-- Table structure for admin
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin`
(
    `id`              int(11)          NOT NULL AUTO_INCREMENT,
    `add_time`        datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`          tinyint(1)       NOT NULL DEFAULT '0' COMMENT '状态',
    `name`            varchar(32)      NOT NULL DEFAULT '' COMMENT '名称',
    `username`        varchar(32)      NOT NULL DEFAULT '' COMMENT '用户名',
    `password`        varchar(64)      NOT NULL DEFAULT '' COMMENT '密码',
    `job_number`      varchar(64)      NOT NULL DEFAULT '' COMMENT '工号',
    `wx_work_userid`  varchar(64)      NOT NULL DEFAULT '' COMMENT '企业微信userid',
    `email`           varchar(256)     NOT NULL DEFAULT '' COMMENT '邮箱',
    `position_id`     int(11)          NOT NULL DEFAULT '0' COMMENT '职位id',
    `department_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '部门id',
    `last_login_time` datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '最近一次登录的时间',
    `last_login_ip`   int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最近一次登录的ip地址',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_username` (`username`) USING BTREE,
    KEY `idx_department_id` (`department_id`) USING BTREE,
    KEY `idx_position_id` (`position_id`) USING BTREE,
    KEY `idx_job_number` (`job_number`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 244
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for admin_department
-- ----------------------------
DROP TABLE IF EXISTS `admin_department`;
CREATE TABLE `admin_department`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '0：无效，1：有效',
    `name`        varchar(128) NOT NULL DEFAULT '' COMMENT '名称;部门名称',
    `key`         varchar(64)  NOT NULL COMMENT '关键字',
    `parent_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '父部门id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 20
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for admin_download_task
-- ----------------------------
DROP TABLE IF EXISTS `admin_download_task`;
CREATE TABLE `admin_download_task`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `begin_time`  datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '开始下载的时间',
    `finish_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结束下载的时间',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `admin_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '管理员id',
    `type`        tinyint(1)   NOT NULL DEFAULT '0' COMMENT '类型',
    `params`      text COMMENT '参数，根据每个下载任务不一样，放在json在里面',
    `path`        varchar(512) NOT NULL DEFAULT '' COMMENT '路径',
    `reason`      varchar(512) NOT NULL DEFAULT '' COMMENT '下载失败的原因，',
    `fail_count`  int(11)      NOT NULL DEFAULT '0' COMMENT '失败次数，这里包含一个重试机制',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_admin_id` (`admin_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 15840
  DEFAULT CHARSET = utf8mb4 COMMENT ='运营后台下载任务';

-- ----------------------------
-- Table structure for admin_job_invite
-- ----------------------------
DROP TABLE IF EXISTS `admin_job_invite`;
CREATE TABLE `admin_job_invite`
(
    `id`              int(11)       NOT NULL AUTO_INCREMENT,
    `add_time`        datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `type`            int(11)       NOT NULL DEFAULT '0' COMMENT '0 普通 1邮件,2短信,3邮件+短信',
    `update_time`     datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `company_id`      int(11)       NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`       int(11)       NOT NULL DEFAULT '0' COMMENT '简历id',
    `job_id`          int(11)       NOT NULL DEFAULT '0' COMMENT '职位id',
    `email_log_id`    int(11)       NOT NULL DEFAULT '0' COMMENT '邮件id',
    `sms_log_id`      int(11)       NOT NULL DEFAULT '0' COMMENT '短信id',
    `remark`          varchar(1024) NOT NULL DEFAULT '' COMMENT '邀约备注',
    `is_remind_check` tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否已查看,1是2否',
    `invite_id`       int(11)       NOT NULL DEFAULT '0' COMMENT '邀约配置ID',
    `admin_id`        int(11)       NOT NULL DEFAULT '0' COMMENT '邀请人ID',
    `status`          tinyint(1)    NOT NULL DEFAULT '1' COMMENT '邀约状态 1成功 2失败',
    `description`     varchar(255)  NOT NULL DEFAULT '' COMMENT '邀约描述',
    `is_apply`        tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否已投递, 1是, 2否',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_invite_id` (`invite_id`) USING BTREE,
    KEY `idx_admin_id` (`admin_id`) USING BTREE,
    KEY `idx_is_apply` (`is_apply`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 275439
  DEFAULT CHARSET = utf8mb4 COMMENT ='运营平台邀约记录';

-- ----------------------------
-- Table structure for admin_job_invite_config
-- ----------------------------
DROP TABLE IF EXISTS `admin_job_invite_config`;
CREATE TABLE `admin_job_invite_config`
(
    `id`                   int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`             datetime         NOT NULL COMMENT '添加时间',
    `update_time`          datetime                  DEFAULT NULL COMMENT '更新时间',
    `way_type`             tinyint(1)       NOT NULL DEFAULT '1' COMMENT '邀约方式 1手动邀约 2智能邀约',
    `text_type`            tinyint(2)       NOT NULL DEFAULT '1' COMMENT '邀约文案类型（1=通用版，2=版本2，3=版本3，4=版本4，5=版本5，66=自定义）',
    `text_content`         varchar(255)     NOT NULL DEFAULT '' COMMENT '邀约文案内容',
    `match_type`           varchar(60)      NOT NULL DEFAULT '' COMMENT '人才匹配项类型  1学历 2专业 3意向职位  4意向城市 5工作经验 6职称 7政治面貌 8海外经验',
    `invite_job_type`      tinyint(2)       NOT NULL DEFAULT '0' COMMENT '已邀约过该职位 0不过滤  1=7天  2=15天  3=30天',
    `apply_type`           tinyint(2)       NOT NULL DEFAULT '0' COMMENT '已投递过该职位  0不过滤  1=7天  2=15天  3=30天',
    `resume_complete_type` tinyint(2)       NOT NULL DEFAULT '0' COMMENT '简历完善度   0不过滤  1要过滤',
    `invite_type`          tinyint(2)       NOT NULL DEFAULT '0' COMMENT '已邀约过  0不过滤  1=7天  2=15天  3=30天',
    `job_id`               int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `active_day`           int(4)           NOT NULL DEFAULT '0' COMMENT '人才活跃时间  单位：天',
    `invite_number`        int(5)           NOT NULL DEFAULT '0' COMMENT '邀请人数',
    `invite_time`          datetime         NOT NULL COMMENT '邀约时间',
    `remark`               varchar(255)     NOT NULL DEFAULT '' COMMENT '备注',
    `invite_resume_set`    varchar(2550)    NOT NULL DEFAULT '' COMMENT '邀请人才Ids',
    `match_content`        text COMMENT '人才匹配项的内容josn',
    `admin_id`             int(11)          NOT NULL DEFAULT '0' COMMENT '邀约人ID',
    `status_call`          tinyint(2)       NOT NULL DEFAULT '1' COMMENT '执行状态 1待执行  2执行完成  9执行中',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_admin_id` (`admin_id`) USING BTREE,
    KEY `idx_way_type` (`way_type`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2299
  DEFAULT CHARSET = utf8mb4 COMMENT ='运营平台邀约配置记录';

-- ----------------------------
-- Table structure for admin_login_log
-- ----------------------------
DROP TABLE IF EXISTS `admin_login_log`;
CREATE TABLE `admin_login_log`
(
    `id`         int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `add_time`   datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `admin_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '管理员id',
    `os`         varchar(32)      NOT NULL DEFAULT '' COMMENT '操作系统',
    `os_version` varchar(32)      NOT NULL DEFAULT '' COMMENT '操作系统版本',
    `model`      varchar(64)      NOT NULL DEFAULT '' COMMENT '设备信息',
    `user_agent` varchar(512)     NOT NULL DEFAULT '' COMMENT '头信息',
    `ip`         int(11) unsigned NOT NULL DEFAULT '0' COMMENT '登录的ip',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_admin_id` (`admin_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 131311
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for admin_menu
-- ----------------------------
DROP TABLE IF EXISTS `admin_menu`;
CREATE TABLE `admin_menu`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态',
    `name`        varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
    `parent_id`   int(11)     NOT NULL DEFAULT '0' COMMENT '父id',
    `level`       tinyint(1)  NOT NULL DEFAULT '0' COMMENT '等级(为未来做三级菜单做准备，现阶段只有两级)',
    `key`         varchar(64) NOT NULL DEFAULT '' COMMENT '前端的name(在这里用key表示，相对好理解)',
    `is_route`    tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否路由(1代表这个是路由，而不是父级)',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 129
  DEFAULT CHARSET = utf8mb4 COMMENT ='运营后台前端菜单';

-- ----------------------------
-- Table structure for admin_menu_action
-- ----------------------------
DROP TABLE IF EXISTS `admin_menu_action`;
CREATE TABLE `admin_menu_action`
(
    `id`            int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`        tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态',
    `admin_menu_id` int(11)     NOT NULL DEFAULT '0' COMMENT '父id',
    `name`          varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
    `key`           varchar(32) NOT NULL DEFAULT '' COMMENT '前端的name(在这里用key表示，相对好理解)',
    PRIMARY KEY (`id`),
    KEY `idx_admin_menu_id` (`admin_menu_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 18
  DEFAULT CHARSET = utf8mb4 COMMENT ='运营后台前端菜单操作';

-- ----------------------------
-- Table structure for admin_message
-- ----------------------------
DROP TABLE IF EXISTS `admin_message`;
CREATE TABLE `admin_message`
(
    `id`                int(11)             NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`          datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `admin_id`          int(11)             NOT NULL DEFAULT '0' COMMENT '会员id',
    `type`              tinyint(1)          NOT NULL DEFAULT '0' COMMENT '单位类型1:系统消息,2:应聘消息',
    `title`             varchar(30)         NOT NULL DEFAULT '' COMMENT '消息标题',
    `content`           varchar(256)        NOT NULL DEFAULT '' COMMENT '消息内容',
    `inner_link`        varchar(256)        NOT NULL DEFAULT '' COMMENT '内部链接',
    `inner_link_params` varchar(256)        NOT NULL DEFAULT '' COMMENT '内部链接参数',
    `is_read`           tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已读0:未读，1:已读',
    `is_delete`         tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除0:未删除，1:已删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_admin_id` (`admin_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for admin_position
-- ----------------------------
DROP TABLE IF EXISTS `admin_position`;
CREATE TABLE `admin_position`
(
    `id`            int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `add_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`        tinyint(1)   NOT NULL DEFAULT '0' COMMENT '0：无效，1：有效',
    `name`          varchar(128) NOT NULL DEFAULT '' COMMENT '名称;部门名称',
    `key`           varchar(64)  NOT NULL COMMENT '关键字',
    `department_id` int(11)      NOT NULL DEFAULT '0' COMMENT '部门id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `add_time` (`add_time`) USING BTREE,
    KEY `idx_department_id` (`department_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 78
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for admin_position_menu
-- ----------------------------
DROP TABLE IF EXISTS `admin_position_menu`;
CREATE TABLE `admin_position_menu`
(
    `id`                    int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`              datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`                tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `admin_position_id`     int(11)      NOT NULL DEFAULT '0' COMMENT '角色名称',
    `admin_menu_id`         int(11)      NOT NULL DEFAULT '0' COMMENT '菜单id',
    `admin_menu_action_ids` varchar(256) NOT NULL DEFAULT '' COMMENT '操作权限ids',
    PRIMARY KEY (`id`),
    KEY `idx_admin_position_id` (`admin_position_id`),
    KEY `idx_admin_menu_id` (`admin_menu_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 13302
  DEFAULT CHARSET = utf8mb4 COMMENT ='角色权限菜单';

-- ----------------------------
-- Table structure for admin_statement_builder_title_config
-- ----------------------------
DROP TABLE IF EXISTS `admin_statement_builder_title_config`;
CREATE TABLE `admin_statement_builder_title_config`
(
    `id`     int(10) unsigned NOT NULL AUTO_INCREMENT,
    `code`   int(10)          NOT NULL DEFAULT '0' COMMENT '代号',
    `title`  varchar(128)     NOT NULL DEFAULT '' COMMENT '标题',
    `status` tinyint(2)       NOT NULL DEFAULT '1' COMMENT '状态 1正常 2 隐藏',
    `tabs`   tinyint(2)       NOT NULL DEFAULT '1' COMMENT '分类(即tabs:1人才 2单位 3职位 4公告 5投递)',
    `type`   tinyint(2)       NOT NULL DEFAULT '1' COMMENT '类型：1明细 2统计',
    `remark` varchar(255)     NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code` (`code`) USING BTREE,
    KEY `idx_type` (`type`) USING BTREE,
    KEY `idx_tabs` (`tabs`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 169
  DEFAULT CHARSET = utf8mb4 COMMENT ='报表生成器标题(表头)配置表';

-- ----------------------------
-- Table structure for agreement_config
-- ----------------------------
DROP TABLE IF EXISTS `agreement_config`;
CREATE TABLE `agreement_config`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `type`        tinyint(2)       NOT NULL DEFAULT '0' COMMENT '类型 1、VIP服务协议  2、求职快服务协议',
    `name`        varchar(255)     NOT NULL DEFAULT '' COMMENT '协议名称',
    `content`     text             NOT NULL COMMENT '协议内容',
    `version`     varchar(255)     NOT NULL COMMENT '版本号',
    `add_time`    datetime         NOT NULL COMMENT '添加时间',
    `update_time` datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(4)       NOT NULL DEFAULT '1' COMMENT '状态:1当前显示版本，2历史版本',
    `description` varchar(255)     NOT NULL DEFAULT '' COMMENT '描述',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_type_version` (`type`, `version`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 4
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for announcement
-- ----------------------------
DROP TABLE IF EXISTS `announcement`;
CREATE TABLE `announcement`
(
    `id`                       int(11)             NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                 datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`              datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `audit_status`             tinyint(1)          NOT NULL DEFAULT '0' COMMENT '审核状态（1审核通过，-1审核拒绝，7等待审核，3草稿）',
    `title`                    varchar(256)        NOT NULL DEFAULT '' COMMENT '公告标题或单位名称',
    `article_id`               int(11)             NOT NULL DEFAULT '0' COMMENT '文章id',
    `member_id`                int(11)             NOT NULL DEFAULT '0' COMMENT '会员id',
    `company_id`               int(11)             NOT NULL DEFAULT '0' COMMENT '企业id',
    `create_type`              tinyint(1)          NOT NULL DEFAULT '0' COMMENT '创建的类型(1企业自行发布，2运营人员代发布,3平台公告,不属于任何企业）',
    `creator_id`               int(11)             NOT NULL DEFAULT '0' COMMENT '创建人的id（企业就企业的id)',
    `work_area_id`             varchar(32)         NOT NULL DEFAULT '' COMMENT '工作地点',
    `period_date`              datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '截止日期',
    `apply_type`               varchar(32)         NOT NULL DEFAULT '' COMMENT '应聘方式(1电子邮件xxxx',
    `apply_address`            varchar(600)        NOT NULL DEFAULT '' COMMENT '投递地址',
    `read_permissions`         varchar(32)         NOT NULL DEFAULT '' COMMENT '阅读权限（暂时不知道是要用作什么）',
    `major_ids`                varchar(32)         NOT NULL DEFAULT '' COMMENT '需求学科ids',
    `relation_company_ids`     varchar(64)         NOT NULL DEFAULT '' COMMENT '关联单位ids',
    `template_id`              int(11)             NOT NULL DEFAULT '1' COMMENT '页面模版id',
    `offline_time`             datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '操作下线时间',
    `status`                   tinyint(1)          NOT NULL DEFAULT '1',
    `is_consume_release`       tinyint(1)          NOT NULL DEFAULT '2' COMMENT '是否消费过发布（2否 1是）',
    `offline_type`             tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '下线方式 0:无；1:自动下线；2：手动下线；3:违规',
    `offline_reason`           varchar(255)        NOT NULL DEFAULT '' COMMENT '违规下线原因',
    `home_sort`                int(6)              NOT NULL DEFAULT '0' COMMENT '单位主页公告排序，0-999，数值越大越靠前',
    `file_ids`                 varchar(100)        NOT NULL DEFAULT '' COMMENT '应聘材料附件id对应file表id',
    `delivery_type`            tinyint(1)          NOT NULL DEFAULT '0' COMMENT '投递类型1=站外投递,2=站内投递',
    `delivery_way`             tinyint(1)          NOT NULL DEFAULT '0' COMMENT '投递方式 1平台投递 2邮箱投递 3网址投递',
    `extra_notify_address`     varchar(255)        NOT NULL DEFAULT '' COMMENT '投递通知地址',
    `is_miniapp`               tinyint(2)          NOT NULL DEFAULT '2' COMMENT '是否被小程序调用1调用 2没调用',
    `is_manual_tag`            tinyint(2)          NOT NULL DEFAULT '0' COMMENT '是否运营手动标记 0没标记 1标记 2不标记',
    `is_attachment_notice`     tinyint(1)          NOT NULL DEFAULT '2' COMMENT '是否附件提醒 1是 2否',
    `uuid`                     varchar(64)         NOT NULL DEFAULT '' COMMENT 'uuid',
    `announcement_heat_type`   tinyint(2)          NOT NULL DEFAULT '0' COMMENT '公告类型热度（90天内点击数量计算热度类型）',
    `establishment_type`       tinyint(2)          NOT NULL DEFAULT '0' COMMENT '编制类型',
    `address_hide_status`      tinyint(2)          NOT NULL DEFAULT '2' COMMENT '地址隐藏状态（1隐藏,2显示）',
    `online_job_amount`        int(11)             NOT NULL DEFAULT '0' COMMENT '在线职位数量',
    `all_job_amount`           int(11)             NOT NULL DEFAULT '0' COMMENT '职位总数（包含在线与下线）',
    `is_first_release`         tinyint(2)          NOT NULL DEFAULT '2' COMMENT '首发:1=首发,2=非首发',
    `sub_title`                varchar(2048)       NOT NULL DEFAULT '' COMMENT '公告简标题',
    `highlights_describe`      text COMMENT '公告亮点描述',
    `background_img_file_id`   int(10)             NOT NULL DEFAULT '0' COMMENT '背景图文件id',
    `background_img_file_type` tinyint(1)          NOT NULL DEFAULT '1' COMMENT '使用的背景图类型1模版默认背景，2单位主页背景，3自定义背景',
    `background_img_file_id_2` int(11)             NOT NULL DEFAULT '0' COMMENT '高级模版2使用图片id',
    `background_img_file_id_3` int(11)             NOT NULL DEFAULT '0' COMMENT '高级模版3使用图片id',
    `activity_job_content`     varchar(600)        NOT NULL DEFAULT '' COMMENT '活动招聘岗位',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_article_id` (`article_id`),
    KEY `idx_period_date` (`period_date`) USING BTREE,
    KEY `idx_uuid` (`uuid`),
    KEY `idx_announcement_heat_type` (`announcement_heat_type`),
    KEY `idx_establishment_type` (`establishment_type`),
    KEY `idx_is_first_release` (`is_first_release`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 329869
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告';

-- ----------------------------
-- Table structure for announcement_area_relation
-- ----------------------------
DROP TABLE IF EXISTS `announcement_area_relation`;
CREATE TABLE `announcement_area_relation`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `area_id`         int(11)          NOT NULL COMMENT '地区ID',
    `level`           tinyint(2)       NOT NULL COMMENT '层级 同area表',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_ area_id` (`area_id`, `level`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 5616184
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位地区的公告中间表';

-- ----------------------------
-- Table structure for announcement_auto_classify_log
-- ----------------------------
DROP TABLE IF EXISTS `announcement_auto_classify_log`;
CREATE TABLE `announcement_auto_classify_log`
(
    `id`                     int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`               datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`                 tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `announcement_id`        int(11)      NOT NULL DEFAULT '0' COMMENT '公告id',
    `before_home_column_ids` varchar(256) NOT NULL DEFAULT '' COMMENT '自动分配前所属栏目ids',
    `after_home_column_ids`  varchar(256) NOT NULL DEFAULT '' COMMENT '自动分配后所属栏目ids',
    `remark`                 text COMMENT '分配的过程记录',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 394420
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for announcement_boshihou
-- ----------------------------
DROP TABLE IF EXISTS `announcement_boshihou`;
CREATE TABLE `announcement_boshihou`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 22058
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for announcement_click_age_total
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_age_total`;
CREATE TABLE `announcement_click_age_total`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `range_1_17`      int(11)          NOT NULL DEFAULT '0' COMMENT '1_17',
    `range_18_25`     int(11)          NOT NULL DEFAULT '0' COMMENT '18_25',
    `range_26_30`     int(11)          NOT NULL DEFAULT '0' COMMENT '26_30',
    `range_31_35`     int(11)          NOT NULL DEFAULT '0' COMMENT '31_35',
    `range_36_40`     int(11)          NOT NULL DEFAULT '0' COMMENT '36_40',
    `range_41`        int(11)          NOT NULL DEFAULT '0' COMMENT '41+',
    `range_guest`     int(11)          NOT NULL DEFAULT '0' COMMENT '游客',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 389598
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告总阅读量表(年龄统计)';

-- ----------------------------
-- Table structure for announcement_click_age_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_age_total_daily`;
CREATE TABLE `announcement_click_age_total_daily`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`        date             NOT NULL COMMENT '时间格式',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `range_1_17`      int(11)          NOT NULL DEFAULT '0' COMMENT '1_17',
    `range_18_25`     int(11)          NOT NULL DEFAULT '0' COMMENT '18_25',
    `range_26_30`     int(11)          NOT NULL DEFAULT '0' COMMENT '26_30',
    `range_31_35`     int(11)          NOT NULL DEFAULT '0' COMMENT '31_35',
    `range_36_40`     int(11)          NOT NULL DEFAULT '0' COMMENT '36_40',
    `range_41`        int(11)          NOT NULL DEFAULT '0' COMMENT '41+',
    `range_guest`     int(11)          NOT NULL DEFAULT '0' COMMENT '游客',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_ad` (`announcement_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告日阅读量表(年龄统计)';

-- ----------------------------
-- Table structure for announcement_click_area_city_total
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_area_city_total`;
CREATE TABLE `announcement_click_area_city_total`
(
    `id`               int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `announcement_id`  int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `area_province_id` int(11)          NOT NULL DEFAULT '0' COMMENT '省份id,0:未知',
    `area_city_id`     int(11)          NOT NULL DEFAULT '0' COMMENT '城市id,0:未知',
    `total`            int(11)          NOT NULL DEFAULT '0' COMMENT '点击数量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_acid` (`announcement_id`, `area_city_id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_area_province_id` (`area_province_id`) USING BTREE,
    KEY `idx_area_city_id` (`area_city_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7821499
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告总阅读量表(城市地区统计)';

-- ----------------------------
-- Table structure for announcement_click_area_city_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_area_city_total_daily`;
CREATE TABLE `announcement_click_area_city_total_daily`
(
    `id`               int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`         date             NOT NULL COMMENT '时间格式',
    `announcement_id`  int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `area_province_id` int(11)          NOT NULL DEFAULT '0' COMMENT '省份id,0:未知',
    `area_city_id`     int(11)          NOT NULL DEFAULT '0' COMMENT '城市id,0:未知',
    `total`            int(11)          NOT NULL DEFAULT '0' COMMENT '点击数量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_acid_ad` (`announcement_id`, `area_city_id`, `add_date`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_area_province_id` (`area_province_id`) USING BTREE,
    KEY `idx_area_city_id` (`area_city_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告日阅读量表(城市地区统计)';

-- ----------------------------
-- Table structure for announcement_click_area_province_total
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_area_province_total`;
CREATE TABLE `announcement_click_area_province_total`
(
    `id`               int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `announcement_id`  int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `area_province_id` int(11)          NOT NULL DEFAULT '0' COMMENT '省份id,0:未知',
    `total`            int(11)          NOT NULL DEFAULT '0' COMMENT '点击数量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_apid` (`announcement_id`, `area_province_id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_area_province_id` (`area_province_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 6303074
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告总阅读量表(省份地区统计)';

-- ----------------------------
-- Table structure for announcement_click_area_province_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_area_province_total_daily`;
CREATE TABLE `announcement_click_area_province_total_daily`
(
    `id`               int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`         date             NOT NULL COMMENT '时间格式',
    `announcement_id`  int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `area_province_id` int(11)          NOT NULL DEFAULT '0' COMMENT '省份id,0:未知',
    `total`            int(11)          NOT NULL DEFAULT '0' COMMENT '点击数量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_apid_ad` (`announcement_id`, `area_province_id`, `add_date`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_area_province_id` (`area_province_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告日阅读量表(省份地区统计)';

-- ----------------------------
-- Table structure for announcement_click_column_total
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_column_total`;
CREATE TABLE `announcement_click_column_total`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `column_id_1`     int(11)          NOT NULL DEFAULT '0' COMMENT '栏目一级ID',
    `column_id_2`     int(11)          NOT NULL DEFAULT '0' COMMENT '栏目二级ID',
    `total`           int(11)          NOT NULL DEFAULT '0' COMMENT '数量',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_aid_cid2` (`announcement_id`, `column_id_2`),
    KEY `idx_announcement_id` (`announcement_id`),
    KEY `idx_column_id_1` (`column_id_1`),
    KEY `idx_column_id_2` (`column_id_2`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 389491
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告总阅读量表(栏目统计)';

-- ----------------------------
-- Table structure for announcement_click_column_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_column_total_daily`;
CREATE TABLE `announcement_click_column_total_daily`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`        date             NOT NULL COMMENT '时间格式',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `column_id_1`     int(11)          NOT NULL DEFAULT '0' COMMENT '栏目一级ID',
    `column_id_2`     int(11)          NOT NULL DEFAULT '0' COMMENT '栏目二级ID',
    `total`           int(11)          NOT NULL DEFAULT '0' COMMENT '数量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_cid2_ad` (`announcement_id`, `column_id_2`, `add_date`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_column_id_1` (`column_id_1`) USING BTREE,
    KEY `idx_column_id_2` (`column_id_2`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告日阅读量表(栏目统计)';

-- ----------------------------
-- Table structure for announcement_click_education_total
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_education_total`;
CREATE TABLE `announcement_click_education_total`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `guest`           int(11)          NOT NULL DEFAULT '0' COMMENT '游客',
    `rests`           int(11)          NOT NULL DEFAULT '0' COMMENT '其他',
    `college`         int(11)          NOT NULL DEFAULT '0' COMMENT '大专',
    `poaceae`         int(11)          NOT NULL DEFAULT '0' COMMENT '本科',
    `doctor`          int(11)          NOT NULL DEFAULT '0' COMMENT '博士',
    `master`          int(11)          NOT NULL DEFAULT '0' COMMENT '硕士',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 389481
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告总阅读量表(学历统计)';

-- ----------------------------
-- Table structure for announcement_click_education_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_education_total_daily`;
CREATE TABLE `announcement_click_education_total_daily`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`        date             NOT NULL COMMENT '时间格式',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `guest`           int(11)          NOT NULL DEFAULT '0' COMMENT '游客',
    `rests`           int(11)          NOT NULL DEFAULT '0' COMMENT '其他',
    `college`         int(11)          NOT NULL DEFAULT '0' COMMENT '大专',
    `poaceae`         int(11)          NOT NULL DEFAULT '0' COMMENT '本科',
    `doctor`          int(11)          NOT NULL DEFAULT '0' COMMENT '博士',
    `master`          int(11)          NOT NULL DEFAULT '0' COMMENT '硕士',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_ad` (`announcement_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告日阅读量表(学历统计)';

-- ----------------------------
-- Table structure for announcement_click_political_status_total
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_political_status_total`;
CREATE TABLE `announcement_click_political_status_total`
(
    `id`                        int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`               datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `announcement_id`           int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `masses`                    int(11)          NOT NULL DEFAULT '0' COMMENT '群众',
    `league_member`             int(11)          NOT NULL DEFAULT '0' COMMENT '共青团员',
    `party_member`              int(11)          NOT NULL DEFAULT '0' COMMENT '中共党员',
    `probationary_party_member` int(11)          NOT NULL DEFAULT '0' COMMENT '中共预备党员',
    `democratic_party`          int(11)          NOT NULL DEFAULT '0' COMMENT '民主党派',
    `no_democratic_party`       int(11)          NOT NULL DEFAULT '0' COMMENT '无民主党派',
    `guest`                     int(11)          NOT NULL DEFAULT '0' COMMENT '游客',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 389481
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告总阅读量表(政治面貌统计)';

-- ----------------------------
-- Table structure for announcement_click_political_status_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_political_status_total_daily`;
CREATE TABLE `announcement_click_political_status_total_daily`
(
    `id`                        int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`                  date             NOT NULL COMMENT '时间格式',
    `announcement_id`           int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `masses`                    int(11)          NOT NULL DEFAULT '0' COMMENT '群众',
    `league_member`             int(11)          NOT NULL DEFAULT '0' COMMENT '共青团员',
    `party_member`              int(11)          NOT NULL DEFAULT '0' COMMENT '中共党员',
    `probationary_party_member` int(11)          NOT NULL DEFAULT '0' COMMENT '中共预备党员',
    `democratic_party`          int(11)          NOT NULL DEFAULT '0' COMMENT '民主党派',
    `no_democratic_party`       int(11)          NOT NULL DEFAULT '0' COMMENT '无民主党派',
    `guest`                     int(11)          NOT NULL DEFAULT '0' COMMENT '游客',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_ad` (`announcement_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告日阅读量表(政治面貌统计)';

-- ----------------------------
-- Table structure for announcement_click_subtitle_total
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_subtitle_total`;
CREATE TABLE `announcement_click_subtitle_total`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `title_level_1`   int(11)          NOT NULL DEFAULT '0' COMMENT '一级职称ID',
    `title_level_2`   int(11)          NOT NULL DEFAULT '0' COMMENT '二级职称ID',
    `total`           int(11)          NOT NULL DEFAULT '0' COMMENT '点击量',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_aid_tl2` (`announcement_id`, `title_level_2`),
    KEY `idx_announcement_id` (`announcement_id`),
    KEY `idx_title_level_1` (`title_level_1`),
    KEY `idx_title_level_2` (`title_level_2`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 610990
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告总阅读量表(二级职称统计)';

-- ----------------------------
-- Table structure for announcement_click_subtitle_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_subtitle_total_daily`;
CREATE TABLE `announcement_click_subtitle_total_daily`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`        date             NOT NULL COMMENT '时间格式',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `title_level_1`   int(11)          NOT NULL DEFAULT '0' COMMENT '一级职称ID',
    `title_level_2`   int(11)          NOT NULL DEFAULT '0' COMMENT '二级职称ID',
    `total`           int(11)          NOT NULL DEFAULT '0' COMMENT '点击量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_tl2_ad` (`announcement_id`, `title_level_2`, `add_date`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_title_level_1` (`title_level_1`) USING BTREE,
    KEY `idx_title_level_2` (`title_level_2`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告日阅读量表(二级职称统计)';

-- ----------------------------
-- Table structure for announcement_click_title_total
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_title_total`;
CREATE TABLE `announcement_click_title_total`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `title_primary`   int(11)          NOT NULL DEFAULT '0' COMMENT '初级职称',
    `title_middle`    int(11)          NOT NULL DEFAULT '0' COMMENT '中级职称',
    `title_high`      int(11)          NOT NULL DEFAULT '0' COMMENT '正高级职称',
    `title_vice_high` int(11)          NOT NULL DEFAULT '0' COMMENT '副高级职称',
    `title_guest`     int(11)          NOT NULL DEFAULT '0' COMMENT '游客',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 389481
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告总阅读量表(一级职称统计)';

-- ----------------------------
-- Table structure for announcement_click_title_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_title_total_daily`;
CREATE TABLE `announcement_click_title_total_daily`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`        date             NOT NULL COMMENT '时间格式',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `title_primary`   int(11)          NOT NULL DEFAULT '0' COMMENT '初级职称',
    `title_middle`    int(11)          NOT NULL DEFAULT '0' COMMENT '中级职称',
    `title_high`      int(11)          NOT NULL DEFAULT '0' COMMENT '正高级职称',
    `title_vice_high` int(11)          NOT NULL DEFAULT '0' COMMENT '副高级职称',
    `title_guest`     int(11)          NOT NULL DEFAULT '0' COMMENT '游客',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_ad` (`announcement_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告日阅读量表(一级职称统计)';

-- ----------------------------
-- Table structure for announcement_click_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_total_daily`;
CREATE TABLE `announcement_click_total_daily`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_date`        date             NOT NULL COMMENT '时间格式',
    `announcement_id` int(11) unsigned NOT NULL COMMENT '公告ID',
    `total`           int(11)          NOT NULL DEFAULT '0' COMMENT '数量',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_announcement_id_add_date` (`announcement_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 23643439
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告日阅读量';

-- ----------------------------
-- Table structure for announcement_click_update_config
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_update_config`;
CREATE TABLE `announcement_click_update_config`
(
    `id`                          int(5) unsigned NOT NULL AUTO_INCREMENT,
    `class_name`                  varchar(255)    NOT NULL DEFAULT '' COMMENT '脚本类名称',
    `update_time`                 datetime        NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '脚本更新时间',
    `start_id`                    int(11)         NOT NULL DEFAULT '0' COMMENT '执行开始节点',
    `end_id`                      int(11)         NOT NULL DEFAULT '0' COMMENT '执行结束节点',
    `limit_max`                   int(11)         NOT NULL DEFAULT '0' COMMENT '最大limit数量',
    `real_execute_status`         tinyint(1)      NOT NULL DEFAULT '0' COMMENT '实时脚本执行状态,0:未完成,1:已完成',
    `history_execute_status`      tinyint(1)      NOT NULL DEFAULT '0' COMMENT '历史脚本执行状态,0:未完成,1:已完成',
    `history_log_complete_status` tinyint(1)      NOT NULL DEFAULT '0' COMMENT '历史数据完成状态,0:未完成,1:已完成',
    `real_fail_msg`               text COMMENT '实时脚本错误信息',
    `history_fail_msg`            text COMMENT '历史脚本错误信息',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告阅读量统计同步配置表';

-- ----------------------------
-- Table structure for announcement_click_work_experience_total
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_work_experience_total`;
CREATE TABLE `announcement_click_work_experience_total`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `range_1_3`       int(11)          NOT NULL DEFAULT '0' COMMENT '1-3',
    `range_3_5`       int(11)          NOT NULL DEFAULT '0' COMMENT '3-5',
    `range_5_10`      int(11)          NOT NULL DEFAULT '0' COMMENT '5-10',
    `range_10`        int(11)          NOT NULL DEFAULT '0' COMMENT '10+',
    `range_guest`     int(11)          NOT NULL DEFAULT '0' COMMENT '游客',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 389008
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告总阅读量表(工作经验统计)';

-- ----------------------------
-- Table structure for announcement_click_work_experience_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `announcement_click_work_experience_total_daily`;
CREATE TABLE `announcement_click_work_experience_total_daily`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`        date             NOT NULL COMMENT '时间格式',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `range_1_3`       int(11)          NOT NULL DEFAULT '0' COMMENT '1-3',
    `range_3_5`       int(11)          NOT NULL DEFAULT '0' COMMENT '3-5',
    `range_5_10`      int(11)          NOT NULL DEFAULT '0' COMMENT '5-10',
    `range_10`        int(11)          NOT NULL DEFAULT '0' COMMENT '10+',
    `range_guest`     int(11)          NOT NULL DEFAULT '0' COMMENT '游客',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_aid_ad` (`announcement_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告日阅读量表(工作经验统计)';

-- ----------------------------
-- Table structure for announcement_collect
-- ----------------------------
DROP TABLE IF EXISTS `announcement_collect`;
CREATE TABLE `announcement_collect`
(
    `id`              int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`          tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `member_id`       int(11)    NOT NULL DEFAULT '0' COMMENT '会员id',
    `announcement_id` int(11)    NOT NULL DEFAULT '0' COMMENT '公告id',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`),
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_update_time` (`update_time`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 568490
  DEFAULT CHARSET = utf8mb4 COMMENT ='收藏公告表';

-- ----------------------------
-- Table structure for announcement_edit
-- ----------------------------
DROP TABLE IF EXISTS `announcement_edit`;
CREATE TABLE `announcement_edit`
(
    `id`              int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`          tinyint(1)  NOT NULL DEFAULT '1' COMMENT '状态，1:在用；2:关闭',
    `announcement_id` int(11)     NOT NULL DEFAULT '0' COMMENT '公告id',
    `edit_content`    mediumtext  NOT NULL COMMENT '编辑修改内容',
    `editor`          varchar(32) NOT NULL DEFAULT '' COMMENT '编辑人名称',
    `editor_type`     tinyint(1)  NOT NULL DEFAULT '0' COMMENT '编辑类型，1:平台；2:用户',
    `editor_id`       int(11)     NOT NULL DEFAULT '0' COMMENT '用户id',
    `job_add_ids`     varchar(64) NOT NULL DEFAULT '' COMMENT '新增职位ids',
    PRIMARY KEY (`id`),
    KEY `idx_editor_id` (`editor_id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 14709
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告编辑';

-- ----------------------------
-- Table structure for announcement_education_relation
-- ----------------------------
DROP TABLE IF EXISTS `announcement_education_relation`;
CREATE TABLE `announcement_education_relation`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `education_code`  int(11)          NOT NULL DEFAULT '0' COMMENT '学历code',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`),
    KEY `idx_education_code` (`education_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 3437298
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位学历公告中间表';

-- ----------------------------
-- Table structure for announcement_extra
-- ----------------------------
DROP TABLE IF EXISTS `announcement_extra`;
CREATE TABLE `announcement_extra`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `company_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '单位ID',
    `is_pi`           tinyint(1)       NOT NULL DEFAULT '2' COMMENT '是否PI:1=是,2=不是',
    `is_pay`          tinyint(1)       NOT NULL DEFAULT '2' COMMENT '是否付费单位；1:包含；2:不包含',
    `is_boshihou_pay` tinyint(1)       NOT NULL DEFAULT '2' COMMENT '博士后广告是否付费单位；1:包含；2:不包含',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_is_boshihou_pay` (`is_boshihou_pay`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 389951
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告附属表';

-- ----------------------------
-- Table structure for announcement_handle_log
-- ----------------------------
DROP TABLE IF EXISTS `announcement_handle_log`;
CREATE TABLE `announcement_handle_log`
(
    `id`              int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`          tinyint(1)       NOT NULL DEFAULT '1' COMMENT '状态',
    `ip`              int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作ip',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告id',
    `handle_type`     varchar(32)      NOT NULL DEFAULT '0' COMMENT '操作类型，1:编辑；2：刷新；3:发布；4:再发布；5:下线；6:审核；7:隐藏；8:显示；9:删除',
    `editor_type`     tinyint(1)       NOT NULL DEFAULT '0' COMMENT '修改类型，1:仅修改公告；2：仅新增职位；3:仅修改职位；4:修改职位+新增职位；5:修改公告+修改职位；6:修改公告+新增职位；7:修改公告+修改职位+新增职位；9:其他',
    `handler_type`    int(11)          NOT NULL DEFAULT '0' COMMENT '用户类型，1:运营平台；2:普通用户',
    `handler_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '操作用户id',
    `handler_name`    varchar(256)     NOT NULL DEFAULT '' COMMENT '操作人名称',
    `handle_before`   mediumtext       NOT NULL COMMENT '操作前',
    `handle_after`    mediumtext       NOT NULL COMMENT '操作后',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_handle_type` (`handle_type`) USING BTREE,
    KEY `idx_editor_type` (`editor_type`) USING BTREE,
    KEY `idx_add_time` (`add_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 890338
  DEFAULT CHARSET = utf8mb4 COMMENT ='公告操作日志';

-- ----------------------------
-- Table structure for area
-- ----------------------------
DROP TABLE IF EXISTS `area`;
CREATE TABLE `area`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT,
    `add_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态',
    `parent_id`   int(11)     NOT NULL DEFAULT '0' COMMENT '父id',
    `name`        varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
    `region`      tinyint(1)  NOT NULL DEFAULT '0' COMMENT '大区',
    `spell`       varchar(32) NOT NULL DEFAULT '' COMMENT '拼音',
    `letter`      char(1)     NOT NULL DEFAULT '' COMMENT '拼音首字母',
    `code`        varchar(32) NOT NULL DEFAULT '' COMMENT '代码',
    `level`       tinyint(1)  NOT NULL DEFAULT '0' COMMENT '等级1省/2市/3区/4街道',
    `sort`        int(11)     NOT NULL DEFAULT '0' COMMENT '排序',
    `full_name`   varchar(64) NOT NULL DEFAULT '' COMMENT '全称',
    `full_id`     varchar(64) NOT NULL DEFAULT '' COMMENT '全id',
    `is_china`    tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否国内',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_spell` (`spell`) USING BTREE,
    KEY `idx_letter` (`letter`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 4000
  DEFAULT CHARSET = utf8mb4 COMMENT ='地区';

-- ----------------------------
-- Table structure for article
-- ----------------------------
DROP TABLE IF EXISTS `article`;
CREATE TABLE `article`
(
    `id`                  int(11)             NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`            datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `refresh_time`        datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '发布时间',
    `status`              tinyint(1)          NOT NULL DEFAULT '0' COMMENT '状态(2已下线，1在线，3草稿)',
    `type`                tinyint(1)          NOT NULL DEFAULT '0' COMMENT '1:公告.2:资讯.3.每日汇总',
    `is_show`             tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否显示，1显示，2隐藏',
    `is_delete`           tinyint(1) unsigned NOT NULL DEFAULT '2' COMMENT '软删除，2否，1是',
    `title`               varchar(256)        NOT NULL DEFAULT '' COMMENT '标题',
    `content`             mediumtext COMMENT '内容',
    `home_column_id`      int(11)             NOT NULL DEFAULT '0' COMMENT '所属栏目id',
    `home_sub_column_ids` varchar(150)        NOT NULL DEFAULT '' COMMENT '副栏目ids',
    `original_url`        varchar(150)        NOT NULL DEFAULT '' COMMENT '来源',
    `author`              varchar(64)         NOT NULL DEFAULT '' COMMENT '作者',
    `tag_ids`             varchar(255)        NOT NULL DEFAULT '' COMMENT '标签(,)',
    `link_url`            varchar(256)        NOT NULL DEFAULT '' COMMENT '网址',
    `cover_thumb`         varchar(256)        NOT NULL DEFAULT '' COMMENT '正文头图',
    `list_thumb`          varchar(64)         NOT NULL DEFAULT '' COMMENT '列表图片',
    `seo_description`     varchar(512)        NOT NULL DEFAULT '' COMMENT '公告摘要',
    `seo_keywords`        varchar(256)        NOT NULL DEFAULT '' COMMENT '关键字',
    `click`               int(11)             NOT NULL DEFAULT '0' COMMENT '点击次数',
    `sort`                int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '排序',
    `recommend_ids`       varchar(32)         NOT NULL DEFAULT '' COMMENT '推荐位ids',
    `release_time`        datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '改字段已停用',
    `original`            varchar(255)        NOT NULL DEFAULT '' COMMENT '来源',
    `apply_audit_time`    datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '申请审核时间',
    `first_release_time`  datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '首次发布时间',
    `delete_time`         datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `real_refresh_time`   datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '真实刷新时间',
    `refresh_date`        date                NOT NULL DEFAULT '0000-00-00' COMMENT '发布日期(格式Y-m-d)',
    PRIMARY KEY (`id`),
    KEY `idx_home_column_id` (`home_column_id`),
    KEY `idx_click` (`click`),
    KEY `idx_sort` (`sort`),
    KEY `idx_refresh_time` (`refresh_time`),
    KEY `idx_first_release_time` (`first_release_time`) USING BTREE,
    KEY `idx_status_delete_show` (`status`, `is_show`, `is_delete`) USING BTREE,
    KEY `idx_refresh_date` (`refresh_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 336412
  DEFAULT CHARSET = utf8mb4 COMMENT ='文章主表';

-- ----------------------------
-- Table structure for article_attribute
-- ----------------------------
DROP TABLE IF EXISTS `article_attribute`;
CREATE TABLE `article_attribute`
(
    `id`          int(11) unsigned    NOT NULL AUTO_INCREMENT,
    `type`        tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '文档属性类型',
    `article_id`  int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '文章id',
    `add_time`    datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '添加时间',
    `sort_time`   datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '排序时间',
    `expire_time` datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '过期时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_article_id_type` (`article_id`, `type`) USING BTREE,
    KEY `idx_type` (`type`) USING BTREE,
    KEY `idx_sort_time` (`sort_time`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 180235
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for article_click_log
-- ----------------------------
DROP TABLE IF EXISTS `article_click_log`;
CREATE TABLE `article_click_log`
(
    `id`           int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `member_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '点击的会员id',
    `article_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '文章的id',
    `source`       tinyint(1)       NOT NULL DEFAULT '1' COMMENT '1:pc,2:h5',
    `useragent`    varchar(2048)    NOT NULL DEFAULT '' COMMENT '请求表头信息',
    `user_cookies` varchar(64)      NOT NULL DEFAULT '' COMMENT '用户的cookie',
    `ip`           int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作ip',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_article_id` (`article_id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_member_id` (`member_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 147913864
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for article_column
-- ----------------------------
DROP TABLE IF EXISTS `article_column`;
CREATE TABLE `article_column`
(
    `id`         int(11) unsigned NOT NULL AUTO_INCREMENT,
    `column_id`  int(11) unsigned NOT NULL COMMENT '栏目id',
    `article_id` int(11) unsigned NOT NULL COMMENT '文章id',
    `add_time`   datetime         NOT NULL COMMENT '添加时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_column_id` (`column_id`) USING BTREE,
    KEY `idx_article_id` (`article_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 44354242
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for baidu_zz_push_log
-- ----------------------------
DROP TABLE IF EXISTS `baidu_zz_push_log`;
CREATE TABLE `baidu_zz_push_log`
(
    `id`            int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`      datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态1成功,2失败',
    `way_type`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '推送方式,1:脚本推送,2:手动推送',
    `url_type`      tinyint(1) NOT NULL DEFAULT '0' COMMENT 'url类型,1:pc,2:h5',
    `date`          date       NOT NULL DEFAULT '0000-00-00' COMMENT '归属日期',
    `send_count`    int(11)    NOT NULL DEFAULT '0' COMMENT '推送数量',
    `success_count` int(11)    NOT NULL DEFAULT '0' COMMENT '成功数量',
    `fail_count`    int(11)    NOT NULL DEFAULT '0' COMMENT '失败数量',
    `send_url`      mediumtext NOT NULL COMMENT '全部url,json格式',
    `fail_url`      mediumtext NOT NULL COMMENT '失败url,json格式',
    `return_log`    text       NOT NULL COMMENT '返回日志',
    `type`          tinyint(2) NOT NULL DEFAULT '1' COMMENT 'type=1百度推送 2=indexnow推送',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_date` (`date`),
    KEY `idx_type` (`type`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 74365
  DEFAULT CHARSET = utf8mb4 COMMENT ='百度站长推送日志';

-- ----------------------------
-- Table structure for buried_point_log
-- ----------------------------
DROP TABLE IF EXISTS `buried_point_log`;
CREATE TABLE `buried_point_log`
(
    `id`            int(10)          NOT NULL AUTO_INCREMENT,
    `add_time`      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `ip`            int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最近一次登录的ip',
    `user_cookies`  varchar(64)      NOT NULL DEFAULT '' COMMENT '用户的cookie',
    `action_type`   tinyint(4)       NOT NULL DEFAULT '0' COMMENT '（1、点击切换方式 2、点击提交 3、点击发送验证码 4、扫码 5、发送验证码数据接收 6、提交手机登录数据接收 7提交账号登录数据接收）\n',
    `action_url`    varchar(255)     NOT NULL DEFAULT '' COMMENT '操作所在链接',
    `params`        varchar(255)     NOT NULL DEFAULT '' COMMENT '参数',
    `action_module` tinyint(4)       NOT NULL DEFAULT '0' COMMENT '（1首页手机登录 2首页账号登录 3首页扫码登录 4注册页手机登录 5注册页账号登录 6注册页扫码登录 7登录页手机登录 8登录页账号登录 9登录页扫码登录 10弹窗手机登录 11弹窗账号登录 12弹窗扫码登录 13详情页手机登录 14页面底部手机登录）\n',
    `member_id`     int(11)          NOT NULL DEFAULT '0' COMMENT '用户id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2951423
  DEFAULT CHARSET = utf8mb4 COMMENT ='埋点操作记录';

-- ----------------------------
-- Table structure for category_job
-- ----------------------------
DROP TABLE IF EXISTS `category_job`;
CREATE TABLE `category_job`
(
    `id`        int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `name`      varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
    `sort`      int(11)     NOT NULL DEFAULT '0' COMMENT '排序',
    `parent_id` int(11)     NOT NULL DEFAULT '0' COMMENT '父id',
    `level`     tinyint(1)  NOT NULL DEFAULT '0' COMMENT '等级',
    `spell`     varchar(32) NOT NULL DEFAULT '' COMMENT '拼音',
    `is_delete` tinyint(1)  NOT NULL DEFAULT '2',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_spell` (`spell`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 278
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for certificate
-- ----------------------------
DROP TABLE IF EXISTS `certificate`;
CREATE TABLE `certificate`
(
    `id`          int(11)                           NOT NULL AUTO_INCREMENT,
    `add_time`    datetime                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `status`      tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '状态',
    `parent_id`   int(11)                           NOT NULL DEFAULT '0' COMMENT '父id',
    `name`        varchar(64) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '名称',
    `sort`        int(11)                           NOT NULL DEFAULT '0' COMMENT '排序',
    `level`       tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '等级',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 377
  DEFAULT CHARSET = utf8 COMMENT ='资质证书表';

-- ----------------------------
-- Table structure for chat_common_greeting
-- ----------------------------
DROP TABLE IF EXISTS `chat_common_greeting`;
CREATE TABLE `chat_common_greeting`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `content`     varchar(512)     NOT NULL DEFAULT '' COMMENT '内容',
    `member_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '所属member_id',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10083
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户聊天打招呼语';

-- ----------------------------
-- Table structure for chat_common_greeting_system
-- ----------------------------
DROP TABLE IF EXISTS `chat_common_greeting_system`;
CREATE TABLE `chat_common_greeting_system`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `content`     varchar(512)     NOT NULL DEFAULT '' COMMENT '内容',
    `type`        tinyint(2)       NOT NULL COMMENT '类型(跟随member表type含义):1个人/2企业',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 9
  DEFAULT CHARSET = utf8mb4 COMMENT ='系统聊天打招呼语';

-- ----------------------------
-- Table structure for chat_common_phrase
-- ----------------------------
DROP TABLE IF EXISTS `chat_common_phrase`;
CREATE TABLE `chat_common_phrase`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `content`     varchar(512) NOT NULL DEFAULT '' COMMENT '内容',
    `member_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '所属member_id',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 117513
  DEFAULT CHARSET = utf8mb4 COMMENT ='聊天常用语';

-- ----------------------------
-- Table structure for chat_history_job
-- ----------------------------
DROP TABLE IF EXISTS `chat_history_job`;
CREATE TABLE `chat_history_job`
(
    `id`                int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`          datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`            tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态',
    `chat_room_id`      int(11)    NOT NULL DEFAULT '0' COMMENT '聊天房间id',
    `job_id`            int(11)    NOT NULL DEFAULT '0' COMMENT '职位id',
    `type`              tinyint(4) NOT NULL DEFAULT '1' COMMENT '创建类型: 新增1/切换2',
    `creator_member_id` int(11)    NOT NULL DEFAULT '0' COMMENT '操作人id的member_id',
    `creator_type`      tinyint(4) NOT NULL DEFAULT '0' COMMENT '上传人的类型,1求职者,2单位',
    `is_apply`          tinyint(4) NOT NULL DEFAULT '2' COMMENT '求职者是否已投递过：1 - 已投递 2 - 未投递',
    `chat_message_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '消息id',
    PRIMARY KEY (`id`),
    KEY `idx_chat_room_id` (`chat_room_id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_chat_message_id` (`chat_message_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 87697
  DEFAULT CHARSET = utf8mb4 COMMENT ='历史聊天职位表';

-- ----------------------------
-- Table structure for chat_message
-- ----------------------------
DROP TABLE IF EXISTS `chat_message`;
CREATE TABLE `chat_message`
(
    `id`             int(11)     NOT NULL AUTO_INCREMENT,
    `add_time`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`         tinyint(4)  NOT NULL DEFAULT '0' COMMENT '消息状态(1发送成功,2已读,-1发送失败)',
    `type`           tinyint(4)  NOT NULL DEFAULT '0' COMMENT '消息类型',
    `main_id`        int(11)     NOT NULL DEFAULT '0' COMMENT '这里用于保存一些关联的时候,其他表的id,类似投递的id,邀约的id等等,主要是看type',
    `from_member_id` int(11)     NOT NULL DEFAULT '0' COMMENT '发送用户id',
    `to_member_id`   int(11)     NOT NULL DEFAULT '0' COMMENT '接收用户id',
    `content`        text        NOT NULL COMMENT '消息全部内容',
    `chat_room_id`   int(11)     NOT NULL DEFAULT '0' COMMENT '聊天房间id',
    `job_id`         int(11)     NOT NULL DEFAULT '0' COMMENT '职位id(这里有可能变的)',
    `is_read`        tinyint(4)  NOT NULL DEFAULT '2' COMMENT '是否已读，1已读，2未读',
    `read_time`      datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '读取时间',
    `show_time`      varchar(16) NOT NULL DEFAULT '' COMMENT '读取时间',
    `is_show_time`   tinyint(4)  NOT NULL DEFAULT '2' COMMENT '是否已读，1是，2否',
    PRIMARY KEY (`id`),
    KEY `idx_chat_room_id` (`chat_room_id`),
    KEY `idx_from_member_id` (`from_member_id`),
    KEY `idx_to_member_id` (`to_member_id`),
    KEY `idx_add_time` (`add_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 215524
  DEFAULT CHARSET = utf8mb4 COMMENT ='聊天记录表';

-- ----------------------------
-- Table structure for chat_message_card
-- ----------------------------
DROP TABLE IF EXISTS `chat_message_card`;
CREATE TABLE `chat_message_card`
(
    `id`               int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`         datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`           tinyint(4) NOT NULL DEFAULT '0' COMMENT '卡片状态',
    `type`             tinyint(4) NOT NULL DEFAULT '0' COMMENT '卡片类型',
    `chat_room_id`     int(11)    NOT NULL DEFAULT '0' COMMENT '聊天房间id',
    `chat_message_id`  int(11)    NOT NULL DEFAULT '0' COMMENT '聊天消息id',
    `operation_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '操作状态(0未操作,其他根据不同卡片写入不同类型)',
    PRIMARY KEY (`id`),
    KEY `idx_chat_room_id` (`chat_room_id`),
    KEY `idx_chat_message_id` (`chat_message_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 94475
  DEFAULT CHARSET = utf8mb4 COMMENT ='聊天消息卡片表';

-- ----------------------------
-- Table structure for chat_message_file
-- ----------------------------
DROP TABLE IF EXISTS `chat_message_file`;
CREATE TABLE `chat_message_file`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`          tinyint(4)   NOT NULL DEFAULT '0' COMMENT '消息状态',
    `file_id`         int(11)      NOT NULL DEFAULT '0' COMMENT '文件id',
    `chat_room_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '聊天房间id',
    `chat_message_id` int(11)      NOT NULL DEFAULT '0' COMMENT '聊天消息id',
    `file_url`        varchar(256) NOT NULL DEFAULT '' COMMENT '文件路径,在oss的路径',
    `file_name`       varchar(256) NOT NULL DEFAULT '' COMMENT '文件名称',
    `download_amount` int(11)      NOT NULL DEFAULT '0' COMMENT '简历下载次数',
    PRIMARY KEY (`id`),
    KEY `idx_chat_room_id` (`chat_room_id`),
    KEY `idx_chat_message_id` (`chat_message_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 265
  DEFAULT CHARSET = utf8mb4 COMMENT ='聊天消息文件表';

-- ----------------------------
-- Table structure for chat_room
-- ----------------------------
DROP TABLE IF EXISTS `chat_room`;
CREATE TABLE `chat_room`
(
    `id`                int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`            tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `uuid`              varchar(256) NOT NULL DEFAULT '' COMMENT '会话id(全局唯一)',
    `resume_member_id`  int(11)      NOT NULL DEFAULT '0' COMMENT '求职者member_id',
    `resume_id`         int(11)      NOT NULL DEFAULT '0' COMMENT '求职者id',
    `company_member_id` int(11)      NOT NULL DEFAULT '0' COMMENT '单位子账号member_id',
    `company_id`        int(11)      NOT NULL DEFAULT '0' COMMENT '单位id',
    `creator_type`      tinyint(4)   NOT NULL DEFAULT '1' COMMENT '创建人类型1求职者，2单位',
    `last_talk_time`    datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '最近一次沟通的时间',
    `talk_progress`     tinyint(4)   NOT NULL DEFAULT '1' COMMENT '沟通进度（1单向，2双向）',
    `talk_job_amount`   int(11)      NOT NULL DEFAULT '0' COMMENT '总沟通职位数量',
    `current_job_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '当前沟通职位id',
    `is_agree_file`     tinyint(4)   NOT NULL DEFAULT '2' COMMENT '是否同意发送文件1是,2否',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uuid` (`uuid`),
    KEY `idx_resume_member_id` (`resume_member_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_company_member_id` (`company_member_id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_current_job_id` (`current_job_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 79793
  DEFAULT CHARSET = utf8mb4 COMMENT ='直聊聊天室';

-- ----------------------------
-- Table structure for chat_room_session
-- ----------------------------
DROP TABLE IF EXISTS `chat_room_session`;
CREATE TABLE `chat_room_session`
(
    `id`             int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`       datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `chat_room_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '房间id',
    `member_id`      int(11)    NOT NULL DEFAULT '0' COMMENT 'memberId',
    `is_top`         tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否置顶1是,2否',
    `is_delete`      tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否删除1是,2否',
    `last_talk_time` datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '最新时间',
    `unread_amount`  int(11)    NOT NULL DEFAULT '0' COMMENT '未读消息总量(对方未读)',
    PRIMARY KEY (`id`),
    KEY `idx_chat_room_id` (`chat_room_id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_last_talk_time` (`last_talk_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 159681
  DEFAULT CHARSET = utf8mb4 COMMENT ='直聊聊天室会话';

-- ----------------------------
-- Table structure for company
-- ----------------------------
DROP TABLE IF EXISTS `company`;
CREATE TABLE `company`
(
    `id`                     int(11)             NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`               datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`            datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`                 tinyint(1)          NOT NULL DEFAULT '0' COMMENT '状态，审核状态(9未审核,1审核通过,0审核信息未提交,7初审通过,-8初审拒绝,8等待复审,-9复审拒绝)',
    `member_id`              int(11)             NOT NULL DEFAULT '0' COMMENT '会员id',
    `full_name`              varchar(256)        NOT NULL DEFAULT '' COMMENT '名字',
    `short_name`             varchar(128)        NOT NULL DEFAULT '' COMMENT '简称',
    `english_name`           varchar(256)        NOT NULL DEFAULT '' COMMENT '英文名字',
    `type`                   tinyint(1)          NOT NULL DEFAULT '0' COMMENT '单位类型',
    `nature`                 tinyint(1)          NOT NULL DEFAULT '0' COMMENT '单位性质(民营等等)',
    `scale`                  tinyint(1)          NOT NULL DEFAULT '0' COMMENT '单位规模',
    `industry_id`            int(11)             NOT NULL DEFAULT '0' COMMENT '所属行业id',
    `area_id`                int(11)             NOT NULL DEFAULT '0' COMMENT '所属地区id',
    `welfare_label_ids`      varchar(256)        NOT NULL DEFAULT '' COMMENT '单位福利id,逗号隔开',
    `website`                varchar(512)        NOT NULL DEFAULT '' COMMENT '单位官网',
    `address`                varchar(256)        NOT NULL DEFAULT '' COMMENT '所在地址',
    `contact`                varchar(32)         NOT NULL DEFAULT '' COMMENT '联系人',
    `department`             varchar(32)         NOT NULL DEFAULT '' COMMENT '所在部门',
    `telephone`              varchar(32)         NOT NULL DEFAULT '' COMMENT '固定电话',
    `fax`                    varchar(32)         NOT NULL DEFAULT '' COMMENT '传真',
    `introduce`              text COMMENT '单位介绍',
    `logo_url`               varchar(256)        NOT NULL DEFAULT '' COMMENT 'logo地址',
    `is_cooperation`         tinyint(1) unsigned NOT NULL DEFAULT '2' COMMENT '2非合作单位，1已合作单位',
    `source_type`            tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '1自主申请，2运营添加',
    `province_id`            int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '省',
    `city_id`                int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '市',
    `district_id`            int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '区',
    `is_pay`                 tinyint(1)          NOT NULL DEFAULT '0' COMMENT '付费情况;0免费单位，1付费单位',
    `admin_id`               int(10) unsigned    NOT NULL DEFAULT '0' COMMENT '业务员id',
    `head_banner_url`        varchar(256)        NOT NULL DEFAULT '' COMMENT '企业头部banner图',
    `label_ids`              varchar(256)        NOT NULL DEFAULT '' COMMENT '单位标签id（逗号隔开,字典表type=26获取）',
    `create_admin_id`        int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '创建人id（自主创建或者运营创建）',
    `job_last_release_time`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '单位下最后操作职位的发布时间（用于排序）',
    `package_type`           tinyint(1)          NOT NULL DEFAULT '1' COMMENT '企业套餐类型（不用于排序，排序试用sort字段）（1:免费会员  2:高级会员  3过期会员 4 试用会员）',
    `style_atlas`            varchar(255)        NOT NULL DEFAULT '' COMMENT '企业风采图集',
    `click`                  int(11)             NOT NULL DEFAULT '0' COMMENT '点击量',
    `is_hide`                tinyint(1)          NOT NULL DEFAULT '2' COMMENT '是否隐藏（1隐藏   2展示）',
    `sort`                   int(11)             NOT NULL DEFAULT '0' COMMENT '单位排序（有一套计分体系，具体参考代码）',
    `delivery_type`          tinyint(1)          NOT NULL DEFAULT '1' COMMENT '投递类型1=站外投递,2=站内投递,3=站内&站外投递',
    `account_nature`         tinyint(1)          NOT NULL DEFAULT '1' COMMENT '账号性质 0不详 1愿开通账号 2不愿开通账号',
    `mobile_head_banner_url` varchar(256)        NOT NULL DEFAULT '' COMMENT '移动端企业头部图片',
    `is_miniapp`             tinyint(2)          NOT NULL DEFAULT '2' COMMENT '是否被小程序调用1调用 2没调用',
    `is_manual_tag`          tinyint(2)          NOT NULL DEFAULT '0' COMMENT '是否运营手动标记 0没标记 1标记 2不标记',
    `uuid`                   varchar(64)         NOT NULL DEFAULT '' COMMENT 'uuid',
    `admin_hide_status`      tinyint(1)                   DEFAULT '0' COMMENT '管理员设置的隐藏状态0管理员未设置，1管理员设置隐藏，2管理员设置显示',
    `group_score_system_id`  int(11)             NOT NULL DEFAULT '0' COMMENT '分值系统ID',
    `is_abroad`              tinyint(1)          NOT NULL DEFAULT '0' COMMENT '是否海外栏目1是2否',
    `overseas_sort_point`    int(11)             NOT NULL DEFAULT '0' COMMENT '海外排名积分',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_industry_id` (`industry_id`),
    KEY `idx_area_id` (`area_id`),
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_job_last_release_time` (`job_last_release_time`) USING BTREE,
    KEY `idx_admin_id` (`admin_id`) USING BTREE,
    KEY `idx_is_hide` (`is_hide`),
    KEY `idx_sort` (`sort`),
    KEY `idx_city_id` (`city_id`) USING BTREE,
    KEY `idx_is_cooperation` (`is_cooperation`),
    KEY `idx_uuid` (`uuid`),
    KEY `idx_type` (`type`),
    KEY `idx_nature` (`nature`),
    KEY `idx_scale` (`scale`),
    KEY `idx_group_score_system_id` (`group_score_system_id`),
    KEY `idx_is_abroad` (`is_abroad`),
    KEY `idx_overseas_sort_point` (`overseas_sort_point`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 122110
  DEFAULT CHARSET = utf8mb4 COMMENT ='企业';

-- ----------------------------
-- Table structure for company_admin_change_log
-- ----------------------------
DROP TABLE IF EXISTS `company_admin_change_log`;
CREATE TABLE `company_admin_change_log`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `member_id`       int(11)      NOT NULL DEFAULT '0' COMMENT '用户id',
    `member_name`     varchar(512) NOT NULL DEFAULT '' COMMENT '用户名称',
    `company_id`      int(11)      NOT NULL DEFAULT '0' COMMENT '公司id',
    `company_name`    varchar(128) NOT NULL DEFAULT '' COMMENT '公司名称',
    `handler_type`    tinyint(1)   NOT NULL DEFAULT '0' COMMENT '操作人类型，1:平台；2:用户',
    `handle_before`   varchar(150) NOT NULL DEFAULT '' COMMENT '操作前',
    `handle_after`    varchar(150) NOT NULL COMMENT '操作后',
    `admin_id_before` int(11)      NOT NULL DEFAULT '0' COMMENT '旧业务员id',
    `admin_id_now`    int(11)      NOT NULL DEFAULT '0' COMMENT '当前业务员id',
    `handler`         varchar(32)  NOT NULL DEFAULT '' COMMENT '操作人',
    `handler_id`      int(11)      NOT NULL DEFAULT '0' COMMENT '操作人id',
    `remark`          varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_admin_id_before` (`admin_id_before`) USING BTREE,
    KEY `idx_admin_id_now` (`admin_id_now`) USING BTREE,
    KEY `idx_handler_id` (`handler_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 709
  DEFAULT CHARSET = utf8mb4 COMMENT ='企业业务员变更记录';

-- ----------------------------
-- Table structure for company_child_unit
-- ----------------------------
DROP TABLE IF EXISTS `company_child_unit`;
CREATE TABLE `company_child_unit`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT,
    `add_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态',
    `member_id`   int(11)     NOT NULL DEFAULT '0' COMMENT '会员id',
    `company_id`  int(11)     NOT NULL DEFAULT '0' COMMENT '企业id',
    `name`        varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
    `contact`     varchar(32) NOT NULL DEFAULT '' COMMENT '联系人',
    `telephone`   varchar(32) NOT NULL DEFAULT '' COMMENT '固定电话',
    `fax`         varchar(32) NOT NULL DEFAULT '' COMMENT '传真',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 43
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for company_click_log
-- ----------------------------
DROP TABLE IF EXISTS `company_click_log`;
CREATE TABLE `company_click_log`
(
    `id`           int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `member_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '点击的会员id',
    `company_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '单位的id',
    `source`       tinyint(1)       NOT NULL DEFAULT '1' COMMENT '1:pc,2:h5;3mini',
    `useragent`    varchar(2048)    NOT NULL DEFAULT '' COMMENT '请求表头信息',
    `user_cookies` varchar(64)      NOT NULL DEFAULT '' COMMENT '用户的cookie',
    `ip`           int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作ip',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 19690319
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for company_collect
-- ----------------------------
DROP TABLE IF EXISTS `company_collect`;
CREATE TABLE `company_collect`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `member_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '会员id',
    `company_id`  int(11)    NOT NULL DEFAULT '0' COMMENT '单位id',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_update_time` (`update_time`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 155036
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位收藏表';

-- ----------------------------
-- Table structure for company_contact
-- ----------------------------
DROP TABLE IF EXISTS `company_contact`;
CREATE TABLE `company_contact`
(
    `id`               int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`           tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `member_id`        int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `company_id`       int(11)      NOT NULL DEFAULT '0' COMMENT '企业id',
    `name`             varchar(32)  NOT NULL DEFAULT '' COMMENT '联系人名称',
    `department`       varchar(64)  NOT NULL DEFAULT '' COMMENT '所在部门',
    `name_is_public`   tinyint(1)   NOT NULL DEFAULT '0' COMMENT '联系人是否公开',
    `mobile_is_public` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '联系电话是否公开',
    `email_is_public`  tinyint(1)   NOT NULL DEFAULT '0' COMMENT '邮箱是否公开',
    `mobile`           varchar(16)  NOT NULL DEFAULT '' COMMENT '手机号',
    `telephone`        varchar(32)  NOT NULL DEFAULT '' COMMENT '电话',
    `email`            varchar(256) NOT NULL DEFAULT '' COMMENT '邮箱',
    `weixin`           varchar(32)  NOT NULL DEFAULT '' COMMENT '微信',
    `qq`               varchar(32)  NOT NULL DEFAULT '' COMMENT 'qq',
    `fax`              varchar(32)  NOT NULL DEFAULT '' COMMENT '传真',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 112091
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for company_cooperation_apply
-- ----------------------------
DROP TABLE IF EXISTS `company_cooperation_apply`;
CREATE TABLE `company_cooperation_apply`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `type`        tinyint(1)   NOT NULL DEFAULT '0' COMMENT '类型,1:发布招聘,2推广宣传',
    `is_handle`   tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否已处理,1:已处理,0未处理',
    `name`        varchar(128) NOT NULL DEFAULT '' COMMENT '单位名称',
    `contact`     varchar(32)  NOT NULL DEFAULT '' COMMENT '联系人',
    `department`  varchar(32)  NOT NULL DEFAULT '' COMMENT '所属部门',
    `mobile`      varchar(32)  NOT NULL DEFAULT '' COMMENT '联系电话',
    `job_name`    varchar(32)  NOT NULL DEFAULT '' COMMENT '招聘岗位名称',
    `education`   varchar(32)  NOT NULL DEFAULT '' COMMENT '学历要求',
    `recruitment` varchar(32)  NOT NULL DEFAULT '' COMMENT '招聘时长',
    `tx_im`       varchar(32)  NOT NULL DEFAULT '' COMMENT '腾讯的im工具',
    `email`       varchar(256) NOT NULL DEFAULT '' COMMENT '联系邮箱',
    `content`     text COMMENT '其他说明',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 997
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位合作申请';

-- ----------------------------
-- Table structure for company_cooperation_apply_handle
-- ----------------------------
DROP TABLE IF EXISTS `company_cooperation_apply_handle`;
CREATE TABLE `company_cooperation_apply_handle`
(
    `id`                           int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`                     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `type`                         tinyint(1)   NOT NULL DEFAULT '0' COMMENT '类型,1:设置已处理,2设置为未处理,3添加标记',
    `admin_id`                     int(11)      NOT NULL DEFAULT '0' COMMENT '处理人的id',
    `content`                      varchar(512) NOT NULL DEFAULT '' COMMENT '处理的内容',
    `company_cooperation_apply_id` int(11)      NOT NULL DEFAULT '0' COMMENT '申请的id',
    PRIMARY KEY (`id`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_company_cooperation_apply_id` (`company_cooperation_apply_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2622
  DEFAULT CHARSET = utf8mb4 COMMENT ='合作申请处理';

-- ----------------------------
-- Table structure for company_delivery_change_log
-- ----------------------------
DROP TABLE IF EXISTS `company_delivery_change_log`;
CREATE TABLE `company_delivery_change_log`
(
    `id`                 int(11) unsigned NOT NULL AUTO_INCREMENT,
    `company_id`         int(11)          NOT NULL DEFAULT '0' COMMENT '单位ID',
    `company_name`       varchar(255)     NOT NULL DEFAULT '' COMMENT '单位名称',
    `description_before` tinyint(1)       NOT NULL COMMENT '修改前',
    `description_after`  tinyint(1)       NOT NULL COMMENT '修改后',
    `add_time`           datetime         NOT NULL COMMENT '添加时间',
    `update_tme`         datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark`             varchar(255)     NOT NULL DEFAULT '' COMMENT '备注',
    `user_type`          tinyint(1)       NOT NULL DEFAULT '0' COMMENT '操作来源 1运营后台 2单位端',
    `user_id`            int(11)          NOT NULL DEFAULT '0' COMMENT '操作人ID',
    `user_name`          varchar(100)     NOT NULL DEFAULT '' COMMENT '操作人名称',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2480
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位投递类型变更日志表';

-- ----------------------------
-- Table structure for company_feature_tag
-- ----------------------------
DROP TABLE IF EXISTS `company_feature_tag`;
CREATE TABLE `company_feature_tag`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
    `update_time` datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `tag`         varchar(128)     NOT NULL DEFAULT '' COMMENT '特色标签内容',
    `admin_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '添加人id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位特色标签字典表';

-- ----------------------------
-- Table structure for company_feature_tag_relation
-- ----------------------------
DROP TABLE IF EXISTS `company_feature_tag_relation`;
CREATE TABLE `company_feature_tag_relation`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT,
    `company_id`     int(11)          NOT NULL COMMENT '单位企业id',
    `feature_tag_id` int(11)          NOT NULL COMMENT '单位特色标签id',
    PRIMARY KEY (`id`),
    KEY `idx_company_id_feature_tag_id` (`company_id`, `feature_tag_id`),
    KEY `idx_feature_tag_id` (`feature_tag_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 436
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位特色标签关系表';

-- ----------------------------
-- Table structure for company_group
-- ----------------------------
DROP TABLE IF EXISTS `company_group`;
CREATE TABLE `company_group`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT,
    `group_name`     varchar(255)     NOT NULL DEFAULT '' COMMENT '群组名称',
    `sort_number`    int(11)          NOT NULL DEFAULT '0' COMMENT '排序',
    `weight`         decimal(20, 2)   NOT NULL DEFAULT '0.00' COMMENT '权重分值',
    `company_number` int(11)          NOT NULL DEFAULT '0' COMMENT '单位所属数量',
    `admin_id`       int(11)          NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `description`    varchar(255)     NOT NULL DEFAULT '' COMMENT '群组说明',
    `is_delete`      tinyint(2)       NOT NULL DEFAULT '2' COMMENT '是否删除（软删除） 1是 2否',
    `add_time`       datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_admin_id` (`admin_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 20
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位群组';

-- ----------------------------
-- Table structure for company_group_relation
-- ----------------------------
DROP TABLE IF EXISTS `company_group_relation`;
CREATE TABLE `company_group_relation`
(
    `id`         int(11) unsigned NOT NULL AUTO_INCREMENT,
    `company_id` int(11)          NOT NULL DEFAULT '0' COMMENT '单位ID',
    `group_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '群组ID',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_group_id` (`group_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 134986
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位群组中间表';

-- ----------------------------
-- Table structure for company_group_score_system
-- ----------------------------
DROP TABLE IF EXISTS `company_group_score_system`;
CREATE TABLE `company_group_score_system`
(
    `id`        int(11) unsigned NOT NULL AUTO_INCREMENT,
    `group_ids` varchar(255)     NOT NULL DEFAULT '' COMMENT '分组（一定要正序放入）',
    `score`     decimal(20, 2)   NOT NULL DEFAULT '0.00' COMMENT '分值',
    PRIMARY KEY (`id`),
    KEY `idx_group_ids` (`group_ids`) USING BTREE,
    KEY `idx_score` (`score`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 156
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位群组分值系统';

-- ----------------------------
-- Table structure for company_info_auth
-- ----------------------------
DROP TABLE IF EXISTS `company_info_auth`;
CREATE TABLE `company_info_auth`
(
    `id`                int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`          datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `company_id`        int(11)          NOT NULL DEFAULT '0' COMMENT '企业id',
    `phase`             tinyint(1)       NOT NULL DEFAULT '0' COMMENT '阶段',
    `audit_status`      tinyint(1)       NOT NULL DEFAULT '0' COMMENT '审核状态(9未审核,1审核通过,0审核信息未提交,7初审通过,-8初审拒绝,8等待复审,-9复审拒绝)',
    `reason`            varchar(256)     NOT NULL DEFAULT '' COMMENT '审核不通过的原因',
    `full_name`         varchar(128)     NOT NULL DEFAULT '' COMMENT '单位名称',
    `type`              tinyint(1)       NOT NULL DEFAULT '0' COMMENT '单位类型',
    `nature`            tinyint(1)       NOT NULL DEFAULT '0' COMMENT '单位性质',
    `industry_id`       tinyint(1)       NOT NULL DEFAULT '0' COMMENT '所属行业id',
    `area_id`           int(11)          NOT NULL DEFAULT '0' COMMENT '地区id',
    `address`           varchar(128)     NOT NULL DEFAULT '' COMMENT '详细地址',
    `contact`           varchar(32)      NOT NULL DEFAULT '' COMMENT '联系人',
    `mobile`            varchar(32)      NOT NULL DEFAULT '' COMMENT '联系电话',
    `email`             varchar(256)     NOT NULL DEFAULT '' COMMENT '联系邮箱',
    `department`        varchar(32)      NOT NULL DEFAULT '' COMMENT '所在部门',
    `telephone`         varchar(32)               DEFAULT '' COMMENT '固定电话',
    `license_path`      varchar(128)     NOT NULL DEFAULT '' COMMENT '单位资质认证',
    `person_info_path`  varchar(128)     NOT NULL DEFAULT '' COMMENT '身份证路径',
    `member_id`         int(11)          NOT NULL DEFAULT '0' COMMENT '会员id',
    `province_id`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '省',
    `city_id`           int(11)          NOT NULL COMMENT '市',
    `district_id`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '区',
    `submit_audit_time` datetime                  DEFAULT NULL COMMENT '提交审核时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 13350
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for company_info_auth_log
-- ----------------------------
DROP TABLE IF EXISTS `company_info_auth_log`;
CREATE TABLE `company_info_auth_log`
(
    `id`           int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `company_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '企业id',
    `phase`        tinyint(1)       NOT NULL DEFAULT '0' COMMENT '阶段',
    `audit_status` tinyint(1)       NOT NULL DEFAULT '0' COMMENT '审核状态(9未审核,1审核通过,-1审核拒绝)',
    `reason`       varchar(256)     NOT NULL DEFAULT '' COMMENT '审核不通过的原因',
    `admin_id`     int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作人ID',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 4818
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位审核记录表';

-- ----------------------------
-- Table structure for company_interview
-- ----------------------------
DROP TABLE IF EXISTS `company_interview`;
CREATE TABLE `company_interview`
(
    `id`              int(11)             NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`          tinyint(1)          NOT NULL DEFAULT '0' COMMENT '状态',
    `job_apply_id`    int(11)             NOT NULL DEFAULT '0' COMMENT '职位投递id',
    `job_name`        varchar(90)         NOT NULL DEFAULT '' COMMENT '职位名称(快照职位名称)',
    `contact`         varchar(30)         NOT NULL DEFAULT '' COMMENT '联系人',
    `telephone`       varchar(20)         NOT NULL DEFAULT '' COMMENT '联系电话',
    `address`         varchar(200)        NOT NULL DEFAULT '' COMMENT '联系地址',
    `interview_time`  datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT ' 面试时间 ',
    `email_content`   varchar(100)        NOT NULL DEFAULT '' COMMENT '邮箱通知内容',
    `message_content` varchar(100)        NOT NULL DEFAULT '' COMMENT '短信通知内容',
    `is_look`         tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否查看',
    `content`         varchar(200)        NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_job_apply_id` (`job_apply_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 765
  DEFAULT CHARSET = utf8mb4 COMMENT ='面试信息';

-- ----------------------------
-- Table structure for company_invite_system_config
-- ----------------------------
DROP TABLE IF EXISTS `company_invite_system_config`;
CREATE TABLE `company_invite_system_config`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT,
    `company_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '单位ID',
    `invite_number` int(6)           NOT NULL DEFAULT '0' COMMENT '邀请数量',
    `type`          tinyint(1)       NOT NULL DEFAULT '1' COMMENT '类型 1天 2周 3月 4年',
    `admin_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '运营平台操作人',
    `add_time`      datetime         NOT NULL COMMENT '添加时间',
    `update_time`   datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`     tinyint(1)       NOT NULL DEFAULT '2' COMMENT '是否删除 1删除 2正常',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_admin_id` (`admin_id`) USING BTREE,
    KEY `idx_type` (`type`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 120
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位邀约次数系统配置表';

-- ----------------------------
-- Table structure for company_member_config
-- ----------------------------
DROP TABLE IF EXISTS `company_member_config`;
CREATE TABLE `company_member_config`
(
    `id`            int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `company_id`    int(11)  NOT NULL DEFAULT '0' COMMENT '单位ID',
    `vip_total`     int(4)   NOT NULL DEFAULT '0' COMMENT '子账号vip总数量',
    `vip_available` int(4)   NOT NULL DEFAULT '0' COMMENT '子账号vip可用数量',
    `vip_used`      int(4)   NOT NULL DEFAULT '0' COMMENT '子账号vip已用数量',
    `total`         int(4)   NOT NULL DEFAULT '0' COMMENT '子账号的总数量',
    `available`     int(4)   NOT NULL DEFAULT '0' COMMENT '子账号的可用数量',
    `used`          int(4)   NOT NULL DEFAULT '0' COMMENT '子账号的已用数量',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 6497
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位子账号配置表';

-- ----------------------------
-- Table structure for company_member_info
-- ----------------------------
DROP TABLE IF EXISTS `company_member_info`;
CREATE TABLE `company_member_info`
(
    `id`                     int(11)             NOT NULL AUTO_INCREMENT,
    `add_time`               datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`            datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `member_id`              int(11)             NOT NULL DEFAULT '0' COMMENT '账号ID',
    `company_id`             int(11)             NOT NULL DEFAULT '0' COMMENT '单位id(隶属单位ID)',
    `contact`                varchar(64)         NOT NULL DEFAULT '' COMMENT '联系人',
    `department`             varchar(128)        NOT NULL DEFAULT '' COMMENT '所在部门',
    `member_rule`            tinyint(1)          NOT NULL COMMENT '账号权限  1普通权限 2VIP权限 9超管权限',
    `company_member_type`    tinyint(1)          NOT NULL COMMENT '账号类型 0主账号 1子账号',
    `create_id`              int(11)             NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `source_type`            int(11)             NOT NULL DEFAULT '2' COMMENT '创建来源 1自主 2运营',
    `is_wx_bind`             tinyint(2)          NOT NULL DEFAULT '0' COMMENT '是否绑定微信0未绑定 1绑定',
    `is_remember_sms_chat`   tinyint(1) unsigned NOT NULL DEFAULT '2' COMMENT '是否记住直聊短信发送',
    `is_remember_sms_invite` tinyint(1) unsigned NOT NULL DEFAULT '2' COMMENT '是否记住投递短信发送',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_member_id` (`member_id`),
    KEY `idx_create_id` (`create_id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_company_member_type` (`company_member_type`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 13462
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位账户信息表';

-- ----------------------------
-- Table structure for company_member_message_config
-- ----------------------------
DROP TABLE IF EXISTS `company_member_message_config`;
CREATE TABLE `company_member_message_config`
(
    `id`               int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`         datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    `update_time`      datetime         NOT NULL COMMENT '更新时间',
    `member_id`        int(11)          NOT NULL DEFAULT '0' COMMENT '账号ID',
    `company_id`       int(11)          NOT NULL DEFAULT '0' COMMENT '单位ID(隶属单位)',
    `delivery_message` tinyint(2)       NOT NULL DEFAULT '1' COMMENT '简历投递消息提醒开关 0关  1开',
    `chat_message`     tinyint(2)       NOT NULL DEFAULT '1' COMMENT '直聊消息提醒开关 0关  1开',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_member_id` (`member_id`),
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 3612
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位消息配置表';

-- ----------------------------
-- Table structure for company_member_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `company_member_operation_log`;
CREATE TABLE `company_member_operation_log`
(
    `id`                int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`          datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `type`              tinyint(4) NOT NULL DEFAULT '0' COMMENT '类型 1单位账号状态操作 2子账号配置  3服务过期 4手动变更账号权限 5创建子账号  6单位终审通过',
    `operation_id`      int(11)    NOT NULL DEFAULT '0' COMMENT '操作人',
    `operation_port`    tinyint(4) NOT NULL DEFAULT '0' COMMENT '操作端口 1系统 2运营 3单位自己',
    `operation_content` text       NOT NULL COMMENT '操作内容',
    `member_id`         int(11)    NOT NULL DEFAULT '0' COMMENT '用户ID',
    `company_id`        int(11)    NOT NULL DEFAULT '0' COMMENT '隶属单位ID',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1767
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位操作日志';

-- ----------------------------
-- Table structure for company_member_wx_bind
-- ----------------------------
DROP TABLE IF EXISTS `company_member_wx_bind`;
CREATE TABLE `company_member_wx_bind`
(
    `id`                int(11)       NOT NULL AUTO_INCREMENT,
    `add_time`          datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`            tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `company_id`        int(11)       NOT NULL DEFAULT '0' COMMENT '单位id',
    `openid`            varchar(64)   NOT NULL DEFAULT '' COMMENT 'openid',
    `unionid`           varchar(64)   NOT NULL DEFAULT '' COMMENT 'unionid',
    `is_subscribe`      tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否关注1是 2否',
    `company_member_id` int(11)       NOT NULL DEFAULT '0' COMMENT '账号id',
    `wx_name`           varchar(100)  NOT NULL DEFAULT '' COMMENT '微信名称',
    `wx_avatar`         varchar(2550) NOT NULL DEFAULT '' COMMENT '微信头像',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_openid` (`openid`) USING BTREE,
    KEY `idx_company_id` (`company_id`),
    KEY `idx_unionid` (`unionid`),
    KEY `idx_ company_member_id` (`company_member_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 10950
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for company_member_wx_bind_callback_log
-- ----------------------------
DROP TABLE IF EXISTS `company_member_wx_bind_callback_log`;
CREATE TABLE `company_member_wx_bind_callback_log`
(
    `id`       int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `add_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `openid`   varchar(64)   NOT NULL DEFAULT '' COMMENT 'openid',
    `data`     varchar(2048) NOT NULL DEFAULT '' COMMENT '实际的内容，使用json保存',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_openid` (`openid`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 126902
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for company_package_change_detail_log
-- ----------------------------
DROP TABLE IF EXISTS `company_package_change_detail_log`;
CREATE TABLE `company_package_change_detail_log`
(
    `id`            int(11)             NOT NULL AUTO_INCREMENT,
    `type`          tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '1：人才投递邀约；2:人才直聊通知；3:人才直聊框提示通知',
    `resume_id`     int(11)             NOT NULL DEFAULT '0' COMMENT '求职者id',
    `member_id`     int(11)             NOT NULL DEFAULT '0' COMMENT '操作人id',
    `company_id`    int(11)             NOT NULL DEFAULT '0' COMMENT '单位id',
    `job_id`        int(11)             NOT NULL DEFAULT '0' COMMENT '职位id',
    `resource_id`   int(11)             NOT NULL DEFAULT '0' COMMENT 'type为1，2，3时是短信日志id',
    `change_log_id` int(11)             NOT NULL DEFAULT '0' COMMENT 'company_package_change_log表id',
    `add_time`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_type_company_id` (`type`, `company_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 674
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位权益消耗明细（只记录消耗）';

-- ----------------------------
-- Table structure for company_package_change_log
-- ----------------------------
DROP TABLE IF EXISTS `company_package_change_log`;
CREATE TABLE `company_package_change_log`
(
    `id`              int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `type`            tinyint(2)    NOT NULL DEFAULT '0' COMMENT '权益类型，1:职位发布；2:职位刷新；3:公告发布；4:公告刷新；5:开通服务(系统赠送)；6:系统配置；7:公告审核拒绝；8:职位审核拒绝；9:人才库下载；10:子账号总量；11:VIP权限授权总量；',
    `identify`        tinyint(1)    NOT NULL DEFAULT '0' COMMENT '权益标识，1、增加；2:减少',
    `change_amount`   int(2)        NOT NULL DEFAULT '0' COMMENT '更改数量',
    `surplus`         int(2)        NOT NULL DEFAULT '0' COMMENT '剩余数量',
    `package_surplus` varchar(1024) NOT NULL DEFAULT '' COMMENT '套餐剩余',
    `member_id`       int(11)       NOT NULL DEFAULT '0' COMMENT '用户id',
    `member_name`     varchar(100)  NOT NULL DEFAULT '' COMMENT '用户名称',
    `company_id`      int(11)       NOT NULL DEFAULT '0' COMMENT '公司id',
    `company_name`    varchar(128)  NOT NULL DEFAULT '' COMMENT '公司名称',
    `handle_before`   varchar(100)  NOT NULL DEFAULT '' COMMENT '变更前',
    `handle_after`    varchar(100)  NOT NULL DEFAULT '' COMMENT '变更后',
    `handler_type`    tinyint(1)    NOT NULL DEFAULT '0' COMMENT '操作人类型，1:平台；2:用户',
    `handler`         varchar(256)  NOT NULL DEFAULT '' COMMENT '操作人',
    `handler_id`      int(11)       NOT NULL DEFAULT '0' COMMENT '操作人id',
    `content`         varchar(512)  NOT NULL DEFAULT '' COMMENT '操作记录，详情',
    `remark`          varchar(3000) NOT NULL DEFAULT '' COMMENT '备注',
    `handle_type`     int(3)        NOT NULL DEFAULT '0' COMMENT '操作类型',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_handler_id` (`handler_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 137352
  DEFAULT CHARSET = utf8mb4 COMMENT ='套餐变更记录';

-- ----------------------------
-- Table structure for company_package_config
-- ----------------------------
DROP TABLE IF EXISTS `company_package_config`;
CREATE TABLE `company_package_config`
(
    `id`                                int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`                            tinyint(1)   NOT NULL DEFAULT '1' COMMENT '状态',
    `name`                              varchar(256) NOT NULL DEFAULT '' COMMENT '名称',
    `code`                              varchar(128) NOT NULL DEFAULT '' COMMENT '代码',
    `job_amount`                        int(11)      NOT NULL DEFAULT '0' COMMENT '职位发布条数',
    `announcement_amount`               int(11)      NOT NULL DEFAULT '0' COMMENT '公告发布条数',
    `job_refresh_amount`                int(11)      NOT NULL DEFAULT '0' COMMENT '职位刷新次数',
    `announcement_refresh_amount`       int(11)      NOT NULL DEFAULT '0' COMMENT '公告刷新次数',
    `job_refresh_interval_day`          int(11)      NOT NULL DEFAULT '0' COMMENT '职位刷新间隔时间',
    `announcement_refresh_interval_day` int(11)      NOT NULL DEFAULT '0' COMMENT '公告刷新间隔时间',
    `announcement_release_interval_day` int(11)      NOT NULL DEFAULT '0' COMMENT '公告发布间隔时间',
    `job_release_interval_day`          int(11)      NOT NULL DEFAULT '0' COMMENT '职位发布间隔时间',
    `member_id`                         int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `company_id`                        int(11)      NOT NULL DEFAULT '0' COMMENT '企业id',
    `package_amount`                    tinyint(11)  NOT NULL DEFAULT '0' COMMENT '权益包数',
    `expire_time`                       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效期',
    `effect_time`                       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
    `package_id`                        int(11)      NOT NULL DEFAULT '0' COMMENT '套餐id',
    `resume_download_amount`            int(11)      NOT NULL DEFAULT '0' COMMENT '简历下载点数',
    `sms_amount`                        int(11)      NOT NULL DEFAULT '0' COMMENT '短信数量',
    `chat_amount`                       int(11)      NOT NULL DEFAULT '0' COMMENT '直聊沟通点数',
    `cycle_resume_download_amount`      int(11)      NOT NULL DEFAULT '0' COMMENT '当前周期简历下载点数',
    `cycle_chat_amount`                 int(11)      NOT NULL DEFAULT '0' COMMENT '当前周期直聊沟通点数',
    `cycle_sms_amount`                  int(11)      NOT NULL DEFAULT '0' COMMENT '短信数量（总量）',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_package_id` (`package_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3331
  DEFAULT CHARSET = utf8mb4 COMMENT ='企业套餐';

-- ----------------------------
-- Table structure for company_package_config_log
-- ----------------------------
DROP TABLE IF EXISTS `company_package_config_log`;
CREATE TABLE `company_package_config_log`
(
    `id`                                int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `name`                              varchar(128) NOT NULL DEFAULT '' COMMENT '名称',
    `code`                              varchar(32)  NOT NULL DEFAULT '' COMMENT '代码',
    `job_amount`                        int(11)      NOT NULL DEFAULT '0' COMMENT '职位发布条数',
    `announcement_amount`               int(11)      NOT NULL DEFAULT '0' COMMENT '公告发布条数',
    `job_refresh_amount`                int(11)      NOT NULL DEFAULT '0' COMMENT '职位刷新次数',
    `announcement_refresh_amount`       int(11)      NOT NULL DEFAULT '0' COMMENT '公告刷新次数',
    `job_refresh_interval_day`          int(11)      NOT NULL DEFAULT '0' COMMENT '职位刷新间隔时间',
    `announcement_refresh_interval_day` int(11)      NOT NULL DEFAULT '0' COMMENT '公告刷新间隔时间',
    `announcement_release_interval_day` int(11)      NOT NULL DEFAULT '0' COMMENT '公告发布间隔时间',
    `job_release_interval_day`          int(11)      NOT NULL DEFAULT '0' COMMENT '职位发布间隔时间',
    `effect_time`                       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
    `expire_time`                       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '到期时间',
    `package_amount`                    int(11)      NOT NULL DEFAULT '0' COMMENT '权益包数',
    `price`                             varchar(32)  NOT NULL DEFAULT '' COMMENT '开通价格',
    `package_id`                        int(11)      NOT NULL DEFAULT '0' COMMENT '套餐id',
    `member_id`                         int(11)      NOT NULL DEFAULT '0' COMMENT '用户ID',
    `company_id`                        int(11)      NOT NULL DEFAULT '0' COMMENT '企业ID',
    `company_name`                      varchar(128) NOT NULL DEFAULT '' COMMENT '企业名称',
    `handle_before`                     varchar(128) NOT NULL DEFAULT '' COMMENT '变更前',
    `handle_after`                      varchar(128) NOT NULL DEFAULT '' COMMENT '变更后',
    `handler`                           varchar(128) NOT NULL DEFAULT '' COMMENT '操作人',
    `handler_id`                        int(11)      NOT NULL DEFAULT '0' COMMENT '操作人ID',
    `remark`                            varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
    `resume_download_amount`            int(11)      NOT NULL DEFAULT '0' COMMENT '简历下载点数',
    `chat_amount`                       int(11)      NOT NULL DEFAULT '0' COMMENT '直聊沟通点数',
    `sms_amount`                        int(11)      NOT NULL DEFAULT '0' COMMENT '短信条数',
    PRIMARY KEY (`id`),
    KEY `idx_package_id` (`package_id`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_handler_id` (`handler_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 6177
  DEFAULT CHARSET = utf8mb4 COMMENT ='企业套餐开通记录';

-- ----------------------------
-- Table structure for company_package_system_config
-- ----------------------------
DROP TABLE IF EXISTS `company_package_system_config`;
CREATE TABLE `company_package_system_config`
(
    `id`                               int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`                           tinyint(1)   NOT NULL DEFAULT '1' COMMENT '状态，1:在用，2:禁用',
    `name`                             varchar(128) NOT NULL DEFAULT '' COMMENT '名称',
    `code`                             varchar(128) NOT NULL DEFAULT '' COMMENT '代码',
    `job_amount`                       int(11)      NOT NULL DEFAULT '0' COMMENT '职位发布条数',
    `announcement_amount`              int(11)      NOT NULL DEFAULT '0' COMMENT '公告发布条数',
    `job_refresh_amount`               int(11)      NOT NULL DEFAULT '0' COMMENT '职位可刷新总量',
    `announcement_refresh_amount`      int(11)      NOT NULL DEFAULT '0' COMMENT '公告可刷新总量',
    `resume_download_amount`           int(11)      NOT NULL DEFAULT '0' COMMENT '简历下载点数',
    `sms_amount`                       int(11)      NOT NULL DEFAULT '0' COMMENT '短信数量',
    `base_job_amount`                  int(3)       NOT NULL DEFAULT '0' COMMENT '职位发布基础数量',
    `base_announcement_amount`         int(3)       NOT NULL DEFAULT '0' COMMENT '公告发布基础数量',
    `base_job_refresh_amount`          int(3)       NOT NULL DEFAULT '0' COMMENT '职位刷新基础数量',
    `base_announcement_refresh_amount` int(3)       NOT NULL DEFAULT '0' COMMENT '公告刷新基础数量',
    `base_resume_download_amount`      int(3)       NOT NULL DEFAULT '0' COMMENT '简历下载基础点数',
    `chat_amount`                      int(11)      NOT NULL DEFAULT '0' COMMENT '直聊沟通点数',
    `base_chat_amount`                 int(11)      NOT NULL DEFAULT '0' COMMENT '直聊沟通基础点数',
    `base_sms_amount`                  int(11)      NOT NULL DEFAULT '0' COMMENT '短信基础条数',
    `type`                             tinyint(3)   NOT NULL DEFAULT '1' COMMENT '套餐类型；1:高级会员；2:免费会员；3：试用会员',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 6
  DEFAULT CHARSET = utf8mb4 COMMENT ='企业套餐配置';

-- ----------------------------
-- Table structure for company_resume_library
-- ----------------------------
DROP TABLE IF EXISTS `company_resume_library`;
CREATE TABLE `company_resume_library`
(
    `id`                int(11)       NOT NULL AUTO_INCREMENT,
    `add_time`          datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `company_id`        int(11)       NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`         int(11)       NOT NULL DEFAULT '0' COMMENT '简历id',
    `source_type`       int(11)       NOT NULL DEFAULT '0' COMMENT '简历来源(这里采取位来存,1是投递,2是下载,3是两者均有)',
    `download_time`     datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '下载时间',
    `apply_time`        datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '应聘时间',
    `tag`               varchar(1024) NOT NULL DEFAULT '' COMMENT '企业对简历对标注',
    `company_member_id` int(11)       NOT NULL DEFAULT '0' COMMENT '单位用户id',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_add_time` (`add_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1086501
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位简历库';

-- ----------------------------
-- Table structure for company_resume_pv_total
-- ----------------------------
DROP TABLE IF EXISTS `company_resume_pv_total`;
CREATE TABLE `company_resume_pv_total`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `resume_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '简历ID',
    `add_date`    date             NOT NULL COMMENT '统计时间',
    `update_time` datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `total`       int(11)          NOT NULL DEFAULT '0' COMMENT 'pv次数',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_add_date` (`add_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 8346607
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位对求职者pv统计';

-- ----------------------------
-- Table structure for company_stat_data
-- ----------------------------
DROP TABLE IF EXISTS `company_stat_data`;
CREATE TABLE `company_stat_data`
(
    `id`                        int(11)       NOT NULL AUTO_INCREMENT,
    `add_time`                  datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`               datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `company_id`                int(11)       NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_view_rate`          decimal(5, 2) NOT NULL DEFAULT '-1.00' COMMENT '简历查看率,其实就是简历投递量/查看量',
    `heat`                      int(11)       NOT NULL DEFAULT '0' COMMENT '热度, 最近3个月人才投递次数*65%+单位主页累计收藏量*5%+当前在线职位数*30%',
    `is_haiwai_column`          tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否是海外栏目；1：是；2:不是',
    `haiwai_sort_point`         int(11)       NOT NULL DEFAULT '0' COMMENT '海外排名积分',
    `is_boshihou_column`        tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否是博士后栏目；1：是；2:不是',
    `boshihou_sort_point`       int(11)       NOT NULL DEFAULT '0' COMMENT '博士后排名积分',
    `online_announcement_count` int(11)       NOT NULL DEFAULT '0' COMMENT '在线公告数量',
    `online_job_count`          int(11)       NOT NULL DEFAULT '0' COMMENT '在线职位数量',
    `is_pi`                     tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否是PI团队；1:是；2:不是',
    `all_announcement_count`    int(11)       NOT NULL DEFAULT '0' COMMENT '公告数量（包含上线和下线）',
    `all_job_count`             int(11)       NOT NULL DEFAULT '0' COMMENT '职位数量（包含上线和下线）',
    `is_pay`                    tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否付费单位；1:包含；2:不包含',
    `is_boshihou_pay`           tinyint(1)    NOT NULL DEFAULT '2' COMMENT '博士后广告是否付费单位；1:包含；2:不包含',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_resume_view_rate` (`resume_view_rate`) USING BTREE,
    KEY `idx_heat` (`heat`),
    KEY `idx_is_boshihou_pay` (`is_boshihou_pay`) USING BTREE,
    KEY `idx_is_boshihou_column` (`is_boshihou_column`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 122435
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位数据统计表';

-- ----------------------------
-- Table structure for company_stat_data_log
-- ----------------------------
DROP TABLE IF EXISTS `company_stat_data_log`;
CREATE TABLE `company_stat_data_log`
(
    `id`               int(11)       NOT NULL AUTO_INCREMENT,
    `add_time`         datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `date`             date          NOT NULL DEFAULT '0000-00-00' COMMENT '所属日期',
    `company_id`       int(11)       NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_view_rate` decimal(5, 2) NOT NULL DEFAULT '-1.00' COMMENT '简历查看率,其实就是简历投递量/查看量',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_date` (`date`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位数据统计表日志表(考虑每天去跑一次)';

-- ----------------------------
-- Table structure for company_view_resume
-- ----------------------------
DROP TABLE IF EXISTS `company_view_resume`;
CREATE TABLE `company_view_resume`
(
    `id`             int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`       datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`      int(11)    NOT NULL DEFAULT '0' COMMENT '简历id',
    `company_id`     int(11)    NOT NULL DEFAULT '0' COMMENT '单位id',
    `count`          int(11)    NOT NULL COMMENT '查看次数',
    `is_resume_view` tinyint(1) NOT NULL DEFAULT '2' COMMENT '求职者是否已经查看了,1是2否',
    `last_time`      datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '最后查看时间',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_last_time` (`last_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 158761
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位查看简历统计表';

-- ----------------------------
-- Table structure for company_view_resume_log
-- ----------------------------
DROP TABLE IF EXISTS `company_view_resume_log`;
CREATE TABLE `company_view_resume_log`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '简历id',
    `company_id`  int(11)    NOT NULL DEFAULT '0' COMMENT '单位id',
    `type`        tinyint(1) NOT NULL COMMENT '类型',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 204605
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位查看简历日志';

-- ----------------------------
-- Table structure for company_welfare_label_relationship
-- ----------------------------
DROP TABLE IF EXISTS `company_welfare_label_relationship`;
CREATE TABLE `company_welfare_label_relationship`
(
    `id`               int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`         datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `member_id`        int(11)    NOT NULL DEFAULT '0' COMMENT '用户Id',
    `welfare_label_id` int(11)    NOT NULL DEFAULT '0' COMMENT '福利标签Id',
    `company_id`       int(11)    NOT NULL DEFAULT '0' COMMENT '企业Id',
    `is_delete`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_welfare_label_id` (`welfare_label_id`),
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1353
  DEFAULT CHARSET = utf8mb4 COMMENT ='企业福利标签关系表';

-- ----------------------------
-- Table structure for controller_access_log
-- ----------------------------
DROP TABLE IF EXISTS `controller_access_log`;
CREATE TABLE `controller_access_log`
(
    `id`         int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`   datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `controller` varchar(64)      NOT NULL DEFAULT '' COMMENT '控制器',
    `ip`         varchar(15)      NOT NULL DEFAULT '' COMMENT 'IP',
    `ua`         varchar(1024)    NOT NULL DEFAULT '' COMMENT '用户ua',
    `user_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '用户id',
    `params`     varchar(2048)    NOT NULL DEFAULT '' COMMENT '请求参数josn',
    `duration`   decimal(10, 2)   NOT NULL DEFAULT '0.00' COMMENT '请求耗时',
    `platform`   varchar(256)     NOT NULL DEFAULT '' COMMENT '平台',
    `city`       varchar(256)     NOT NULL DEFAULT '' COMMENT '城市',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_controller` (`controller`) USING BTREE,
    KEY `idx_ip` (`ip`) USING BTREE,
    KEY `idx_user_id` (`user_id`) USING BTREE,
    KEY `idx_duration` (`duration`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 69782989
  DEFAULT CHARSET = utf8mb4 COMMENT ='控制器访问日志';

-- ----------------------------
-- Table structure for daily_announcement_summary
-- ----------------------------
DROP TABLE IF EXISTS `daily_announcement_summary`;
CREATE TABLE `daily_announcement_summary`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0:未审核,1:通过)',
    `article_id`  int(11)    NOT NULL DEFAULT '0' COMMENT '文章id',
    `belong_date` date       NOT NULL DEFAULT '0000-00-00' COMMENT '所属日期',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_article_id` (`article_id`) USING BTREE,
    KEY `idx_belong_date` (`belong_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1222
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for dictionary
-- ----------------------------
DROP TABLE IF EXISTS `dictionary`;
CREATE TABLE `dictionary`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT,
    `add_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态',
    `code`        int(11)     NOT NULL DEFAULT '0' COMMENT '编号',
    `name`        varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
    `type`        tinyint(4)  NOT NULL DEFAULT '0' COMMENT '类型名称 （1：学历水平 2：意向职能，3：工作性质 4：求职状态 5：到岗时间 6：期望月薪 7：项目类别 8：所属行业 9：资质证书 10：技能语言\n 11：技能掌握程度 12：附件信息主题：13 职称  14：政治面貌  15：工作经验  16：是否海外经历  17：年龄要求  18：是否985/211  19：学历要求\n20:单位性质  21:岗位类型  22:单位规模  23:职位发布时间范围  24:工资范围  25:单位类型  26:单位标签  27:报名方式  28:论文位次  29:民族  30:专利位次 31:编制）',
    `parent_id`   int(11)     NOT NULL DEFAULT '0' COMMENT '父id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_type` (`type`) USING BTREE,
    KEY `idx_code` (`code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 510
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='数据字典表';

-- ----------------------------
-- Table structure for email_log
-- ----------------------------
DROP TABLE IF EXISTS `email_log`;
CREATE TABLE `email_log`
(
    `id`       int(11)       NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`   tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态1成功,0失败',
    `email`    varchar(256)  NOT NULL DEFAULT '' COMMENT '邮箱',
    `content`  varchar(1024) NOT NULL DEFAULT '' COMMENT '发送内容',
    `type`     tinyint(1)    NOT NULL DEFAULT '0' COMMENT '类型, 1注册 2修改密码  3修改邮箱  4绑定邮箱 5站外投递',
    `reason`   varchar(256)  NOT NULL DEFAULT '' COMMENT '发送失败原因',
    `is_read`  tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否已读 1已读 2未读',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_email` (`email`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 4995304
  DEFAULT CHARSET = utf8mb4 COMMENT ='邮件发送记录表';

-- ----------------------------
-- Table structure for engine_search_rule
-- ----------------------------
DROP TABLE IF EXISTS `engine_search_rule`;
CREATE TABLE `engine_search_rule`
(
    `id`              int(10) unsigned NOT NULL AUTO_INCREMENT,
    `description`     varchar(255)     NOT NULL DEFAULT '' COMMENT '规则描述',
    `rule_level_1`    varchar(128)     NOT NULL DEFAULT '' COMMENT '规则1级',
    `rule_level_2`    varchar(128)     NOT NULL DEFAULT '' COMMENT '规则2级',
    `add_time`        datetime         NOT NULL COMMENT '添加时间',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`       varchar(255)     NOT NULL DEFAULT '2' COMMENT '是否删除 1是 2正常',
    `seo_title`       varchar(255)     NOT NULL DEFAULT '' COMMENT '当前规则头文本标题配置',
    `seo_keywords`    varchar(255)     NOT NULL DEFAULT '' COMMENT '当前规则头文本keywords本配置',
    `seo_description` varchar(255)     NOT NULL DEFAULT '' COMMENT '当前规则头文本描述本配置',
    `is_match`        tinyint(1)       NOT NULL DEFAULT '1' COMMENT '不在规则二中是否匹配 1匹配 2不匹配',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_level` (`rule_level_1`, `rule_level_2`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 20
  DEFAULT CHARSET = utf8mb4 COMMENT ='搜索引擎的筛选规则';

-- ----------------------------
-- Table structure for engine_search_source
-- ----------------------------
DROP TABLE IF EXISTS `engine_search_source`;
CREATE TABLE `engine_search_source`
(
    `id`            int(10) unsigned NOT NULL AUTO_INCREMENT,
    `type_id`       int(10)          NOT NULL DEFAULT '0' COMMENT '规则ID 1工作地点 2单位类型 3学科分类  4学历要求  5职位类型',
    `level_id_1`    int(10)          NOT NULL DEFAULT '0' COMMENT '一级对应其他配置表ID（其他值得是类型配置的对应表）',
    `level_name_1`  varchar(128)     NOT NULL DEFAULT '' COMMENT '一级名称（这个名称可能与雷影配置对应表的名称有差别，但是ID所指是含义是一致）',
    `level_spell_1` varchar(128)     NOT NULL DEFAULT '' COMMENT '一级拼音名称',
    `level_id_2`    int(10)          NOT NULL DEFAULT '0' COMMENT '二级对应其他配置表ID（其他值得是类型配置的对应表）',
    `level_name_2`  varchar(128)     NOT NULL DEFAULT '' COMMENT '二级名称（这个名称可能与雷影配置对应表的名称有差别，但是ID所指是含义是一致）',
    `level_spell_2` varchar(128)     NOT NULL DEFAULT '' COMMENT '二级拼音名称',
    `add_time`      datetime         NOT NULL COMMENT '添加时间',
    `update_time`   datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`     varchar(255)     NOT NULL DEFAULT '2' COMMENT '是否删除 1是 2正常',
    `is_hot`        tinyint(1)       NOT NULL DEFAULT '0' COMMENT '是否热门 1是热门 0不是热门',
    `sort_num_1`    int(10)          NOT NULL DEFAULT '0' COMMENT '一级排序',
    `level_tag`     tinyint(1)       NOT NULL COMMENT '等级标识  暂时应用在类型上',
    `sort_num_2`    int(10)          NOT NULL DEFAULT '0' COMMENT '二级排序',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_type_id` (`type_id`),
    KEY `idx_level_id_1` (`level_id_1`),
    KEY `idx_level_id_2` (`level_id_2`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 497
  DEFAULT CHARSET = utf8mb4 COMMENT ='搜索引擎资源配置表';

-- ----------------------------
-- Table structure for engine_search_source_rule
-- ----------------------------
DROP TABLE IF EXISTS `engine_search_source_rule`;
CREATE TABLE `engine_search_source_rule`
(
    `id`            int(10) unsigned NOT NULL AUTO_INCREMENT,
    `source_type_1` tinyint(1)       NOT NULL DEFAULT '0' COMMENT '资源类型ID',
    `source_id_1`   int(10)          NOT NULL DEFAULT '0' COMMENT '资源ID',
    `source_type_2` tinyint(1)       NOT NULL DEFAULT '0' COMMENT '资源类型ID',
    `source_id_2`   int(10)          NOT NULL DEFAULT '0' COMMENT '资源ID',
    `add_time`      datetime         NOT NULL COMMENT '添加时间',
    `update_time`   datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`     tinyint(1)       NOT NULL DEFAULT '2' COMMENT '状态 2正常  1删除',
    `type`          tinyint(1)       NOT NULL DEFAULT '1' COMMENT '类型 1允许匹配  2禁止匹配',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_ source_id_1` (`source_id_1`) USING BTREE,
    KEY `idx_source_id_2` (`source_id_2`) USING BTREE,
    KEY `idx_source_type_1` (`source_type_1`),
    KEY `idx_source_type_2` (`source_type_2`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 904
  DEFAULT CHARSET = utf8mb4 COMMENT ='搜索引擎-职位中心规则二配置表';

-- ----------------------------
-- Table structure for engine_search_statistics
-- ----------------------------
DROP TABLE IF EXISTS `engine_search_statistics`;
CREATE TABLE `engine_search_statistics`
(
    `id`               int(10) unsigned NOT NULL AUTO_INCREMENT,
    `rule_id`          int(10)          NOT NULL DEFAULT '0' COMMENT '规则ID',
    `route_url`        varchar(512)              DEFAULT NULL,
    `level_content_1`  varchar(128)     NOT NULL DEFAULT '' COMMENT '参数1',
    `level_content_2`  varchar(128)     NOT NULL DEFAULT '' COMMENT '参数2',
    `add_time`         datetime         NOT NULL COMMENT '添加时间',
    `update_time`      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `number`           int(10)          NOT NULL DEFAULT '0' COMMENT '访问次数',
    `level_content_tj` varchar(255)     NOT NULL DEFAULT '' COMMENT '推荐参数',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_route_url` (`route_url`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='搜索引擎搜索统计表';

-- ----------------------------
-- Table structure for engine_search_type
-- ----------------------------
DROP TABLE IF EXISTS `engine_search_type`;
CREATE TABLE `engine_search_type`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `name`        varchar(100)     NOT NULL DEFAULT '' COMMENT '筛选名称',
    `sort_num`    int(100)         NOT NULL COMMENT '排序',
    `add_time`    datetime         NOT NULL COMMENT '添加时间',
    `update_time` datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`   varchar(255)     NOT NULL DEFAULT '2' COMMENT '是否删除 1是 2正常',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 6
  DEFAULT CHARSET = utf8mb4 COMMENT ='搜索引擎筛选项配置表';

-- ----------------------------
-- Table structure for file
-- ----------------------------
DROP TABLE IF EXISTS `file`;
CREATE TABLE `file`
(
    `id`           int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`       tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态1正常,0删除',
    `platform`     tinyint(1)   NOT NULL DEFAULT '0' COMMENT '平台(1本地，2七牛）',
    `name`         varchar(128) NOT NULL DEFAULT '' COMMENT '文件名（上传的时候的名字）',
    `path`         varchar(256) NOT NULL DEFAULT '' COMMENT '路径，上传的路径（平台的url+这里的路径才是最终访问的)',
    `size`         bigint(20)   NOT NULL DEFAULT '0' COMMENT '大小/b为单位',
    `suffix`       varchar(32)  NOT NULL DEFAULT '' COMMENT '后缀',
    `width`        int(11)      NOT NULL DEFAULT '0' COMMENT '图片宽度-px',
    `height`       int(11)      NOT NULL DEFAULT '0' COMMENT '图片高度-px',
    `creator_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '上传人的id',
    `creator_type` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '上传人的类型,1普通用户,2管理员',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1713543
  DEFAULT CHARSET = utf8mb4 COMMENT ='文件';

-- ----------------------------
-- Table structure for friend_link_config
-- ----------------------------
DROP TABLE IF EXISTS `friend_link_config`;
CREATE TABLE `friend_link_config`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `title`       varchar(128) NOT NULL DEFAULT '' COMMENT '链接标题',
    `link_url`    varchar(255) NOT NULL DEFAULT '' COMMENT '链接',
    `status`      tinyint(2)   NOT NULL DEFAULT '1' COMMENT '状态  1显示 2隐藏 3删除',
    `add_time`    datetime     NOT NULL COMMENT '添加时间',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `sort_number` int(5)       NOT NULL DEFAULT '0' COMMENT '排序大靠前',
    `is_nofollow` tinyint(2)   NOT NULL DEFAULT '2' COMMENT '是否添加nofollow：1添加 2不添加',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 41
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for guess_like_announcement
-- ----------------------------
DROP TABLE IF EXISTS `guess_like_announcement`;
CREATE TABLE `guess_like_announcement`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`        datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
    `ip`              varchar(15)      NOT NULL DEFAULT '' COMMENT 'IP',
    `resume_id`       int(11)          NOT NULL DEFAULT '0' COMMENT '用户id',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告id',
    PRIMARY KEY (`id`),
    KEY `idx_time_resume_ip` (`add_time`, `resume_id`, `ip`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 416015
  DEFAULT CHARSET = utf8mb4 COMMENT ='猜你喜欢公告';

-- ----------------------------
-- Table structure for gxjob_arctype
-- ----------------------------
DROP TABLE IF EXISTS `gxjob_arctype`;
CREATE TABLE `gxjob_arctype`
(
    `id`          smallint(5) unsigned NOT NULL AUTO_INCREMENT,
    `reid`        smallint(5) unsigned NOT NULL DEFAULT '0',
    `topid`       smallint(5) unsigned NOT NULL DEFAULT '0',
    `sortrank`    smallint(5) unsigned NOT NULL DEFAULT '50',
    `typename`    char(30)             NOT NULL DEFAULT '',
    `typedir`     char(60)             NOT NULL DEFAULT '',
    `isdefault`   smallint(6)          NOT NULL DEFAULT '0',
    `defaultname` char(15)             NOT NULL DEFAULT 'index.html',
    `issend`      smallint(6)          NOT NULL DEFAULT '0',
    `channeltype` smallint(6)                   DEFAULT '1',
    `maxpage`     smallint(6)          NOT NULL DEFAULT '-1',
    `ispart`      smallint(6)          NOT NULL DEFAULT '0',
    `corank`      smallint(6)          NOT NULL DEFAULT '0',
    `tempindex`   char(50)             NOT NULL DEFAULT '',
    `templist`    char(50)             NOT NULL DEFAULT '',
    `temparticle` char(50)             NOT NULL DEFAULT '',
    `namerule`    char(50)             NOT NULL DEFAULT '',
    `namerule2`   char(50)             NOT NULL DEFAULT '',
    `modname`     char(20)             NOT NULL DEFAULT '',
    `description` char(150)            NOT NULL DEFAULT '',
    `keywords`    varchar(60)          NOT NULL DEFAULT '',
    `seotitle`    varchar(80)          NOT NULL DEFAULT '',
    `moresite`    tinyint(1) unsigned  NOT NULL DEFAULT '0',
    `sitepath`    char(60)             NOT NULL DEFAULT '',
    `siteurl`     char(50)             NOT NULL DEFAULT '',
    `ishidden`    smallint(6)          NOT NULL DEFAULT '0',
    `cross`       tinyint(1)           NOT NULL DEFAULT '0',
    `crossid`     text,
    `content`     text,
    `smalltypes`  text,
    PRIMARY KEY (`id`),
    KEY `reid` (`reid`, `isdefault`, `channeltype`, `ispart`, `corank`, `topid`, `ishidden`),
    KEY `sortrank` (`sortrank`)
) ENGINE = MyISAM
  AUTO_INCREMENT = 627
  DEFAULT CHARSET = gbk;

-- ----------------------------
-- Table structure for home_column
-- ----------------------------
DROP TABLE IF EXISTS `home_column`;
CREATE TABLE `home_column`
(
    `id`                   int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`               tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `name`                 varchar(50)  NOT NULL DEFAULT '' COMMENT '栏目名称',
    `parent_id`            int(11)      NOT NULL DEFAULT '0' COMMENT '父id',
    `level`                tinyint(1)   NOT NULL DEFAULT '0' COMMENT '等级',
    `sort`                 int(11)      NOT NULL DEFAULT '0' COMMENT '排序',
    `code`                 varchar(50)  NOT NULL DEFAULT '' COMMENT '唯一代码',
    `content_type`         tinyint(1)   NOT NULL DEFAULT '0' COMMENT '内容类型,1招聘公告,2热点咨询',
    `template_type`        tinyint(1)   NOT NULL DEFAULT '0' COMMENT '栏目模模板,1模板A,2模板B',
    `detail_type`          tinyint(1)   NOT NULL DEFAULT '0' COMMENT '详情模块,1模板A,2模板B',
    `is_hide`              tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否显示,1显示2不显示',
    `path`                 varchar(256) NOT NULL DEFAULT '' COMMENT '旧系统路径',
    `seo_keywords`         varchar(256) NOT NULL DEFAULT '' COMMENT '关键字',
    `seo_description`      varchar(512) NOT NULL DEFAULT '' COMMENT '公告摘要',
    `content`              varchar(512) NOT NULL DEFAULT '' COMMENT '描述',
    `last_update_admin_id` int(11)      NOT NULL DEFAULT '0' COMMENT '上次更新人id',
    `creator_id`           int(11)      NOT NULL DEFAULT '0' COMMENT '创建人id',
    `seo_title`            varchar(256) NOT NULL DEFAULT '' COMMENT 'seo_title',
    `click`                int(11)      NOT NULL DEFAULT '0' COMMENT '点击量',
    `operate_attribute`    tinyint(1)   NOT NULL DEFAULT '0' COMMENT '运营属性，0:无;1:地区一级；2:地区二级',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_creator_id` (`creator_id`),
    KEY `idx_sort` (`sort`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 548
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='栏目表';

-- ----------------------------
-- Table structure for home_column_dictionary_relationship
-- ----------------------------
DROP TABLE IF EXISTS `home_column_dictionary_relationship`;
CREATE TABLE `home_column_dictionary_relationship`
(
    `id`             int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`       datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '日期时间',
    `status`         tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态,是否在用，1:是；2:否',
    `home_column_id` int(11)    NOT NULL DEFAULT '0' COMMENT '栏目主键id',
    `type`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '字典类型0其他,1地区,2学科',
    `main_id`        int(11)    NOT NULL DEFAULT '0' COMMENT '字典的主键id',
    PRIMARY KEY (`id`),
    KEY `idx_main_id` (`main_id`) USING BTREE,
    KEY `idx_main_id_type` (`main_id`, `type`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 515
  DEFAULT CHARSET = utf8mb4 COMMENT ='栏目字典属性关联表';

-- ----------------------------
-- Table structure for home_position
-- ----------------------------
DROP TABLE IF EXISTS `home_position`;
CREATE TABLE `home_position`
(
    `id`            int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`        tinyint(1)   NOT NULL DEFAULT '2' COMMENT '状态，1:显示；2:隐藏；9:删除',
    `platform_type` tinyint(4)   NOT NULL DEFAULT '0' COMMENT '所属平台',
    `number`        varchar(256) NOT NULL DEFAULT '0' COMMENT '位置编号',
    `name`          varchar(256) NOT NULL DEFAULT '' COMMENT '位置名称',
    `chinese_name`  varchar(256) NOT NULL DEFAULT '' COMMENT '中文名称',
    `width`         varchar(32)  NOT NULL DEFAULT '0' COMMENT '版位宽',
    `height`        varchar(32)  NOT NULL DEFAULT '0' COMMENT '版位高',
    `creator_type`  tinyint(1)   NOT NULL DEFAULT '1' COMMENT '创建人类型，1:平台，2:其他',
    `creator`       varchar(255) NOT NULL COMMENT '创建人',
    `creator_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '创建人id',
    `sort`          int(6)       NOT NULL DEFAULT '1' COMMENT '广告位排序',
    `describe`      varchar(500) NOT NULL DEFAULT '' COMMENT '广告位描述',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_number` (`number`) USING BTREE,
    KEY `idx_creator_id` (`creator_id`) USING BTREE,
    KEY `idx_sort` (`sort`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2432
  DEFAULT CHARSET = utf8mb4 COMMENT ='广告位';

-- ----------------------------
-- Table structure for home_position_tag
-- ----------------------------
DROP TABLE IF EXISTS `home_position_tag`;
CREATE TABLE `home_position_tag`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
    `update_time` datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `name`        varchar(128)     NOT NULL DEFAULT '' COMMENT '标签内容',
    `admin_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '添加人id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 9
  DEFAULT CHARSET = utf8mb4 COMMENT ='广告标签字典表';

-- ----------------------------
-- Table structure for home_position_tag_relation
-- ----------------------------
DROP TABLE IF EXISTS `home_position_tag_relation`;
CREATE TABLE `home_position_tag_relation`
(
    `id`               int(11) unsigned NOT NULL AUTO_INCREMENT,
    `home_position_id` int(11)          NOT NULL COMMENT '广告位id',
    `tag_id`           int(11)          NOT NULL COMMENT '标签id',
    PRIMARY KEY (`id`),
    KEY `idx_home_position_id_tag_id` (`tag_id`, `home_position_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 165
  DEFAULT CHARSET = utf8mb4 COMMENT ='广告位标签关系表';

-- ----------------------------
-- Table structure for hw_activity
-- ----------------------------
DROP TABLE IF EXISTS `hw_activity`;
CREATE TABLE `hw_activity`
(
    `id`                               int(10)       NOT NULL AUTO_INCREMENT,
    `add_time`                         datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                      datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                           tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态(1正常，2删除）',
    `name`                             varchar(255)  NOT NULL DEFAULT '' COMMENT '活动名称',
    `series_type`                      tinyint(4)    NOT NULL DEFAULT '0' COMMENT '活动系列:1=归国活动,2=出海引才,3=更多活动（出海）,4=高才博士后,5=国内线下招聘会,6=线上引才活动',
    `type`                             tinyint(4)    NOT NULL DEFAULT '0' COMMENT '活动类型:1=海外专场,2=组团招聘,3=海外活动,4=其他活动,5=学者论坛,6=学子归国行,7=创业大赛,8=人才大会,9=其他活动,10=第三方活动,11=博后活动,12=全国巡回现场招聘会,13=RPO项目人才交流会,14=组团&特色专场招聘会,15=RPO线上面试会,16=英才职通车（全球引才直播交流会）',
    `sub_type`                         tinyint(4)    NOT NULL DEFAULT '0' COMMENT '调用活动类型:5=学者论坛,6=学子归国行,7=创业大赛,8=人才大会,9=其他活动',
    `introduce`                        text          NOT NULL COMMENT '活动简介',
    `sign_end_date`                    date          NOT NULL DEFAULT '0000-00-00' COMMENT '报名截止日期',
    `sign_custom_end_date`             varchar(255)  NOT NULL DEFAULT '' COMMENT '自定义报名截止日期',
    `detail_url`                       varchar(255)  NOT NULL DEFAULT '' COMMENT '详情链接',
    `sign_up_url`                      varchar(255)  NOT NULL DEFAULT '' COMMENT '报名链接',
    `main_img_file_id`                 varchar(255)  NOT NULL DEFAULT '' COMMENT '主图文件id',
    `logo_file_id`                     varchar(255)  NOT NULL DEFAULT '' COMMENT 'logo文件id',
    `review_img_file_ids`              varchar(255)  NOT NULL DEFAULT '' COMMENT '精彩回顾图片，逗号分隔',
    `other_img_one_file_id`            varchar(255)  NOT NULL DEFAULT '' COMMENT '其他图片1',
    `other_img_two_file_id`            varchar(255)  NOT NULL DEFAULT '' COMMENT '其他图片2',
    `other_img_three_file_id`          varchar(255)  NOT NULL DEFAULT '' COMMENT '其他图片3',
    `other_description_one`            varchar(255)  NOT NULL DEFAULT '' COMMENT '其他说明1',
    `other_description_two`            varchar(255)  NOT NULL DEFAULT '' COMMENT '其他说明2',
    `other_description_three`          varchar(255)  NOT NULL DEFAULT '' COMMENT '其他说明3',
    `grounding_status`                 tinyint(2)    NOT NULL DEFAULT '0' COMMENT '上架状态（1上架 、2下架）',
    `sort`                             tinyint(2)    NOT NULL DEFAULT '0' COMMENT '活动排序',
    `company_id`                       int(10)       NOT NULL DEFAULT '0' COMMENT '关联单位id',
    `activity_status`                  tinyint(2)    NOT NULL DEFAULT '0' COMMENT '活动状态(1进行中，2已结束）',
    `activity_start_date`              date          NOT NULL DEFAULT '0000-00-00' COMMENT '活动开始时间',
    `activity_end_date`                date          NOT NULL DEFAULT '0000-00-00' COMMENT '活动结束时间',
    `is_outside_url`                   tinyint(2)    NOT NULL DEFAULT '0' COMMENT '是否站外链接',
    `activity_child_status`            tinyint(4)    NOT NULL DEFAULT '0' COMMENT '活动子状态-1=报名中;1=待举办;2=即将开始;3=正在进行;4=结束',
    `tags`                             varchar(60)   NOT NULL DEFAULT '' COMMENT '活动标签:1=付费,2=项目推广,3=内部推广,4=额外推广,9=其他',
    `to_hold_type`                     tinyint(4)    NOT NULL DEFAULT '0' COMMENT '举办方式；0:未知；1：线上；2:线下；3:线上+线下',
    `custom_feature_tag`               varchar(255)  NOT NULL DEFAULT '' COMMENT '自定义特色标签，多个用,隔开',
    `longitude`                        varchar(20)   NOT NULL DEFAULT '' COMMENT '经度',
    `latitude`                         varchar(20)   NOT NULL DEFAULT '' COMMENT '纬度',
    `activity_organization`            varchar(255)  NOT NULL DEFAULT '' COMMENT '活动组织',
    `activity_detail`                  mediumtext    NOT NULL COMMENT '活动详情',
    `participation_method`             mediumtext    NOT NULL COMMENT '参会方式',
    `activity_highlights_title`        varchar(20)   NOT NULL DEFAULT '' COMMENT '活动亮点标题(activity_highlighs字段的标题)',
    `activity_highlights`              mediumtext    NOT NULL COMMENT '活动亮点(修改为参会须知)',
    `activity_benefits`                varchar(255)  NOT NULL DEFAULT '' COMMENT '参会福利',
    `activity_benefits_content`        mediumtext    NOT NULL COMMENT '福利详情',
    `attendance_notes`                 varchar(1024) NOT NULL DEFAULT '' COMMENT '参会须知',
    `image_pc_banner_id`               int(11)       NOT NULL DEFAULT '0' COMMENT 'PC banner图',
    `image_service_code_id`            int(11)       NOT NULL DEFAULT '0' COMMENT '客服二维码',
    `image_mini_master_id`             int(11)       NOT NULL DEFAULT '0' COMMENT 'MINI主图',
    `image_mini_banner_id`             int(11)       NOT NULL DEFAULT '0' COMMENT 'MINI banner图',
    `image_notice_id`                  int(11)       NOT NULL DEFAULT '0' COMMENT '提示图',
    `apply_link_person_type`           tinyint(4)    NOT NULL DEFAULT '1' COMMENT '人才报名链接：1=表单链接；2=第三方链接（点击跳转）；3=表单选项',
    `apply_link_person_form_id`        int(11)       NOT NULL DEFAULT '0' COMMENT '人才报名链接选择表单类型时；填写表单id，activity_form',
    `apply_link_person_form_option_id` int(11)       NOT NULL DEFAULT '0' COMMENT '人才报名链接选择表单类型时；填写表单选项id，activity_form_intention_option',
    `apply_link_person`                varchar(255)  NOT NULL DEFAULT '' COMMENT '人才报名链接',
    `apply_link_company`               varchar(255)  NOT NULL DEFAULT '' COMMENT '单位报名链接',
    `apply_company_time`               date          NOT NULL DEFAULT '0000-00-00' COMMENT '单位报名截止时间',
    `template_id`                      tinyint(4)    NOT NULL DEFAULT '1' COMMENT '模板ID：1=默认；2=通用模板',
    `activity_link`                    varchar(255)  NOT NULL DEFAULT '' COMMENT '页面链接',
    `wonderful_review`                 mediumtext    NOT NULL COMMENT '精彩回顾',
    `participation_company_amount`     int(11)       NOT NULL DEFAULT '0' COMMENT '参加招聘会单位数量（更新时一同更新）',
    `click`                            int(11)       NOT NULL DEFAULT '0' COMMENT '浏览量',
    `activity_number`                  varchar(60)   NOT NULL DEFAULT '' COMMENT '活动需求人数',
    `apply_person_time`                date          NOT NULL DEFAULT '0000-00-00' COMMENT '人才报名截止时间',
    `admin_id`                         int(11)       NOT NULL DEFAULT '0' COMMENT '添加人id',
    `first_grounding_time`             datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '首次上架时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_company_id` (`company_id`),
    KEY `idx_activity_link` (`activity_link`),
    KEY `idx_series_type` (`series_type`),
    KEY `idx_type` (`type`),
    KEY `idx_sub_type` (`sub_type`),
    KEY `idx_sort` (`sort`),
    KEY `idx_grounding_status` (`grounding_status`),
    KEY `idx_activity_status` (`activity_status`),
    KEY `idx_activity_child_status` (`activity_child_status`),
    KEY `idx_to_hold_type` (`to_hold_type`),
    KEY `idx_participation_company_amount` (`participation_company_amount`),
    KEY `idx_click` (`click`),
    KEY `idx_activity_number` (`activity_number`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 427
  DEFAULT CHARSET = utf8mb4 COMMENT ='海外活动表';

-- ----------------------------
-- Table structure for hw_activity_announcement
-- ----------------------------
DROP TABLE IF EXISTS `hw_activity_announcement`;
CREATE TABLE `hw_activity_announcement`
(
    `id`              int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '专场ID',
    `add_time`        datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `activity_id`     int(11)          NOT NULL DEFAULT '0' COMMENT '活动ID',
    `company_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '单位ID',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_activity_id` (`activity_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1342
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动关联公告';

-- ----------------------------
-- Table structure for hw_activity_click_log
-- ----------------------------
DROP TABLE IF EXISTS `hw_activity_click_log`;
CREATE TABLE `hw_activity_click_log`
(
    `id`           int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `member_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '点击的会员id',
    `activity_id`  int(11)          NOT NULL DEFAULT '0' COMMENT '单位的id',
    `source`       tinyint(1)       NOT NULL DEFAULT '1' COMMENT '1:pc,2:h5;3mini',
    `useragent`    varchar(2048)    NOT NULL DEFAULT '' COMMENT '请求表头信息',
    `user_cookies` varchar(64)      NOT NULL DEFAULT '' COMMENT '用户的cookie',
    `ip`           int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作ip',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_activity_id` (`activity_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 12412
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动页浏览日志';

-- ----------------------------
-- Table structure for hw_activity_company
-- ----------------------------
DROP TABLE IF EXISTS `hw_activity_company`;
CREATE TABLE `hw_activity_company`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT COMMENT '专场ID',
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `activity_id` int(11)    NOT NULL DEFAULT '0' COMMENT '活动ID',
    `company_id`  int(11)    NOT NULL DEFAULT '0' COMMENT '单位ID',
    `is_top`      tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否置顶：1=是；2=否',
    `sort`        int(11)    NOT NULL DEFAULT '0' COMMENT '排序',
    `sort_point`  int(11)    NOT NULL DEFAULT '0' COMMENT '计算单位参会单位分数，用于活动参会单位总排序（定时任务更新）',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_activity_id` (`activity_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1370
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动关联单位';

-- ----------------------------
-- Table structure for hw_activity_company_hot
-- ----------------------------
DROP TABLE IF EXISTS `hw_activity_company_hot`;
CREATE TABLE `hw_activity_company_hot`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT COMMENT '专场ID',
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `activity_id` int(11)    NOT NULL DEFAULT '0' COMMENT '活动ID',
    `company_id`  int(11)    NOT NULL DEFAULT '0' COMMENT '单位ID',
    `sort`        int(11)    NOT NULL DEFAULT '0' COMMENT '排序',
    `link_type`   tinyint(4) NOT NULL DEFAULT '1' COMMENT '落地链接类型:1=公告详情；2=单位主页',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_activity_id` (`activity_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2222
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动热门单位';

-- ----------------------------
-- Table structure for hw_activity_feature_tag_relation
-- ----------------------------
DROP TABLE IF EXISTS `hw_activity_feature_tag_relation`;
CREATE TABLE `hw_activity_feature_tag_relation`
(
    `id`             int(11)  NOT NULL AUTO_INCREMENT,
    `add_time`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `activity_id`    int(11)  NOT NULL DEFAULT '0' COMMENT '关联的活动id',
    `feature_tag_id` int(11)  NOT NULL COMMENT '关联的特色标签id:1=博士专场；2=出站博士后专场；3=硕博综合场；4=海外优青；5=校园场；6=城市综合场；7=海外名校行；8=政府引才专场',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_activity_id` (`activity_id`) USING BTREE,
    KEY `idx_feature_tag_id` (`feature_tag_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 876
  DEFAULT CHARSET = utf8mb4 COMMENT ='活动系统特色标签';

-- ----------------------------
-- Table structure for hw_activity_promotion
-- ----------------------------
DROP TABLE IF EXISTS `hw_activity_promotion`;
CREATE TABLE `hw_activity_promotion`
(
    `id`            int(10)      NOT NULL AUTO_INCREMENT,
    `add_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`        tinyint(4)   NOT NULL DEFAULT '0' COMMENT '状态',
    `position_type` tinyint(4)   NOT NULL DEFAULT '0' COMMENT '推广位置type',
    `activity_id`   int(10)      NOT NULL DEFAULT '0' COMMENT '活动id',
    `sort`          int(10)      NOT NULL DEFAULT '0' COMMENT '推广排序',
    `start_date`    date         NOT NULL DEFAULT '0000-00-00' COMMENT '推广开始时间',
    `end_date`      date         NOT NULL DEFAULT '0000-00-00' COMMENT '推广结束时间',
    `img_type`      tinyint(4)   NOT NULL DEFAULT '0' COMMENT '显示图片类型',
    `img_file_id`   varchar(255) NOT NULL DEFAULT '' COMMENT '图片地址',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1387
  DEFAULT CHARSET = utf8mb4 COMMENT ='海外活动推广记录表';

-- ----------------------------
-- Table structure for hw_activity_session
-- ----------------------------
DROP TABLE IF EXISTS `hw_activity_session`;
CREATE TABLE `hw_activity_session`
(
    `id`             int(10)      NOT NULL AUTO_INCREMENT,
    `add_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`         tinyint(2)   NOT NULL DEFAULT '0' COMMENT '状态',
    `activity_id`    int(10)      NOT NULL DEFAULT '0' COMMENT '活动id',
    `name`           varchar(255) NOT NULL DEFAULT '' COMMENT '场次名称',
    `number`         varchar(255) NOT NULL DEFAULT '' COMMENT '场次编号',
    `custom_time`    varchar(255) NOT NULL DEFAULT '' COMMENT '自定义举办时间',
    `start_date`     date         NOT NULL DEFAULT '0000-00-00' COMMENT '举办开始日期',
    `start_time`     char(4)      NOT NULL DEFAULT '' COMMENT '举办开始时间',
    `end_date`       date         NOT NULL DEFAULT '0000-00-00' COMMENT '举办结束日期',
    `end_time`       char(4)      NOT NULL DEFAULT '' COMMENT '举办结束时间',
    `time_type`      tinyint(2)   NOT NULL DEFAULT '0' COMMENT '举办时间类型（1当地  2北京）',
    `custom_address` varchar(255) NOT NULL DEFAULT '' COMMENT '自定义举办地点',
    `detail_address` varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
    `sort`           tinyint(2)   NOT NULL DEFAULT '0' COMMENT '排序',
    `is_top`         tinyint(4)   NOT NULL DEFAULT '2' COMMENT '是否置顶；1:置顶；2:不置顶',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_activity_id` (`activity_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 589
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for hw_activity_session_area
-- ----------------------------
DROP TABLE IF EXISTS `hw_activity_session_area`;
CREATE TABLE `hw_activity_session_area`
(
    `id`          int(10)    NOT NULL AUTO_INCREMENT,
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `activity_id` int(10)    NOT NULL DEFAULT '0' COMMENT '活动id',
    `session_id`  int(10)    NOT NULL DEFAULT '0' COMMENT '场次id',
    `area_id`     int(10)    NOT NULL DEFAULT '0' COMMENT '地点id',
    `level`       tinyint(2) NOT NULL COMMENT '地区级别',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1503
  DEFAULT CHARSET = utf8mb4 COMMENT ='海外活动场次地点表';

-- ----------------------------
-- Table structure for hw_special_activity
-- ----------------------------
DROP TABLE IF EXISTS `hw_special_activity`;
CREATE TABLE `hw_special_activity`
(
    `id`                                 int(11)      NOT NULL AUTO_INCREMENT COMMENT '专场ID',
    `add_time`                           datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                        datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `name`                               varchar(255) NOT NULL DEFAULT '' COMMENT '专场名称',
    `to_hold_type`                       tinyint(4)   NOT NULL DEFAULT '1' COMMENT '举办方式：1=线上；2=线下；3=线上+线下',
    `custom_time`                        varchar(255) NOT NULL DEFAULT '' COMMENT '自定义举办时间',
    `start_date`                         date                  DEFAULT NULL COMMENT '举办开始日期',
    `start_time`                         char(4)      NOT NULL DEFAULT '' COMMENT '举办开始时间',
    `event_organization`                 varchar(255) NOT NULL DEFAULT '' COMMENT '活动组织',
    `end_date`                           date                  DEFAULT NULL COMMENT '举办结束日期',
    `end_time`                           char(4)      NOT NULL DEFAULT '' COMMENT '举办结束时间',
    `custom_address`                     varchar(255) NOT NULL DEFAULT '' COMMENT '自定义举办地点',
    `detail_address`                     varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
    `activity_detail`                    mediumtext COMMENT '活动详情',
    `participation_method`               mediumtext COMMENT '参会方式',
    `retrospection`                      mediumtext COMMENT '往届回顾',
    `participation_benefit`              varchar(255) NOT NULL DEFAULT '' COMMENT '参会福利',
    `participation_benefit_detail`       mediumtext COMMENT '参会福利详情',
    `image_service_code_id`              int(11)      NOT NULL DEFAULT '0' COMMENT '客服二维码',
    `image_pc_banner_id`                 int(11)      NOT NULL DEFAULT '0' COMMENT 'PC-banner图',
    `image_mini_banner_id`               int(11)      NOT NULL DEFAULT '0' COMMENT '小程序-banner图',
    `apply_link_person_type`             tinyint(4)   NOT NULL DEFAULT '1' COMMENT '人才报名链接：1=表单报名链接；2:其他链接；3:报名表单选项',
    `apply_link_person_form_id`          int(11)      NOT NULL DEFAULT '0' COMMENT '人才报名链接-表单id',
    `apply_link_person_form_option_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '人才报名链接-表单选项id',
    `apply_link_person`                  varchar(255) NOT NULL DEFAULT '' COMMENT '第三方人才报名链接',
    `apply_person_time`                  date         NOT NULL DEFAULT '0000-00-00' COMMENT '人才报名截止时间',
    `apply_link_company`                 varchar(255) NOT NULL DEFAULT '' COMMENT '单位报名链接',
    `apply_company_time`                 date         NOT NULL DEFAULT '0000-00-00' COMMENT '单位报名截止时间',
    `template_id`                        tinyint(4)   NOT NULL DEFAULT '1' COMMENT '模板ID；1:默认；2:通用',
    `special_link`                       varchar(255) NOT NULL DEFAULT '' COMMENT '专场页面链接',
    `status`                             tinyint(4)   NOT NULL DEFAULT '1' COMMENT '专场状态；1：待举办;2：即将开始；3:进行中；4:已结束；',
    `real_participation_activity_amount` smallint(6)  NOT NULL DEFAULT '0' COMMENT '关联活动数量（编辑新增时一同更新）',
    `tag_ids`                            varchar(255) NOT NULL DEFAULT '' COMMENT '特色标签，多个使用,隔开：1=博士专场；2=出站博士后专场；3=硕博综合场；4=海外优青；5=校园场；6=城市综合场；7=海外名校行；8=政府引才专场\n',
    `custom_tag`                         varchar(255) NOT NULL DEFAULT '' COMMENT '自定义特色标签中文，多个使用,隔开',
    `type`                               tinyint(4)   NOT NULL DEFAULT '0' COMMENT '活动类型',
    `admin_id`                           int(11)      NOT NULL DEFAULT '0' COMMENT '添加人id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7
  DEFAULT CHARSET = utf8mb4 COMMENT ='专场表';

-- ----------------------------
-- Table structure for hw_special_activity_relation
-- ----------------------------
DROP TABLE IF EXISTS `hw_special_activity_relation`;
CREATE TABLE `hw_special_activity_relation`
(
    `id`             int(10) unsigned NOT NULL AUTO_INCREMENT,
    `special_id`     int(11)          NOT NULL DEFAULT '0' COMMENT '专场ID',
    `activity_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '活动ID',
    `sort`           int(11)          NOT NULL DEFAULT '0' COMMENT '排序',
    `add_time`       datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `activity_short` varchar(128)     NOT NULL DEFAULT '' COMMENT '活动简称',
    `is_recommend`   tinyint(4)       NOT NULL DEFAULT '2' COMMENT '是否推广；1:推广；2:不推广',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_special_id` (`special_id`) USING BTREE,
    KEY `idx_activity_id` (`activity_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 607
  DEFAULT CHARSET = utf8mb4 COMMENT ='专场关联活动中间表';

-- ----------------------------
-- Table structure for hw_special_activity_session_area
-- ----------------------------
DROP TABLE IF EXISTS `hw_special_activity_session_area`;
CREATE TABLE `hw_special_activity_session_area`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `special_id`  int(11)    NOT NULL DEFAULT '0' COMMENT '专场id',
    `area_id`     int(11)    NOT NULL DEFAULT '0' COMMENT '地点id',
    `level`       tinyint(4) NOT NULL COMMENT '地区级别',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_special_id` (`special_id`) USING BTREE,
    KEY `idx_area_id` (`area_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 404
  DEFAULT CHARSET = utf8mb4 COMMENT ='专场地点';

-- ----------------------------
-- Table structure for job
-- ----------------------------
DROP TABLE IF EXISTS `job`;
CREATE TABLE `job`
(
    `id`                   int(11)        NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`             datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`               tinyint(1)     NOT NULL DEFAULT '0' COMMENT '状态（1在线，0下线，3等待发布、编辑、保存，9删除）',
    `member_id`            int(11)        NOT NULL DEFAULT '0' COMMENT '会员id',
    `company_id`           int(11)        NOT NULL DEFAULT '0' COMMENT '企业id',
    `is_article`           tinyint(1)     NOT NULL DEFAULT '0' COMMENT '是否公告模式，1是，2否',
    `name`                 varchar(256)   NOT NULL DEFAULT '' COMMENT '职位名称',
    `period_date`          datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '职位有效期',
    `is_stick`             tinyint(1)     NOT NULL DEFAULT '0' COMMENT '是否长期',
    `code`                 varchar(256)   NOT NULL DEFAULT '' COMMENT '职位代码',
    `job_category_id`      int(11)        NOT NULL DEFAULT '0' COMMENT '职位类别id',
    `education_type`       int(11)        NOT NULL DEFAULT '0' COMMENT '学历要求类型',
    `major_id`             varchar(4096)  NOT NULL DEFAULT '' COMMENT '专业id',
    `nature_type`          int(11)        NOT NULL DEFAULT '0' COMMENT '性质类型',
    `is_negotiable`        tinyint(1)     NOT NULL DEFAULT '0' COMMENT '是否自定义：1是，2否',
    `wage_type`            tinyint(1)     NOT NULL DEFAULT '0' COMMENT '薪资类型(1月，2年，3日）',
    `min_wage`             int(11)        NOT NULL DEFAULT '0' COMMENT '薪资最低',
    `max_wage`             int(11)        NOT NULL DEFAULT '0' COMMENT '薪资最高',
    `experience_type`      tinyint(1)     NOT NULL DEFAULT '0' COMMENT '经验要求类型',
    `age_type`             varchar(128)   NOT NULL DEFAULT '' COMMENT '年龄要求类型',
    `min_age`              tinyint(1)     NOT NULL DEFAULT '0' COMMENT '年龄要求最低',
    `max_age`              tinyint(1)     NOT NULL DEFAULT '0' COMMENT '年龄要求最高',
    `title_type`           tinyint(5)     NOT NULL DEFAULT '0' COMMENT '职称类型',
    `political_type`       tinyint(1)     NOT NULL DEFAULT '0' COMMENT '政治面貌类型',
    `abroad_type`          tinyint(1)     NOT NULL DEFAULT '0' COMMENT '海外经历类型',
    `amount`               varchar(128)   NOT NULL DEFAULT '' COMMENT '招聘人数',
    `department`           varchar(256)   NOT NULL DEFAULT '' COMMENT '用户部门',
    `district_id`          int(11)        NOT NULL DEFAULT '0' COMMENT '工作地点id',
    `province_id`          int(11)        NOT NULL DEFAULT '0' COMMENT '省Id',
    `city_id`              int(11)        NOT NULL DEFAULT '0' COMMENT '市Id',
    `address`              varchar(128)   NOT NULL DEFAULT '' COMMENT '工作地点详细地址',
    `welfare_tag`          varchar(512)   NOT NULL DEFAULT '' COMMENT '福利标签',
    `duty`                 varchar(2048)  NOT NULL DEFAULT '' COMMENT '岗位职责',
    `requirement`          varchar(2048)  NOT NULL DEFAULT '' COMMENT '任职要求',
    `remark`               varchar(2048)  NOT NULL DEFAULT '' COMMENT '其他说明',
    `refresh_time`         datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '发布时间',
    `audit_status`         tinyint(1)     NOT NULL DEFAULT '0' COMMENT '审核状态（1审核通过，0已下线，-1审核拒绝，3编辑中，7等待审核）',
    `is_show`              tinyint(1)     NOT NULL DEFAULT '1' COMMENT '是否显示',
    `click`                int(11)        NOT NULL DEFAULT '0' COMMENT '点击次数',
    `lat`                  decimal(32, 8) NOT NULL DEFAULT '0.00000000' COMMENT '维度',
    `lng`                  decimal(32, 8) NOT NULL DEFAULT '0.00000000' COMMENT '经度',
    `release_time`         datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '改字段已停用',
    `offline_type`         tinyint(1)     NOT NULL DEFAULT '0' COMMENT '下线方式 0:无；1:自动下线；2：手动下线，3:违规下线',
    `offline_time`         datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '操作下线时间',
    `announcement_id`      int(11)        NOT NULL DEFAULT '0' COMMENT '公告id',
    `gender_type`          tinyint(1)     NOT NULL DEFAULT '0' COMMENT '性别要求类型 0:不限；1:男；2:女',
    `create_type`          tinyint(1)     NOT NULL DEFAULT '1' COMMENT '创建类型，1:自建；2:代建',
    `create_id`            int(11)        NOT NULL DEFAULT '0' COMMENT '创建人Id',
    `creator`              varchar(90)    NOT NULL DEFAULT '' COMMENT '创建人名称',
    `download_amount`      int(11)        NOT NULL DEFAULT '0' COMMENT '简历下载次数',
    `apply_audit_time`     datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '申请审核时间',
    `delete_time`          datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `apply_type`           varchar(32)    NOT NULL DEFAULT '' COMMENT '应聘方式(1电子邮件xxxx',
    `apply_address`        varchar(600)   NOT NULL DEFAULT '' COMMENT '投递地址',
    `first_release_time`   datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '首次发布时间',
    `offline_reason`       varchar(255)   NOT NULL DEFAULT '' COMMENT '违规下线原因',
    `is_consume_release`   tinyint(1)     NOT NULL DEFAULT '2' COMMENT '是否消费过发布，1:是，2:否',
    `real_refresh_time`    datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '真实刷新时间',
    `file_ids`             varchar(100)   NOT NULL DEFAULT '' COMMENT '应聘材料附件id对应file表id',
    `delivery_limit_type`  varchar(60)    NOT NULL DEFAULT '' COMMENT '投递限制 1学历 2应聘材料',
    `refresh_date`         date           NOT NULL DEFAULT '0000-00-00' COMMENT '发布日期(格式Ymd)',
    `delivery_type`        tinyint(1)     NOT NULL DEFAULT '0' COMMENT '投递类型1=站外投递,2=站内投递',
    `delivery_way`         tinyint(1)     NOT NULL DEFAULT '0' COMMENT '投递方式 1平台投递 2邮箱投递 3网址投递',
    `extra_notify_address` varchar(255)   NOT NULL DEFAULT '' COMMENT '投递通知地址',
    `establishment_type`   varchar(60)    NOT NULL DEFAULT '' COMMENT '职位编制类型 对应字典表类型31',
    `is_establishment`     tinyint(2)     NOT NULL DEFAULT '2' COMMENT '是否有编制 1有编制 2无编制',
    `is_miniapp`           tinyint(2)     NOT NULL DEFAULT '2' COMMENT '是否被小程序调用1调用 2没调用',
    `is_manual_tag`        tinyint(2)     NOT NULL DEFAULT '0' COMMENT '是否运营手动标记 0没标记 1标记 2不标记',
    `uuid`                 varchar(64)    NOT NULL DEFAULT '' COMMENT 'uuid',
    `company_sort`         int(11)        NOT NULL DEFAULT '0' COMMENT '公司排序',
    `apply_heat_type`      tinyint(2)     NOT NULL DEFAULT '0' COMMENT '该职位近90天的投递热度（V1.9需求，投递触发更新）',
    `is_first_release`     tinyint(2)     NOT NULL DEFAULT '2' COMMENT '首发:1=首发,2=非首发',
    `search_name`          varchar(1024)  NOT NULL DEFAULT '',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_refresh_time` (`refresh_time`) USING BTREE,
    KEY `idx_click` (`click`) USING BTREE,
    KEY `idx_job_category_id` (`job_category_id`) USING BTREE,
    KEY `idx_min_wage` (`min_wage`) USING BTREE,
    KEY `idx_max_wage` (`max_wage`) USING BTREE,
    KEY `idx_district_id` (`district_id`) USING BTREE,
    KEY `idx_province_id` (`province_id`) USING BTREE,
    KEY `idx_city_id` (`city_id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_refresh_date` (`refresh_date`) USING BTREE,
    KEY `idx_status` (`status`),
    KEY `idx_education_type` (`education_type`) USING BTREE,
    KEY `idx_is_establishment` (`is_establishment`),
    KEY `idx_uuid` (`uuid`),
    KEY `idx_is_show` (`is_show`),
    KEY `idx_company_sort` (`company_sort`),
    KEY `idx_status_refresh_date_company_sort_id` (`status`, `refresh_date`, `company_sort`, `id`),
    KEY `idx_apply_heat_type` (`apply_heat_type`),
    KEY `idx_is_first_release` (`is_first_release`),
    KEY `idx_job_optimization_mini` (`is_show`, `status`, `city_id`, `is_miniapp`, `refresh_date`, `id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1725021
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位表';

-- ----------------------------
-- Table structure for job_apply
-- ----------------------------
DROP TABLE IF EXISTS `job_apply`;
CREATE TABLE `job_apply`
(
    `id`                               int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                         datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                      datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`                           tinyint(1)    NOT NULL DEFAULT '1' COMMENT '状态默认1(1：已投递  2:通过初筛  3:邀请面试  4:不合适  5:已录用)\n',
    `company_id`                       int(11)       NOT NULL DEFAULT '0' COMMENT '发布职位的单位id',
    `resume_id`                        int(11)       NOT NULL DEFAULT '0' COMMENT '简历id',
    `company_member_id`                int(11)       NOT NULL DEFAULT '0' COMMENT '单位的会员id',
    `resume_member_id`                 int(11)       NOT NULL DEFAULT '0' COMMENT '个人的简历member_id',
    `job_id`                           int(11)       NOT NULL DEFAULT '0' COMMENT '职位id',
    `job_name`                         varchar(256)  NOT NULL DEFAULT '' COMMENT '职位名称(快照职位名称)',
    `resume_name`                      varchar(32)   NOT NULL DEFAULT '' COMMENT '投递简历人的名称(快照)',
    `is_check`                         tinyint(1)    NOT NULL DEFAULT '0' COMMENT '是否已经查看',
    `source`                           tinyint(1)    NOT NULL DEFAULT '0' COMMENT '投递来源(1自主2委托)',
    `company_tag`                      varchar(1024) NOT NULL DEFAULT '' COMMENT '企业标识的tag(逗号隔开的文案)',
    `resume_attachment_id`             int(11)       NOT NULL DEFAULT '0' COMMENT '简历附件id',
    `stuff_file_id`                    varchar(128)  NOT NULL DEFAULT '' COMMENT '材料id',
    `is_invitation`                    tinyint(1)    NOT NULL DEFAULT '0' COMMENT '是否已经邀请过面试了(冗余过来方便查询)',
    `note`                             varchar(128)  NOT NULL DEFAULT '' COMMENT '备注',
    `company_mark_status`              tinyint(2)    NOT NULL DEFAULT '0' COMMENT '企业辅助标记状态，1:未接通，2:无意向，3:未到面，4:待录用,5、已入职，6:未入职，',
    `is_resume_operation_remind_check` tinyint(1)    NOT NULL DEFAULT '1' COMMENT '是否提醒已查看(这个状态 其实是记录当前主状态操作(不包含查看)是否已经被求职者查看了,查看过就不需要去强提醒了,1是已查看,2是未查看)',
    `is_resume_check_remind_check`     tinyint(1)    NOT NULL DEFAULT '1' COMMENT '这个投递被老师查看就变成2,求职者未点击了已知以后就变成了1',
    `equity_status`                    tinyint(1)    NOT NULL DEFAULT '0' COMMENT '权益状态 0失效 1生效中',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_resume_attachment_id` (`resume_attachment_id`) USING BTREE,
    KEY `idx_stuff_file_id` (`stuff_file_id`) USING BTREE,
    KEY `idx_add_time` (`add_time`),
    KEY `idx_equity_status` (`equity_status`),
    KEY `idx_company_member_id` (`company_member_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1658317
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位投递';

-- ----------------------------
-- Table structure for job_apply_abroad_total
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_abroad_total`;
CREATE TABLE `job_apply_abroad_total`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time` datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `job_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `total`       int(11)          NOT NULL DEFAULT '0' COMMENT '匹配投递量',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 205072
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位总投递量表(匹配海外经历统计)';

-- ----------------------------
-- Table structure for job_apply_abroad_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_abroad_total_daily`;
CREATE TABLE `job_apply_abroad_total_daily`
(
    `id`       int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date` date             NOT NULL COMMENT '时间格式',
    `job_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `total`    int(11)          NOT NULL DEFAULT '0' COMMENT '匹配投递量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_ad` (`job_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 618800
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位日投递量表(匹配海外经历统计)';

-- ----------------------------
-- Table structure for job_apply_area_total
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_area_total`;
CREATE TABLE `job_apply_area_total`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `job_id`         int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `city_total`     int(11)          NOT NULL DEFAULT '0' COMMENT '城市匹配投递量',
    `province_total` int(11)          NOT NULL DEFAULT '0' COMMENT '省份匹配投递量',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 383571
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位总投递量表(匹配意向城市统计)';

-- ----------------------------
-- Table structure for job_apply_area_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_area_total_daily`;
CREATE TABLE `job_apply_area_total_daily`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`       date             NOT NULL COMMENT '时间格式',
    `job_id`         int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `city_total`     int(11)          NOT NULL DEFAULT '0' COMMENT '城市匹配投递量',
    `province_total` int(11)          NOT NULL DEFAULT '0' COMMENT '省份匹配投递量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_ad` (`job_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1647004
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位日投递量表(匹配意向城市统计)';

-- ----------------------------
-- Table structure for job_apply_education_total
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_education_total`;
CREATE TABLE `job_apply_education_total`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time` datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `job_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `rests`       int(11)          NOT NULL DEFAULT '0' COMMENT '其他',
    `college`     int(11)          NOT NULL DEFAULT '0' COMMENT '大专',
    `poaceae`     int(11)          NOT NULL DEFAULT '0' COMMENT '本科',
    `doctor`      int(11)          NOT NULL DEFAULT '0' COMMENT '博士',
    `master`      int(11)          NOT NULL DEFAULT '0' COMMENT '硕士',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 481577
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位总投递量表(学历统计)';

-- ----------------------------
-- Table structure for job_apply_education_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_education_total_daily`;
CREATE TABLE `job_apply_education_total_daily`
(
    `id`       int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date` date             NOT NULL COMMENT '时间格式',
    `job_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `rests`    int(11)          NOT NULL DEFAULT '0' COMMENT '其他',
    `college`  int(11)          NOT NULL DEFAULT '0' COMMENT '大专',
    `poaceae`  int(11)          NOT NULL DEFAULT '0' COMMENT '本科',
    `doctor`   int(11)          NOT NULL DEFAULT '0' COMMENT '博士',
    `master`   int(11)          NOT NULL DEFAULT '0' COMMENT '硕士',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_ad` (`job_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2336408
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位日投递量表(学历统计)';

-- ----------------------------
-- Table structure for job_apply_handle_log
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_handle_log`;
CREATE TABLE `job_apply_handle_log`
(
    `id`           int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `job_apply_id` int(11)      NOT NULL DEFAULT '0' COMMENT '职位申请id',
    `resume_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `company_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '单位id',
    `handler_type` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '处理人类型(1求职者，2企业)',
    `handler_name` varchar(256) NOT NULL DEFAULT '0' COMMENT '处理人的名称（快照）',
    `handle_type`  tinyint(1)   NOT NULL DEFAULT '0' COMMENT '处理的类型(1投递，2查看。。。99备注)',
    `title`        varchar(512) NOT NULL DEFAULT '' COMMENT '标题',
    `content`      varchar(512) NOT NULL DEFAULT '' COMMENT '内容',
    `handle_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '处理人账号id(对应member_id)',
    PRIMARY KEY (`id`),
    KEY `idx_job_apply_id` (`job_apply_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2703436
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位投递的整个流程日志(单个求职者)';

-- ----------------------------
-- Table structure for job_apply_limit_company
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_limit_company`;
CREATE TABLE `job_apply_limit_company`
(
    `id`                        int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `job_apply_limit_config_id` int(11)  NOT NULL DEFAULT '0' COMMENT '投递限制id',
    `company_id`                int(11)  NOT NULL DEFAULT '0' COMMENT '单位id',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_job_apply_limit_config_id` (`job_apply_limit_config_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1700
  DEFAULT CHARSET = utf8mb4 COMMENT ='投递限制单位';

-- ----------------------------
-- Table structure for job_apply_limit_company_tag
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_limit_company_tag`;
CREATE TABLE `job_apply_limit_company_tag`
(
    `id`                        int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `job_apply_limit_config_id` int(11)  NOT NULL DEFAULT '0' COMMENT '投递限制id',
    `company_feature_tag_id`    int(11)  NOT NULL DEFAULT '0' COMMENT '单位特色标签id(0代表全部合作单位）',
    PRIMARY KEY (`id`),
    KEY `idx_company_tag_id` (`company_feature_tag_id`) USING BTREE,
    KEY `idx_job_apply_limit_config_id` (`job_apply_limit_config_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 11
  DEFAULT CHARSET = utf8mb4 COMMENT ='投递限制单位标签';

-- ----------------------------
-- Table structure for job_apply_limit_config
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_limit_config`;
CREATE TABLE `job_apply_limit_config`
(
    `id`           int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`     datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`       tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `type`         tinyint(1)    NOT NULL DEFAULT '0' COMMENT '限制类型1禁止投递，2降权投递',
    `resume_type`  tinyint(1)    NOT NULL DEFAULT '0' COMMENT '限制求职者类型1、求职者id。2求职者标签',
    `resume_data`  text COMMENT '限制的具体数据，逗号隔开，如果是id或者标签的id',
    `company_type` tinyint(1)    NOT NULL DEFAULT '0' COMMENT '限制单位类型1、全部合作单位。2指定单位。3指定单位标签',
    `company_data` text COMMENT '限制的具体数据，逗号隔开，如果是id或者标签的id',
    `message_type` tinyint(1)    NOT NULL DEFAULT '0' COMMENT '投递提示文案类型1xxx,2xxxxxx',
    `remark`       varchar(2048) NOT NULL DEFAULT '' COMMENT '备注',
    `admin_id`     int(11)       NOT NULL DEFAULT '0' COMMENT '添加人员id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 23
  DEFAULT CHARSET = utf8mb4 COMMENT ='投递限制配置';

-- ----------------------------
-- Table structure for job_apply_limit_resume
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_limit_resume`;
CREATE TABLE `job_apply_limit_resume`
(
    `id`                        int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `job_apply_limit_config_id` int(11) NOT NULL DEFAULT '0' COMMENT '投递限制id',
    `resume_id`                 int(11) NOT NULL DEFAULT '0' COMMENT '求职者id',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_job_apply_limit_config_id` (`job_apply_limit_config_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 59
  DEFAULT CHARSET = utf8mb4 COMMENT ='投递限制求职者';

-- ----------------------------
-- Table structure for job_apply_limit_resume_tag
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_limit_resume_tag`;
CREATE TABLE `job_apply_limit_resume_tag`
(
    `id`                        int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `job_apply_limit_config_id` int(11)  NOT NULL DEFAULT '0' COMMENT '投递限制id',
    `resume_tag_id`             int(11)  NOT NULL DEFAULT '0' COMMENT '求职者标签id',
    PRIMARY KEY (`id`),
    KEY `idx_resume_tag_id` (`resume_tag_id`) USING BTREE,
    KEY `idx_job_apply_limit_config_id` (`job_apply_limit_config_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 44
  DEFAULT CHARSET = utf8mb4 COMMENT ='投递限制求职者标签';

-- ----------------------------
-- Table structure for job_apply_record
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_record`;
CREATE TABLE `job_apply_record`
(
    `id`              int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `delivery_type`   tinyint(1)    NOT NULL DEFAULT '1' COMMENT '投递类型 1站外投递 2站内投递',
    `delivery_way`    tinyint(1)    NOT NULL DEFAULT '1' COMMENT '投递方式 1平台投递 2邮箱投递 3网址投递 99自主录入投递',
    `add_time`        datetime      NOT NULL COMMENT '创建时间',
    `update_time`     datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `apply_id`        int(11)       NOT NULL DEFAULT '0' COMMENT '投递ID 关联回站内投递表',
    `apply_site_id`   int(11)       NOT NULL DEFAULT '0' COMMENT '投递ID 关联回站外投递表',
    `company_id`      int(11)       NOT NULL DEFAULT '0' COMMENT '发布职位的单位id',
    `resume_id`       int(11)       NOT NULL DEFAULT '0' COMMENT '简历id',
    `announcement_id` int(11)       NOT NULL DEFAULT '0' COMMENT '公告ID',
    `job_id`          int(11)       NOT NULL DEFAULT '0' COMMENT '职位id',
    `source`          tinyint(1)    NOT NULL DEFAULT '0' COMMENT '投递来源(1自主投递2委托投递3自主录入)',
    `platform`        tinyint(1)    NOT NULL DEFAULT '0' COMMENT '投递平台 1=pc 2=h5 3=mini',
    `match_complete`  decimal(8, 2) NOT NULL DEFAULT '0.00' COMMENT '匹配度',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_company_id` (`company_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_apply_id` (`apply_id`),
    KEY `idx_apply_site_id` (`apply_site_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2852457
  DEFAULT CHARSET = utf8mb4 COMMENT ='投递记录表';

-- ----------------------------
-- Table structure for job_apply_record_extra
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_record_extra`;
CREATE TABLE `job_apply_record_extra`
(
    `job_id`                  int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '职位ID',
    `total`                   int(11)          NOT NULL DEFAULT '0' COMMENT '投递总数',
    `delivery_type_outer`     int(11)          NOT NULL DEFAULT '0' COMMENT '站外投递总数',
    `delivery_type_outside`   int(11)          NOT NULL DEFAULT '0' COMMENT '站内投递总数',
    `delivery_way_platform`   int(11)          NOT NULL DEFAULT '0' COMMENT '投递方式平台投递总数',
    `delivery_way_email`      int(11)          NOT NULL DEFAULT '0' COMMENT '投递方式邮件投递总数',
    `delivery_way_link`       int(11)          NOT NULL DEFAULT '0' COMMENT '投递方式链接投递总数',
    `platform_pc`             int(11)          NOT NULL DEFAULT '0' COMMENT '投递平台pc总数',
    `platform_h5`             int(11)          NOT NULL DEFAULT '0' COMMENT '投递平台h5总数',
    `platform_mini`           int(11)          NOT NULL DEFAULT '0' COMMENT '投递平台mini总数',
    `platform_app`            int(11)          NOT NULL DEFAULT '0' COMMENT '投递平台app总数',
    `interview`               int(11)          NOT NULL DEFAULT '0' COMMENT '投递邀面数量',
    `education_other`         int(11)          NOT NULL DEFAULT '0' COMMENT '学历分布-其他',
    `education_junior`        int(11)          NOT NULL DEFAULT '0' COMMENT '学历分布-大专',
    `education_undergraduate` int(11)          NOT NULL DEFAULT '0' COMMENT '学历分布-本科',
    `education_master`        int(11)          NOT NULL DEFAULT '0' COMMENT '学历分布-硕士',
    `education_doctor`        int(11)          NOT NULL DEFAULT '0' COMMENT '学历分布-博士',
    PRIMARY KEY (`job_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1725020
  DEFAULT CHARSET = utf8mb4 COMMENT ='投递记录统计表';

-- ----------------------------
-- Table structure for job_apply_subtitle_total
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_subtitle_total`;
CREATE TABLE `job_apply_subtitle_total`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`   datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `job_id`        int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `title_level_1` int(11)          NOT NULL DEFAULT '0' COMMENT '一级职称ID',
    `title_level_2` int(11)          NOT NULL DEFAULT '0' COMMENT '二级职称ID',
    `total`         int(11)          NOT NULL DEFAULT '0' COMMENT '投递量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_tl2` (`job_id`, `title_level_2`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_title_level_1` (`title_level_1`) USING BTREE,
    KEY `idx_title_level_2` (`title_level_2`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 721871
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位总投递量表(二级职称统计)';

-- ----------------------------
-- Table structure for job_apply_subtitle_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_subtitle_total_daily`;
CREATE TABLE `job_apply_subtitle_total_daily`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`      date             NOT NULL COMMENT '时间格式',
    `job_id`        int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `title_level_1` int(11)          NOT NULL DEFAULT '0' COMMENT '一级职称ID',
    `title_level_2` int(11)          NOT NULL DEFAULT '0' COMMENT '二级职称ID',
    `total`         int(11)          NOT NULL DEFAULT '0' COMMENT '投递量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_tl2_ad` (`job_id`, `title_level_2`, `add_date`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_title_level_1` (`title_level_1`) USING BTREE,
    KEY `idx_title_level_2` (`title_level_2`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2454966
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位日投递量表(二级职称统计)';

-- ----------------------------
-- Table structure for job_apply_title_total
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_title_total`;
CREATE TABLE `job_apply_title_total`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `job_id`          int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `title_primary`   int(11)          NOT NULL DEFAULT '0' COMMENT '初级职称',
    `title_middle`    int(11)          NOT NULL DEFAULT '0' COMMENT '中级职称',
    `title_high`      int(11)          NOT NULL DEFAULT '0' COMMENT '正高级职称',
    `title_vice_high` int(11)          NOT NULL DEFAULT '0' COMMENT '副高级职称',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 481577
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位总投递量表(一级职称统计)';

-- ----------------------------
-- Table structure for job_apply_title_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_title_total_daily`;
CREATE TABLE `job_apply_title_total_daily`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`        date             NOT NULL COMMENT '时间格式',
    `job_id`          int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `title_primary`   int(11)          NOT NULL DEFAULT '0' COMMENT '初级职称',
    `title_middle`    int(11)          NOT NULL DEFAULT '0' COMMENT '中级职称',
    `title_high`      int(11)          NOT NULL DEFAULT '0' COMMENT '正高级职称',
    `title_vice_high` int(11)          NOT NULL DEFAULT '0' COMMENT '副高级职称',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_ad` (`job_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2336408
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位日投递量表(一级职称统计)';

-- ----------------------------
-- Table structure for job_apply_top_equity_record
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_top_equity_record`;
CREATE TABLE `job_apply_top_equity_record`
(
    `id`                       int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`                 datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
    `update_time`              datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `resume_id`                int(11)          NOT NULL DEFAULT '0' COMMENT '简历ID',
    `job_id`                   int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `apply_id`                 int(11)          NOT NULL DEFAULT '0' COMMENT '站内投递表的ID',
    `equity_status`            tinyint(1)       NOT NULL DEFAULT '0' COMMENT '是否使用权益投递，权益状态 0失效 1生效中',
    `equity_package_detail_id` int(11)          NOT NULL DEFAULT '0' COMMENT '源真实消耗ID(也就是resume_equity_package_detail表ID)',
    `expire_type`              tinyint(2)       NOT NULL DEFAULT '1' COMMENT '权益过期类型 1正在生效 2系统到期失效 3单位操作失效',
    `expire_time`              datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '失效时间',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_apply_id` (`apply_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 4433
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位投递使用权益记录表';

-- ----------------------------
-- Table structure for job_apply_top_major_total
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_top_major_total`;
CREATE TABLE `job_apply_top_major_total`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time` datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `job_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `total`       int(11)          NOT NULL DEFAULT '0' COMMENT '匹配投递量',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 418851
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位总投递量表(匹配学科专业统计)';

-- ----------------------------
-- Table structure for job_apply_top_major_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_top_major_total_daily`;
CREATE TABLE `job_apply_top_major_total_daily`
(
    `id`       int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date` date             NOT NULL COMMENT '时间格式',
    `job_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `total`    int(11)          NOT NULL DEFAULT '0' COMMENT '匹配投递量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_ad` (`job_id`, `add_date`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1728946
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位日投递量表(匹配学科专业统计)';

-- ----------------------------
-- Table structure for job_apply_total
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_total`;
CREATE TABLE `job_apply_total`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time` datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `job_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `total`       int(11)          NOT NULL DEFAULT '0' COMMENT '投递量',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 481577
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位总投递量表';

-- ----------------------------
-- Table structure for job_apply_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_total_daily`;
CREATE TABLE `job_apply_total_daily`
(
    `id`       int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date` date             NOT NULL COMMENT '时间格式',
    `job_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `total`    int(11)          NOT NULL DEFAULT '0' COMMENT '投递量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_ad` (`job_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2336408
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位日投递量表';

-- ----------------------------
-- Table structure for job_apply_update_config
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_update_config`;
CREATE TABLE `job_apply_update_config`
(
    `id`                          int(5) unsigned NOT NULL AUTO_INCREMENT,
    `class_name`                  varchar(255)    NOT NULL DEFAULT '' COMMENT '脚本类名称',
    `update_time`                 datetime        NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '脚本更新时间',
    `start_id`                    int(11)         NOT NULL DEFAULT '0' COMMENT '执行开始节点',
    `end_id`                      int(11)         NOT NULL DEFAULT '0' COMMENT '执行结束节点',
    `limit_max`                   int(11)         NOT NULL DEFAULT '0' COMMENT '最大limit数量',
    `real_execute_status`         tinyint(1)      NOT NULL DEFAULT '0' COMMENT '实时脚本执行状态,0:未完成,1:已完成',
    `history_execute_status`      tinyint(1)      NOT NULL DEFAULT '0' COMMENT '历史脚本执行状态,0:未完成,1:已完成',
    `history_log_complete_status` tinyint(1)      NOT NULL DEFAULT '0' COMMENT '历史数据完成状态,0:未完成,1:已完成',
    `real_fail_msg`               text COMMENT '实时脚本错误信息',
    `history_fail_msg`            text COMMENT '历史脚本错误信息',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位投递量统计同步配置表';

-- ----------------------------
-- Table structure for job_apply_work_experience_total
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_work_experience_total`;
CREATE TABLE `job_apply_work_experience_total`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time` datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `job_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `range_0_1`   int(11)          NOT NULL DEFAULT '0' COMMENT '0-1',
    `range_1_3`   int(11)          NOT NULL DEFAULT '0' COMMENT '1-3',
    `range_3_5`   int(11)          NOT NULL DEFAULT '0' COMMENT '3-5',
    `range_5_10`  int(11)          NOT NULL DEFAULT '0' COMMENT '5-10',
    `range_10`    int(11)          NOT NULL DEFAULT '0' COMMENT '10+',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 481577
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位总投递量表(工作经验统计)';

-- ----------------------------
-- Table structure for job_apply_work_experience_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `job_apply_work_experience_total_daily`;
CREATE TABLE `job_apply_work_experience_total_daily`
(
    `id`         int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date`   date             NOT NULL COMMENT '时间格式',
    `job_id`     int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `range_0_1`  int(11)          NOT NULL DEFAULT '0' COMMENT '0-1',
    `range_1_3`  int(11)          NOT NULL DEFAULT '0' COMMENT '1-3',
    `range_3_5`  int(11)          NOT NULL DEFAULT '0' COMMENT '3-5',
    `range_5_10` int(11)          NOT NULL DEFAULT '0' COMMENT '5-10',
    `range_10`   int(11)          NOT NULL DEFAULT '0' COMMENT '10+',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_ad` (`job_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2336408
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位日投递量表(工作经验统计)';

-- ----------------------------
-- Table structure for job_auto_classify_log
-- ----------------------------
DROP TABLE IF EXISTS `job_auto_classify_log`;
CREATE TABLE `job_auto_classify_log`
(
    `id`                     int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`               datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`                 tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `job_id`                 int(11)       NOT NULL DEFAULT '0' COMMENT '职位id',
    `before_home_column_ids` varchar(512)  NOT NULL DEFAULT '' COMMENT '自动分配前所属栏目ids',
    `after_home_column_ids`  varchar(1024) NOT NULL DEFAULT '' COMMENT '自动分配后所属栏目ids',
    `remark`                 text COMMENT '职位分配的过程记录',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1122348
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for job_boshihou
-- ----------------------------
DROP TABLE IF EXISTS `job_boshihou`;
CREATE TABLE `job_boshihou`
(
    `id`     int(11) unsigned NOT NULL AUTO_INCREMENT,
    `job_id` int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 91984
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for job_category_relation
-- ----------------------------
DROP TABLE IF EXISTS `job_category_relation`;
CREATE TABLE `job_category_relation`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `job_id`          int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `category_id`     int(11)          NOT NULL COMMENT '类型ID',
    `level`           tinyint(1)       NOT NULL COMMENT '职位类型等级跟category level字段',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_category_id` (`category_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 34553168
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位类型中间表';

-- ----------------------------
-- Table structure for job_click_ip_total
-- ----------------------------
DROP TABLE IF EXISTS `job_click_ip_total`;
CREATE TABLE `job_click_ip_total`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `update_time` datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `job_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `ip`          int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'ip',
    `total`       int(11)          NOT NULL DEFAULT '0' COMMENT '点击量',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_jid_ip` (`job_id`, `ip`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_ip` (`ip`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 45281074
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位总阅读量表(IP统计)';

-- ----------------------------
-- Table structure for job_click_ip_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `job_click_ip_total_daily`;
CREATE TABLE `job_click_ip_total_daily`
(
    `id`       int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date` date             NOT NULL COMMENT '时间格式',
    `job_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `ip`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'ip',
    `total`    int(11)          NOT NULL DEFAULT '0' COMMENT '点击量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_ip_ad` (`job_id`, `ip`, `add_date`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_ip` (`ip`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 47214821
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位日阅读量表(IP统计)';

-- ----------------------------
-- Table structure for job_click_log
-- ----------------------------
DROP TABLE IF EXISTS `job_click_log`;
CREATE TABLE `job_click_log`
(
    `id`           int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `member_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '点击的会员id',
    `job_id`       int(11)          NOT NULL DEFAULT '0' COMMENT '职位的id',
    `source`       tinyint(1)       NOT NULL DEFAULT '1' COMMENT '1:pc,2:h5',
    `useragent`    varchar(2048)    NOT NULL DEFAULT '' COMMENT '请求表头信息',
    `user_cookies` varchar(64)      NOT NULL DEFAULT '' COMMENT '用户的cookie',
    `ip`           int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作ip',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_member_id` (`member_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 57774998
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for job_click_total_daily
-- ----------------------------
DROP TABLE IF EXISTS `job_click_total_daily`;
CREATE TABLE `job_click_total_daily`
(
    `id`       int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `add_date` date             NOT NULL COMMENT '时间格式',
    `job_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `total`    int(11)          NOT NULL DEFAULT '0' COMMENT '点击量',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_jid_ad` (`job_id`, `add_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 23741897
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位日阅读量表';

-- ----------------------------
-- Table structure for job_click_update_config
-- ----------------------------
DROP TABLE IF EXISTS `job_click_update_config`;
CREATE TABLE `job_click_update_config`
(
    `id`                          int(5) unsigned NOT NULL AUTO_INCREMENT,
    `class_name`                  varchar(255)    NOT NULL DEFAULT '' COMMENT '脚本类名称',
    `update_time`                 datetime        NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '脚本更新时间',
    `start_id`                    int(11)         NOT NULL DEFAULT '0' COMMENT '执行开始节点',
    `end_id`                      int(11)         NOT NULL DEFAULT '0' COMMENT '执行结束节点',
    `limit_max`                   int(11)         NOT NULL DEFAULT '0' COMMENT '最大limit数量',
    `real_execute_status`         tinyint(1)      NOT NULL DEFAULT '0' COMMENT '实时脚本执行状态,0:未完成,1:已完成',
    `history_execute_status`      tinyint(1)      NOT NULL DEFAULT '0' COMMENT '历史脚本执行状态,0:未完成,1:已完成',
    `history_log_complete_status` tinyint(1)      NOT NULL DEFAULT '0' COMMENT '历史数据完成状态,0:未完成,1:已完成',
    `real_fail_msg`               text COMMENT '实时脚本错误信息',
    `history_fail_msg`            text COMMENT '历史脚本错误信息',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位阅读量统计同步配置表';

-- ----------------------------
-- Table structure for job_collect
-- ----------------------------
DROP TABLE IF EXISTS `job_collect`;
CREATE TABLE `job_collect`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `member_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '会员id',
    `job_id`      int(11)    NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`),
    KEY `idx_update_time` (`update_time`) USING BTREE,
    KEY `idx_member_id` (`member_id`),
    KEY `idx_job_id` (`job_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 784036
  DEFAULT CHARSET = utf8 COMMENT ='职位收藏表';

-- ----------------------------
-- Table structure for job_column
-- ----------------------------
DROP TABLE IF EXISTS `job_column`;
CREATE TABLE `job_column`
(
    `id`        int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`  datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `column_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '栏目id',
    `job_id`    int(11) unsigned NOT NULL DEFAULT '0' COMMENT '职位id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_column_id` (`column_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 90332468
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for job_contact
-- ----------------------------
DROP TABLE IF EXISTS `job_contact`;
CREATE TABLE `job_contact`
(
    `id`                     int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`               datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`            datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `job_id`                 int(11)  NOT NULL DEFAULT '0' COMMENT '职位ID',
    `announcement_id`        int(11)  NOT NULL DEFAULT '0' COMMENT '公告ID',
    `company_member_info_id` int(11)  NOT NULL DEFAULT '0' COMMENT '关联联系人账号ID',
    `company_id`             int(11)  NOT NULL DEFAULT '0' COMMENT '联系人隶属单位ID',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_announcement_id` (`announcement_id`),
    KEY `idx_company_member_info_id` (`company_member_info_id`),
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 295464
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位联系人中间表';

-- ----------------------------
-- Table structure for job_contact_synergy
-- ----------------------------
DROP TABLE IF EXISTS `job_contact_synergy`;
CREATE TABLE `job_contact_synergy`
(
    `id`                     int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`               datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`            datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `job_id`                 int(11)  NOT NULL DEFAULT '0' COMMENT '职位ID',
    `announcement_id`        int(11)  NOT NULL DEFAULT '0' COMMENT '公告ID',
    `company_member_info_id` int(11)  NOT NULL DEFAULT '0' COMMENT '关联联系人账号ID',
    `company_id`             int(11)  NOT NULL DEFAULT '0' COMMENT '联系人隶属单位ID',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_announcement_id` (`announcement_id`),
    KEY `idx_company_member_info_id` (`company_member_info_id`),
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 9776
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位协同联系人';

-- ----------------------------
-- Table structure for job_edit
-- ----------------------------
DROP TABLE IF EXISTS `job_edit`;
CREATE TABLE `job_edit`
(
    `id`              int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`          tinyint(1)  NOT NULL DEFAULT '1' COMMENT '状态，1:在用；2:关闭',
    `job_id`          int(11)     NOT NULL DEFAULT '0' COMMENT '职位id',
    `edit_content`    text        NOT NULL COMMENT '编辑修改内容',
    `editor`          varchar(32) NOT NULL DEFAULT '' COMMENT '编辑人名称',
    `editor_type`     tinyint(1)  NOT NULL DEFAULT '0' COMMENT '编辑类型，1:平台；2:用户',
    `editor_id`       int(11)     NOT NULL DEFAULT '0' COMMENT '用户id',
    `announcement_id` int(11)     NOT NULL DEFAULT '0' COMMENT '关联公告id',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_editor_id` (`editor_id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 54908
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位编辑';

-- ----------------------------
-- Table structure for job_extra
-- ----------------------------
DROP TABLE IF EXISTS `job_extra`;
CREATE TABLE `job_extra`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `job_id`          int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `company_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '单位ID',
    `is_pi`           tinyint(1)       NOT NULL DEFAULT '2' COMMENT '是否PI:1=是,2=不是',
    `is_pay`          tinyint(1)       NOT NULL DEFAULT '2' COMMENT '是否付费单位；1:包含；2:不包含',
    `is_boshihou_pay` tinyint(1)       NOT NULL DEFAULT '2' COMMENT '博士后广告是否付费单位；1:包含；2:不包含',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_is_boshihou_pay` (`is_boshihou_pay`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1758504
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位附属表';

-- ----------------------------
-- Table structure for job_handle_log
-- ----------------------------
DROP TABLE IF EXISTS `job_handle_log`;
CREATE TABLE `job_handle_log`
(
    `id`              int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `ip`              int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作ip',
    `job_id`          int(11)          NOT NULL DEFAULT '0' COMMENT '职位id',
    `handle_type`     varchar(32)      NOT NULL DEFAULT '0' COMMENT '操作类型，1:编辑；2：刷新；3:发布；4:再发布；5:下线；6:审核；7:隐藏；8:显示；9:删除；10:复制',
    `handler_type`    int(11)          NOT NULL DEFAULT '0' COMMENT '用户类型，1:运营平台；2:普通用户',
    `handler_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '操作用户id',
    `handler_name`    varchar(256)     NOT NULL DEFAULT '' COMMENT '操作人名称',
    `handle_before`   text             NOT NULL COMMENT '操作前',
    `handle_after`    text             NOT NULL COMMENT '操作后',
    `announcement_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '公告id',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 137173
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位操作日志';

-- ----------------------------
-- Table structure for job_keywords_preprocess
-- ----------------------------
DROP TABLE IF EXISTS `job_keywords_preprocess`;
CREATE TABLE `job_keywords_preprocess`
(
    `id`          int(11)        NOT NULL AUTO_INCREMENT,
    `keywords`    varchar(255)   NOT NULL DEFAULT '' COMMENT '关键字',
    `job_ids`     varchar(10000) NOT NULL DEFAULT '' COMMENT '关键字查询到的职位id，多个使用英文,隔开',
    `add_time`    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
    `update_time` datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_keywords_education_type` (`keywords`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 7489
  DEFAULT CHARSET = utf8mb4 COMMENT ='关键字预处理查询职位信息';

-- ----------------------------
-- Table structure for job_major_relation
-- ----------------------------
DROP TABLE IF EXISTS `job_major_relation`;
CREATE TABLE `job_major_relation`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `job_id`          int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `major_id`        int(11)          NOT NULL COMMENT '专业ID',
    `level`           tinyint(1)       NOT NULL COMMENT '专业等级跟major level字段',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_major_id` (`major_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 42159528
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位专业中间表';

-- ----------------------------
-- Table structure for job_subscribe
-- ----------------------------
DROP TABLE IF EXISTS `job_subscribe`;
CREATE TABLE `job_subscribe`
(
    `id`               int(11)       NOT NULL AUTO_INCREMENT,
    `add_time`         datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`           tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `job_category_ids` varchar(1024) NOT NULL DEFAULT '' COMMENT '意向职位id，逗号分隔',
    `area_ids`         varchar(1024) NOT NULL DEFAULT '' COMMENT '意向城市id，逗号分隔',
    `education_ids`    varchar(1024) NOT NULL DEFAULT '' COMMENT '学历id，逗号分隔',
    `send_email`       varchar(255)  NOT NULL DEFAULT '' COMMENT '推送邮箱',
    `is_send_email`    tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否推送邮箱（1是  2否）',
    `is_send_wechat`   tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否推送微信（1是  2否）',
    `resume_id`        int(11)       NOT NULL DEFAULT '0' COMMENT '简历id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_resume_id` (`resume_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7060
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位订阅记录表';

-- ----------------------------
-- Table structure for job_subscribe_send_log
-- ----------------------------
DROP TABLE IF EXISTS `job_subscribe_send_log`;
CREATE TABLE `job_subscribe_send_log`
(
    `id`                int(11)       NOT NULL AUTO_INCREMENT,
    `add_time`          datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`            tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `subscribe_id`      int(11)       NOT NULL COMMENT '订阅记录id',
    `subscribe_content` varchar(1024) NOT NULL COMMENT '订阅记录内容',
    `email_log_id`      int(11)       NOT NULL COMMENT '邮件内容',
    `job_ids`           varchar(1024) NOT NULL COMMENT '微信推送内容',
    `resume_id`         int(11)       NOT NULL DEFAULT '0' COMMENT '简历id',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_subscribe_id` (`subscribe_id`) USING BTREE,
    KEY `idx_email_log_id` (`email_log_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 290580
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位订阅发送日志';

-- ----------------------------
-- Table structure for job_temp
-- ----------------------------
DROP TABLE IF EXISTS `job_temp`;
CREATE TABLE `job_temp`
(
    `id`                   int(11)             NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`             datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `member_id`            int(11)             NOT NULL DEFAULT '0' COMMENT '会员id',
    `company_id`           int(11)             NOT NULL DEFAULT '0' COMMENT '企业id',
    `name`                 varchar(256)        NOT NULL DEFAULT '' COMMENT '职位名称',
    `period_date`          datetime            NOT NULL COMMENT '职位有效期',
    `code`                 varchar(32)         NOT NULL DEFAULT '' COMMENT '职位代码',
    `job_category_id`      int(11)             NOT NULL DEFAULT '0' COMMENT '职位类别id',
    `education_type`       int(11)             NOT NULL DEFAULT '0' COMMENT '学历要求类型',
    `major_id`             varchar(512)        NOT NULL DEFAULT '' COMMENT '专业id',
    `nature_type`          int(11)             NOT NULL DEFAULT '0' COMMENT '性质类型',
    `is_negotiable`        tinyint(1)          NOT NULL DEFAULT '0' COMMENT '是否面议：0否，1是',
    `wage_type`            tinyint(1)          NOT NULL DEFAULT '0' COMMENT '薪资类型(1月，2年，3日）',
    `min_wage`             int(11)             NOT NULL DEFAULT '0' COMMENT '薪资最低',
    `max_wage`             int(11)             NOT NULL DEFAULT '0' COMMENT '薪资最高',
    `experience_type`      tinyint(1)          NOT NULL DEFAULT '0' COMMENT '经验要求类型',
    `age_type`             varchar(128)        NOT NULL DEFAULT '' COMMENT '年龄要求类型',
    `min_age`              tinyint(1)          NOT NULL DEFAULT '0' COMMENT '年龄要求最低',
    `max_age`              tinyint(1)          NOT NULL DEFAULT '0' COMMENT '年龄要求最高',
    `title_type`           tinyint(1)          NOT NULL DEFAULT '0' COMMENT '职称类型',
    `political_type`       tinyint(1)          NOT NULL DEFAULT '0' COMMENT '政治面貌类型',
    `abroad_type`          tinyint(1)          NOT NULL DEFAULT '0' COMMENT '海外经历类型',
    `amount`               varchar(128)        NOT NULL DEFAULT '0' COMMENT '招聘人数',
    `department`           varchar(32)         NOT NULL DEFAULT '' COMMENT '用户部门',
    `district_id`          int(11)             NOT NULL DEFAULT '0' COMMENT '工作地点id',
    `province_id`          int(11)             NOT NULL DEFAULT '0' COMMENT '省Id',
    `city_id`              int(11)             NOT NULL DEFAULT '0' COMMENT '市Id',
    `address`              varchar(128)        NOT NULL DEFAULT '' COMMENT '工作地点详细地址',
    `welfare_tag`          varchar(512)        NOT NULL DEFAULT '' COMMENT '福利标签',
    `duty`                 varchar(2048)       NOT NULL DEFAULT '' COMMENT '岗位职责',
    `requirement`          varchar(2048)       NOT NULL DEFAULT '' COMMENT '任职要求',
    `remark`               varchar(2048)       NOT NULL DEFAULT '' COMMENT '其他说明',
    `audit_status`         tinyint(1)          NOT NULL DEFAULT '0' COMMENT '审核状态',
    `offline_type`         tinyint(1)          NOT NULL DEFAULT '0' COMMENT '下线方式 0:无；1:自动下线；2：手动下线',
    `announcement_id`      int(11)             NOT NULL DEFAULT '0' COMMENT '公告id',
    `create_type`          tinyint(1)          NOT NULL DEFAULT '1' COMMENT '创建类型，1:修改；2:新增',
    `gender_type`          tinyint(1)          NOT NULL DEFAULT '0' COMMENT '性别要求类型 0:不限；1:男；2:女',
    `is_temp`              tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '判别临时数据字段',
    `apply_type`           varchar(32)         NOT NULL DEFAULT '' COMMENT '应聘方式(1电子邮件xxxx',
    `apply_address`        varchar(600)        NOT NULL DEFAULT '' COMMENT '投递地址',
    `job_id`               int(11) unsigned    NOT NULL DEFAULT '0',
    `delivery_limit_type`  varchar(60)         NOT NULL DEFAULT '' COMMENT '投递限制 1学历 2应聘材料',
    `delivery_type`        tinyint(1)          NOT NULL DEFAULT '0' COMMENT '投递类型1=站外投递,2=站内投递',
    `delivery_way`         tinyint(1)          NOT NULL DEFAULT '0' COMMENT '投递方式 1平台投递 2邮箱投递 3网址投递',
    `extra_notify_address` varchar(255)        NOT NULL DEFAULT '' COMMENT '投递通知地址',
    `establishment_type`   varchar(60)         NOT NULL DEFAULT '' COMMENT '职位编制类型 对应字典表类型31',
    `is_establishment`     tinyint(2)          NOT NULL DEFAULT '2' COMMENT '是否有编制 1有编制 2无编制',
    `contact_id`           int(11)             NOT NULL DEFAULT '0' COMMENT '联系人ID',
    `contact_synergy_id`   varchar(60)         NOT NULL DEFAULT '' COMMENT '协同账号ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 10715023
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位临时表，用于公告发布职位临时存储数据';

-- ----------------------------
-- Table structure for job_top_config
-- ----------------------------
DROP TABLE IF EXISTS `job_top_config`;
CREATE TABLE `job_top_config`
(
    `id`                int(10)      NOT NULL AUTO_INCREMENT,
    `add_time`          datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `update_time`       datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `is_delete`         tinyint(1)   NOT NULL DEFAULT '2' COMMENT '状态（1移除，2正常）',
    `job_id`            int(10)      NOT NULL DEFAULT '0' COMMENT '职位id',
    `company_id`        int(10)      NOT NULL DEFAULT '0' COMMENT '单位ID',
    `date`              date         NOT NULL DEFAULT '0000-00-00' COMMENT '置顶日期',
    `status`            tinyint(4)   NOT NULL DEFAULT '0' COMMENT '置顶状态（  1置顶中  2未开始   3暂停置顶  9已结束）',
    `type`              tinyint(4)   NOT NULL DEFAULT '0' COMMENT '置顶类型（1常规 2搜索）',
    `job_category_id`   int(10)      NOT NULL DEFAULT '0' COMMENT '搜索置顶职位类型id',
    `area_id`           int(10)      NOT NULL DEFAULT '0' COMMENT '搜索置顶工作地点id（省或者市）',
    `sort`              int(10)      NOT NULL DEFAULT '0' COMMENT '排序',
    `remark`            varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `admin_id`          int(10)      NOT NULL DEFAULT '0' COMMENT '创建人id',
    `job_name`          varchar(255) NOT NULL DEFAULT '' COMMENT '职位名称快照',
    `job_category_name` varchar(255) NOT NULL DEFAULT '' COMMENT '职位类型名称快照',
    `area_name`         varchar(255) NOT NULL DEFAULT '' COMMENT '地点名称快照',
    `is_run`            tinyint(1)   NOT NULL DEFAULT '2' COMMENT '是否正在置顶中(1是  2否)',
    `run_time`          datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '实际（第一次）开始置顶时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_job_category_id` (`job_category_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_date` (`date`) USING BTREE,
    KEY `idx_area_id` (`area_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 17398
  DEFAULT CHARSET = utf8mb4 COMMENT ='置顶配置表';

-- ----------------------------
-- Table structure for job_top_config_log
-- ----------------------------
DROP TABLE IF EXISTS `job_top_config_log`;
CREATE TABLE `job_top_config_log`
(
    `id`           int(10)      NOT NULL AUTO_INCREMENT,
    `add_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`       tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `job_id`       int(10)      NOT NULL DEFAULT '0' COMMENT '职位id',
    `date`         varchar(255) NOT NULL DEFAULT '' COMMENT '置顶时间，逗号分隔',
    `type_content` varchar(255) NOT NULL DEFAULT '' COMMENT '置顶类型内容',
    `sort`         int(10)      NOT NULL DEFAULT '0' COMMENT '置顶排序',
    `remark`       varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `admin_id`     int(10)      NOT NULL COMMENT '创建人id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 11704
  DEFAULT CHARSET = utf8mb4 COMMENT ='置顶配置日志表';

-- ----------------------------
-- Table structure for job_welfare_relation
-- ----------------------------
DROP TABLE IF EXISTS `job_welfare_relation`;
CREATE TABLE `job_welfare_relation`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `announcement_id` int(11)          NOT NULL DEFAULT '0' COMMENT '公告ID',
    `job_id`          int(11)          NOT NULL DEFAULT '0' COMMENT '职位ID',
    `welfare_id`      int(11)          NOT NULL COMMENT '福利ID',
    PRIMARY KEY (`id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_welfare_id` (`welfare_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 17740527
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位专业中间表';

-- ----------------------------
-- Table structure for log
-- ----------------------------
DROP TABLE IF EXISTS `log`;
CREATE TABLE `log`
(
    `id`       bigint(20) NOT NULL AUTO_INCREMENT,
    `level`    int(11)      DEFAULT NULL,
    `category` varchar(255) DEFAULT NULL,
    `log_time` double       DEFAULT NULL,
    `prefix`   text,
    `message`  text,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_log_level` (`level`) USING BTREE,
    KEY `idx_log_category` (`category`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 564442
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for major
-- ----------------------------
DROP TABLE IF EXISTS `major`;
CREATE TABLE `major`
(
    `id`                 int(11)     NOT NULL AUTO_INCREMENT,
    `add_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`             tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态',
    `parent_id`          int(11)     NOT NULL DEFAULT '0' COMMENT '父id',
    `name`               varchar(64) NOT NULL DEFAULT '' COMMENT '名称',
    `sort`               int(11)     NOT NULL DEFAULT '0' COMMENT '排序',
    `level`              tinyint(1)  NOT NULL DEFAULT '0' COMMENT '等级',
    `code`               varchar(64) NOT NULL DEFAULT '' COMMENT '代码',
    `major_specialty_id` int(11)     NOT NULL DEFAULT '0' COMMENT '专业领域ID',
    `spell`              varchar(64) NOT NULL DEFAULT '' COMMENT '拼音',
    PRIMARY KEY (`id`),
    KEY `idx_code` (`code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_sort` (`sort`),
    KEY `idx_major_specialty_id` (`major_specialty_id`) USING BTREE,
    KEY `idx_spell` (`spell`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 824
  DEFAULT CHARSET = utf8mb4 COMMENT ='学科专业';

-- ----------------------------
-- Table structure for major_ai_dictionary
-- ----------------------------
DROP TABLE IF EXISTS `major_ai_dictionary`;
CREATE TABLE `major_ai_dictionary`
(
    `id`       int(11)      NOT NULL AUTO_INCREMENT,
    `add_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`   tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `name`     varchar(256) NOT NULL DEFAULT '' COMMENT '名字(需要识别的文案)',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2344
  DEFAULT CHARSET = utf8mb4 COMMENT ='学科智能识别字典';

-- ----------------------------
-- Table structure for major_ai_dictionary_relationship
-- ----------------------------
DROP TABLE IF EXISTS `major_ai_dictionary_relationship`;
CREATE TABLE `major_ai_dictionary_relationship`
(
    `id`            int(11)  NOT NULL AUTO_INCREMENT,
    `add_time`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `dictionary_id` int(11)  NOT NULL DEFAULT '0' COMMENT 'major_ai_dictionary的主键id',
    `major_id`      int(11)  NOT NULL DEFAULT '0' COMMENT '识别的学科id',
    PRIMARY KEY (`id`),
    KEY `idx_dictionary_id` (`dictionary_id`) USING BTREE,
    KEY `idx_major_id` (`major_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2721
  DEFAULT CHARSET = utf8mb4 COMMENT ='学科智能识别字典关联';

-- ----------------------------
-- Table structure for major_ai_dictionary_synonym
-- ----------------------------
DROP TABLE IF EXISTS `major_ai_dictionary_synonym`;
CREATE TABLE `major_ai_dictionary_synonym`
(
    `id`                     int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`               datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`                 tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `name`                   varchar(256) NOT NULL DEFAULT '' COMMENT '名字(需要识别的文案)',
    `major_ai_dictionary_id` int(11)      NOT NULL DEFAULT '0' COMMENT '学科智能识别字典id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_major_ai_dictionary_id` (`major_ai_dictionary_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 109
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for major_ai_log
-- ----------------------------
DROP TABLE IF EXISTS `major_ai_log`;
CREATE TABLE `major_ai_log`
(
    `id`               int(11)       NOT NULL AUTO_INCREMENT,
    `add_time`         datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `text`             varchar(2048) NOT NULL DEFAULT '' COMMENT '识别文本',
    `dictionary_ids`   varchar(512)  NOT NULL DEFAULT '0' COMMENT 'major_ai_dictionary的主键id',
    `type`             tinyint(1)    NOT NULL DEFAULT '0' COMMENT '单位类型1:批量导入,2:手动识别',
    `dictionary_names` varchar(4096) NOT NULL DEFAULT '' COMMENT '名字(需要识别的文案)',
    `major_ids`        varchar(512)  NOT NULL DEFAULT '' COMMENT '识别的学科ids逗号隔开',
    `major_names`      varchar(1024) NOT NULL DEFAULT '' COMMENT '识别的学科名字s逗号隔开',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 528233
  DEFAULT CHARSET = utf8mb4 COMMENT ='学科智能识别日志';

-- ----------------------------
-- Table structure for major_specialty
-- ----------------------------
DROP TABLE IF EXISTS `major_specialty`;
CREATE TABLE `major_specialty`
(
    `id`                    int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`              datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`                tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `name`                  varchar(64)  NOT NULL DEFAULT '' COMMENT '名称',
    `sort`                  int(11)      NOT NULL DEFAULT '0' COMMENT '排序',
    `sort_announcement_job` int(11)      NOT NULL DEFAULT '0' COMMENT '排序-公告职位',
    `sort_home`             int(11)      NOT NULL DEFAULT '0' COMMENT '排序-首页',
    `spell`                 varchar(64)  NOT NULL DEFAULT '' COMMENT '拼音',
    `icon`                  varchar(255) NOT NULL DEFAULT '' COMMENT '领域图标',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_sort` (`sort`),
    KEY `idx_sort_announcement_job` (`sort_announcement_job`) USING BTREE,
    KEY `idx_sort_home` (`sort_home`) USING BTREE,
    KEY `idx_spell` (`spell`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 14
  DEFAULT CHARSET = utf8mb4 COMMENT ='学科专业领域';

-- ----------------------------
-- Table structure for member
-- ----------------------------
DROP TABLE IF EXISTS `member`;
CREATE TABLE `member`
(
    `id`                    int(11)          NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`              datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`                tinyint(1)       NOT NULL DEFAULT '0' COMMENT '状态0删除,-1禁止登陆,1正常,9等待审核,-2非合作帐号',
    `type`                  tinyint(1)       NOT NULL DEFAULT '0' COMMENT '会员类型(1个人/2企业)',
    `username`              varchar(32)      NOT NULL DEFAULT '' COMMENT '用户名',
    `password`              varchar(64)      NOT NULL DEFAULT '' COMMENT '密码',
    `email`                 varchar(256)     NOT NULL DEFAULT '' COMMENT '邮箱',
    `mobile_code`           varchar(16)      NOT NULL DEFAULT '' COMMENT '手机号区号',
    `mobile`                varchar(16)      NOT NULL DEFAULT '' COMMENT '手机号',
    `avatar`                varchar(128)     NOT NULL DEFAULT '' COMMENT '头像',
    `last_login_time`       datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '最近一次登录时间',
    `last_login_ip`         int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最近一次登录的ip',
    `email_register_status` tinyint(1)       NOT NULL DEFAULT '-1' COMMENT '邮箱登录状态（-1禁止登录，1正常）',
    `source_type`           tinyint(1)       NOT NULL COMMENT '注册来源（1web端  2H5）',
    `last_active_time`      datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '最近活跃时间',
    `company_member_type`   tinyint(1)       NOT NULL DEFAULT '0' COMMENT '单位账号类型 0主账号 1子账号类型',
    `is_chat`               tinyint(1)       NOT NULL DEFAULT '1' COMMENT '是否开启直聊开关（1:是；2:否）',
    `is_chat_window`        tinyint(1)       NOT NULL DEFAULT '2' COMMENT '是否打开聊天窗 1是 2否',
    `is_greeting`           tinyint(1)       NOT NULL DEFAULT '2' COMMENT '是否启用招呼语 1启用 2关闭',
    `greeting_type`         tinyint(1)       NOT NULL DEFAULT '1' COMMENT '默认招呼语类型 1系统 2自定义',
    `greeting_default_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '默认招呼语ID',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_mobile` (`mobile`),
    KEY `idx_email` (`email`),
    KEY `idx_last_active_time` (`last_active_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1282736
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员';

-- ----------------------------
-- Table structure for member_action_log
-- ----------------------------
DROP TABLE IF EXISTS `member_action_log`;
CREATE TABLE `member_action_log`
(
    `id`          int(11)          NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `member_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '会员id',
    `member_type` tinyint(1)       NOT NULL DEFAULT '0' COMMENT '会员类型(1个人/2企业)',
    `ip`          int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最近一次登录的ip',
    `platform`    varchar(32)      NOT NULL DEFAULT '' COMMENT 'pc,h5等等',
    `content`     varchar(128)     NOT NULL DEFAULT '' COMMENT '内容',
    `is_login`    tinyint(1)       NOT NULL DEFAULT '0' COMMENT '是否登录',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_ip` (`ip`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 14115405
  DEFAULT CHARSET = utf8;

-- ----------------------------
-- Table structure for member_address
-- ----------------------------
DROP TABLE IF EXISTS `member_address`;
CREATE TABLE `member_address`
(
    `id`          int(11)        NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime       NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)     NOT NULL DEFAULT '0' COMMENT '状态',
    `member_id`   int(11)        NOT NULL DEFAULT '0' COMMENT '会员id',
    `member_type` tinyint(1)     NOT NULL DEFAULT '0' COMMENT '会员类型(1个人/2企业)',
    `lat`         decimal(32, 8) NOT NULL DEFAULT '0.00000000' COMMENT '维度',
    `lng`         decimal(32, 8) NOT NULL DEFAULT '0.00000000' COMMENT '经度',
    `district_id` int(11)        NOT NULL DEFAULT '0' COMMENT '区id',
    `province_id` int(11)        NOT NULL DEFAULT '0' COMMENT '省Id',
    `city_id`     int(11)        NOT NULL DEFAULT '0' COMMENT '市Id',
    `detail`      varchar(512)   NOT NULL DEFAULT '' COMMENT '详细地址',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 116421
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员地址';

-- ----------------------------
-- Table structure for member_bind
-- ----------------------------
DROP TABLE IF EXISTS `member_bind`;
CREATE TABLE `member_bind`
(
    `id`           int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`       tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `member_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `type`         tinyint(1)   NOT NULL DEFAULT '1' COMMENT '类型1(微信公众号)',
    `openid`       varchar(64)  NOT NULL DEFAULT '' COMMENT 'openid',
    `unionid`      varchar(64)  NOT NULL DEFAULT '' COMMENT 'unionid',
    `avatar`       varchar(256) NOT NULL DEFAULT '' COMMENT '头像url',
    `is_subscribe` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否关注0否1是',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_openid` (`openid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员绑定';

-- ----------------------------
-- Table structure for member_message
-- ----------------------------
DROP TABLE IF EXISTS `member_message`;
CREATE TABLE `member_message`
(
    `id`                int(11)             NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`          datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `member_id`         int(11)             NOT NULL DEFAULT '0' COMMENT '会员id',
    `type`              tinyint(1)          NOT NULL DEFAULT '0' COMMENT '单位类型1:系统消息,2:应聘消息',
    `title`             varchar(512)        NOT NULL DEFAULT '' COMMENT '消息标题',
    `content`           varchar(512)        NOT NULL DEFAULT '' COMMENT '消息内容',
    `inner_link`        varchar(512)        NOT NULL DEFAULT '' COMMENT '内部链接',
    `inner_link_params` varchar(2048)       NOT NULL DEFAULT '0' COMMENT '内部链接参数',
    `is_read`           tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已读0:未读，1:已读',
    `is_delete`         tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除0:未删除，1:已删除',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4104378
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员消息';

-- ----------------------------
-- Table structure for member_schedule
-- ----------------------------
DROP TABLE IF EXISTS `member_schedule`;
CREATE TABLE `member_schedule`
(
    `id`             int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`         tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `title`          varchar(512) NOT NULL DEFAULT '' COMMENT '主题',
    `content`        varchar(512) NOT NULL DEFAULT '' COMMENT '内容',
    `begin_time`     datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '开始时间',
    `end_time`       datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结束时间',
    `place`          varchar(64)  NOT NULL DEFAULT '' COMMENT '地点',
    `remind_time`    datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '提醒时间',
    `remind_type`    tinyint(1)   NOT NULL DEFAULT '0' COMMENT '提醒时间类型，1:15分钟；2:30分钟；3:45分钟；4:1小时；5:2小时；6:3小时',
    `member_id`      int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `is_need_remind` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否需要提醒',
    `is_remind`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否已经提醒了',
    `type`           tinyint(1)   NOT NULL DEFAULT '0' COMMENT '类型1自主添加的日程,2求职者面试日程,3企业面试日程',
    `main_id`        int(11)      NOT NULL DEFAULT '0' COMMENT '关联其他主表的时候，其他表的id',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_begin_time` (`begin_time`),
    KEY `idx_remind_time` (`remind_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1536
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员日程';

-- ----------------------------
-- Table structure for member_search_log
-- ----------------------------
DROP TABLE IF EXISTS `member_search_log`;
CREATE TABLE `member_search_log`
(
    `id`           int(11)          NOT NULL AUTO_INCREMENT,
    `add_time`     datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '时间',
    `type`         tinyint(1)       NOT NULL DEFAULT '0' COMMENT '搜索入口类型（1、PC职位列表,2、H5职位列表）',
    `member_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '人员的id',
    `ip`           int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'ip地址',
    `user_cookies` varchar(64)      NOT NULL DEFAULT '' COMMENT '用户的cookie',
    `keyword`      varchar(512)     NOT NULL DEFAULT '' COMMENT '搜索的关键字',
    `params`       text COMMENT '参数，json化',
    `url`          varchar(2048)    NOT NULL DEFAULT '' COMMENT 'url地址',
    `user_agent`   varchar(2048)    NOT NULL DEFAULT '' COMMENT '头信息',
    `params_txt`   varchar(4096)    NOT NULL DEFAULT '',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 43735439
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户搜索日志';

-- ----------------------------
-- Table structure for member_suggestion
-- ----------------------------
DROP TABLE IF EXISTS `member_suggestion`;
CREATE TABLE `member_suggestion`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL COMMENT '修改时间',
    `is_delete`   tinyint(1)   NOT NULL DEFAULT '2' COMMENT '删除，1是，2否',
    `member_type` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '用户类型，1:职位；2:企业；3:简历',
    `member_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '用户id',
    `member_name` varchar(32)  NOT NULL DEFAULT '' COMMENT '用户名称',
    `content`     varchar(900) NOT NULL DEFAULT '' COMMENT '建议反馈内容',
    `is_handle`   tinyint(1)   NOT NULL DEFAULT '2' COMMENT '是否处理，1是，2否',
    `handle_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 6
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户建议反馈';

-- ----------------------------
-- Table structure for new_resume_activity_accept
-- ----------------------------
DROP TABLE IF EXISTS `new_resume_activity_accept`;
CREATE TABLE `new_resume_activity_accept`
(
    `id`                    int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`              datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间(注册时间)',
    `update_time`           datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`                tinyint(1)       NOT NULL DEFAULT '9' COMMENT '状态:1:达标,-1:已过期,9等待发放',
    `resume_id`             int(11)          NOT NULL DEFAULT '0' COMMENT '受邀人简历ID',
    `share_resume_id`       int(11)          NOT NULL DEFAULT '0' COMMENT '分享人简历ID（融于方便查询）',
    `share_id`              int(11)          NOT NULL DEFAULT '0' COMMENT '分享ID',
    `success_time`          datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '成功达标时间',
    `share_issue_status`    tinyint(1)       NOT NULL DEFAULT '9' COMMENT '发放奖励分享者状态:1:已发放,9:待发放，-1:发放失败',
    `accept_issue_status`   tinyint(1)       NOT NULL DEFAULT '9' COMMENT '发放奖励接收者状态:1:已发放,9:待发放，-1:发放失败',
    `share_issue_reason`    varchar(256)     NOT NULL DEFAULT '' COMMENT '分享者发放奖励原因',
    `accept_issue_reason`   varchar(256)     NOT NULL DEFAULT '' COMMENT '接受者发放奖励原因',
    `share_issue_order_id`  int(11)          NOT NULL DEFAULT '0' COMMENT '分享者发放奖励对应订单id',
    `accept_issue_order_id` int(11)          NOT NULL DEFAULT '0' COMMENT '接受者发放奖励对应订单id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_share_resume_id` (`share_resume_id`) USING BTREE,
    KEY `idx_share_id` (`share_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 6747
  DEFAULT CHARSET = utf8mb4 COMMENT ='拉新活动受邀';

-- ----------------------------
-- Table structure for new_resume_activity_share
-- ----------------------------
DROP TABLE IF EXISTS `new_resume_activity_share`;
CREATE TABLE `new_resume_activity_share`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`       datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    `update_time`    datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`         tinyint(1)       NOT NULL DEFAULT '1' COMMENT '状态:1:正常,2:已过期，-1已被关闭',
    `token`          varchar(16)      NOT NULL DEFAULT '' COMMENT '分享的token',
    `resume_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '简历ID',
    `click`          int(11)          NOT NULL DEFAULT '0' COMMENT '访问次数',
    `accept_amount`  int(11)          NOT NULL DEFAULT '0' COMMENT '累积邀请总数（用户注册成功后+1）',
    `reach_amount`   int(11)          NOT NULL DEFAULT '0' COMMENT '累积到达总数（用户达标后+1）',
    `success_amount` int(11)          NOT NULL DEFAULT '0' COMMENT '累积成功总数（发放成功后+1）',
    `qr_code_url`    varchar(255)     NOT NULL DEFAULT '' COMMENT '二维码地址',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_token` (`token`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3261
  DEFAULT CHARSET = utf8mb4 COMMENT ='拉新活动分享';

-- ----------------------------
-- Table structure for news
-- ----------------------------
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news`
(
    `id`                  int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`            datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `creator_id`          int(11)    NOT NULL DEFAULT '0' COMMENT '创建人的id',
    `status`              tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0:未审核,1:通过)',
    `article_id`          int(11)    NOT NULL DEFAULT '0' COMMENT '文章id',
    `use_site_type`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '使用站点类型：0-原站点，1-高才博后',
    `first_use_site_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '首次发布站点类型：0-原站点，1-高才博后，发布后不可更改',
    PRIMARY KEY (`id`),
    KEY `idx_creator_id` (`creator_id`),
    KEY `idx_article_id` (`article_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5235
  DEFAULT CHARSET = utf8mb4 COMMENT ='资讯表';

-- ----------------------------
-- Table structure for news_collect
-- ----------------------------
DROP TABLE IF EXISTS `news_collect`;
CREATE TABLE `news_collect`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `member_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '会员id',
    `news_id`     int(11)    NOT NULL DEFAULT '0' COMMENT '资讯id',
    PRIMARY KEY (`id`),
    KEY `idx_ information_id` (`news_id`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_update_time` (`update_time`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 748
  DEFAULT CHARSET = utf8mb4 COMMENT ='收藏资讯表';

-- ----------------------------
-- Table structure for news_handle_log
-- ----------------------------
DROP TABLE IF EXISTS `news_handle_log`;
CREATE TABLE `news_handle_log`
(
    `id`            int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `ip`            int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作ip',
    `news_id`       int(11)          NOT NULL DEFAULT '0' COMMENT '资讯id',
    `article_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '文章id',
    `title`         varchar(32)      NOT NULL DEFAULT '' COMMENT '资讯标题',
    `handle_type`   tinyint(1)       NOT NULL DEFAULT '0' COMMENT '操作类型，1:编辑；2：复制；3:移动；4、审核；5:删除',
    `handler_type`  int(11)          NOT NULL DEFAULT '0' COMMENT '用户类型，1:运营平台；2:普通用户',
    `handler_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '操作用户id',
    `handler_name`  varchar(32)      NOT NULL DEFAULT '' COMMENT '操作人名称',
    `handle_before` text             NOT NULL COMMENT '操作前',
    `handle_after`  text             NOT NULL COMMENT '操作后',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_news_id` (`news_id`) USING BTREE,
    KEY `idx_article_id` (`article_id`) USING BTREE,
    KEY `idx_handler_id` (`handler_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 58
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for off_site_job_apply
-- ----------------------------
DROP TABLE IF EXISTS `off_site_job_apply`;
CREATE TABLE `off_site_job_apply`
(
    `id`                   int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`             datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`               tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态',
    `title`                varchar(256)  NOT NULL DEFAULT '' COMMENT '公告标题',
    `link`                 varchar(512)  NOT NULL DEFAULT '' COMMENT '公告或职位链接',
    `job_name`             varchar(128)  NOT NULL DEFAULT '' COMMENT '投递职位',
    `apply_status`         tinyint(1)    NOT NULL DEFAULT '0' COMMENT '投递状态1:已投递,2:通过初筛,3:邀请面试,4:已面试,5:不合适,6:待应聘',
    `apply_date`           date          NOT NULL DEFAULT '0000-00-00' COMMENT '投递时间',
    `salary`               varchar(64)   NOT NULL DEFAULT '' COMMENT '薪酬待遇',
    `email`                varchar(256)  NOT NULL DEFAULT '' COMMENT '投递邮箱',
    `content`              varchar(1024) NOT NULL DEFAULT '' COMMENT '描述备注',
    `resume_id`            int(11)       NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`            int(11)       NOT NULL DEFAULT '0' COMMENT '会员id',
    `source`               tinyint(1)    NOT NULL DEFAULT '0' COMMENT '投递来源（1本人录入  2网站投递  3报名应聘）',
    `job_id`               int(10)       NOT NULL DEFAULT '0' COMMENT '职位id',
    `announcement_id`      int(10)       NOT NULL DEFAULT '0' COMMENT '公告id',
    `resume_attachment_id` int(10)       NOT NULL DEFAULT '0' COMMENT '附件简历id',
    `stuff_file_id`        varchar(256)  NOT NULL DEFAULT '' COMMENT '材料id（id用逗号分割）',
    `email_log_id`         int(10)       NOT NULL DEFAULT '0' COMMENT '邮件发送记录id',
    `company_tag`          varchar(512)  NOT NULL DEFAULT '' COMMENT '企业标识的tag(逗号隔开的文案)',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_apply_date` (`apply_date`),
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_resume_attachment_id` (`resume_attachment_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1194800
  DEFAULT CHARSET = utf8mb4 COMMENT ='站外应聘';

-- ----------------------------
-- Table structure for order_notify_log
-- ----------------------------
DROP TABLE IF EXISTS `order_notify_log`;
CREATE TABLE `order_notify_log`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `order_no`    varchar(255) NOT NULL DEFAULT '' COMMENT '平台订单号',
    `member_type` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '1求职者,2单位端',
    `notify`      text         NOT NULL COMMENT '回调参数',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1091
  DEFAULT CHARSET = utf8mb4 COMMENT ='支付订单回调日志表';

-- ----------------------------
-- Table structure for pay_transform_buried_point_log
-- ----------------------------
DROP TABLE IF EXISTS `pay_transform_buried_point_log`;
CREATE TABLE `pay_transform_buried_point_log`
(
    `id`           int(10)             NOT NULL AUTO_INCREMENT,
    `add_time`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime            NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `ip`           int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '用户ip',
    `user_cookies` varchar(64)         NOT NULL DEFAULT '' COMMENT '用户的cookie',
    `action_type`  tinyint(4)          NOT NULL DEFAULT '0' COMMENT '动作类型\n',
    `action_id`    int(10)             NOT NULL COMMENT '事件id',
    `params`       text                NOT NULL COMMENT '事件参数',
    `action_url`   varchar(1024)       NOT NULL DEFAULT '' COMMENT '操作页面',
    `member_id`    int(11)             NOT NULL DEFAULT '0' COMMENT '用户id',
    `action_name`  varchar(255)        NOT NULL COMMENT '事件名称',
    `platform`     tinyint(4)          NOT NULL COMMENT '触发端口（1pc端  2小程序）',
    `uuid`         bigint(32) unsigned NOT NULL COMMENT '唯一码',
    `order_no`     varchar(255)        NOT NULL DEFAULT '' COMMENT '平台订单号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 606356
  DEFAULT CHARSET = utf8mb4 COMMENT ='支付转化用户埋点数据表';

-- ----------------------------
-- Table structure for resume
-- ----------------------------
DROP TABLE IF EXISTS `resume`;
CREATE TABLE `resume`
(
    `id`                    int(11)     NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`              datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`                tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态,1正常,-1限制登录',
    `member_id`             int(11)     NOT NULL DEFAULT '0' COMMENT '会员id',
    `audit_status`          tinyint(1)  NOT NULL DEFAULT '0' COMMENT '审核状态,-1不通过,1通过,9待审核',
    `name`                  varchar(32) NOT NULL DEFAULT '' COMMENT '名字',
    `gender`                tinyint(1)  NOT NULL DEFAULT '1' COMMENT '性别,1男,2女',
    `birthday`              date        NOT NULL DEFAULT '0000-00-00' COMMENT '生日',
    `residence`             varchar(32) NOT NULL DEFAULT '' COMMENT '现居住地',
    `height`                varchar(32) NOT NULL DEFAULT '' COMMENT '身高',
    `marriage`              tinyint(1)  NOT NULL DEFAULT '2' COMMENT '婚否(1已，2否）',
    `education`             tinyint(1)  NOT NULL DEFAULT '0' COMMENT '学历',
    `enter_job_time`        tinyint(1)  NOT NULL DEFAULT '0' COMMENT '可入职时间',
    `native_place_area_id`  varchar(32) NOT NULL DEFAULT '' COMMENT '籍贯',
    `id_card`               varchar(32) NOT NULL DEFAULT '' COMMENT '身份证号',
    `refresh_time`          datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '刷新时间',
    `click`                 int(11)     NOT NULL DEFAULT '0' COMMENT '查看次数',
    `advantage`             text COMMENT '个人优势',
    `political_status_id`   tinyint(1)  NOT NULL DEFAULT '0' COMMENT '政治面貌',
    `english_name`          varchar(32) NOT NULL DEFAULT '' COMMENT '英文名',
    `nation_id`             tinyint(1)  NOT NULL DEFAULT '0' COMMENT '民族',
    `title_id`              varchar(64) NOT NULL DEFAULT '0' COMMENT '职称id',
    `household_register_id` int(10)     NOT NULL DEFAULT '0' COMMENT '户籍/国籍id',
    `arrive_date_type`      tinyint(1)  NOT NULL DEFAULT '0' COMMENT '到岗时间类型(-1为自定义)',
    `arrive_date`           date        NOT NULL DEFAULT '0000-00-00' COMMENT '到岗时间(当类型为自定义的时候填写)',
    `work_status`           tinyint(1)  NOT NULL DEFAULT '0' COMMENT '求职状态(1在职，2离职)',
    `is_project_school`     tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否985/211',
    `age`                   tinyint(1)  NOT NULL DEFAULT '0' COMMENT '年龄',
    `work_experience`       int(11)     NOT NULL DEFAULT '0' COMMENT '工作经验（年）',
    `last_apply_job_time`   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后投递时间',
    `last_education_id`     int(11)     NOT NULL DEFAULT '0' COMMENT '最高教育经历id\n',
    `top_education_code`    tinyint(1)  NOT NULL DEFAULT '0' COMMENT '最高学历code',
    `last_work_id`          int(11)     NOT NULL DEFAULT '0' COMMENT '最近工作经历id',
    `birthday_code`         char(4)     NOT NULL DEFAULT '' COMMENT '生日月日代码(更新生日时同步)',
    `work_begin_date_code`  char(4)     NOT NULL DEFAULT '' COMMENT '开始工作时间月日代码（更新工作经历时同步）',
    `last_update_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '简历的最后更新时间',
    `complete`              tinyint(3)  NOT NULL DEFAULT '0' COMMENT '简历完整度',
    `vip_type`              tinyint(1)  NOT NULL DEFAULT '0' COMMENT '会员类型,0:普通用户,1:生效会员,-1:过期会员',
    `vip_level`             tinyint(2)  NOT NULL DEFAULT '0' COMMENT '会员等级 0普通 1黄金 2钻石',
    `vip_begin_time`        datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '会员生效时间',
    `vip_expire_time`       datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '会员过期时间',
    `identity_type`         tinyint(1)  NOT NULL DEFAULT '-1' COMMENT '身份类型（-1缺失，1职场人，2应届生）',
    `begin_work_date`       date        NOT NULL DEFAULT '0000-00-00' COMMENT '参加工作时间',
    `uuid`                  varchar(64) NOT NULL DEFAULT '' COMMENT 'uuid',
    `is_abroad`             tinyint(2)  NOT NULL DEFAULT '2' COMMENT '是否海外经历,1是,2否',
    `is_postdoc`            tinyint(1)  NOT NULL DEFAULT '2' COMMENT '是否有博士后经历,1是,2否',
    `is_resume_library`     tinyint(1)  NOT NULL DEFAULT '2' COMMENT '是否在人才库  1是 2否',
    `resume_type`           tinyint(2)  NOT NULL DEFAULT '3' COMMENT '简历类型  1精英简历 2优质简历 3普通简历',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_id_card` (`id_card`),
    KEY `idx_refresh_time` (`refresh_time`),
    KEY `idx_birthday_code` (`birthday`) USING BTREE,
    KEY `idx_work_begin_date_code` (`work_begin_date_code`),
    KEY `idx_uuid` (`uuid`),
    KEY `idx_top_education_code` (`top_education_code`),
    KEY `idx_work_experience` (`work_experience`),
    KEY `idx_household_register_id` (`household_register_id`),
    KEY `idx_age` (`age`),
    KEY `idx_complete` (`complete`),
    KEY `idx_is_abroad` (`is_abroad`),
    KEY `idx_last_education_id` (`last_education_id`),
    KEY `idx_is_resume_library` (`is_resume_library`),
    KEY `idx_is_postdoc` (`is_postdoc`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1160499
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员在线简历';

-- ----------------------------
-- Table structure for resume_academic_book
-- ----------------------------
DROP TABLE IF EXISTS `resume_academic_book`;
CREATE TABLE `resume_academic_book`
(
    `id`             int(11)     NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`         tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`      int(11)     NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`      int(11)     NOT NULL DEFAULT '0' COMMENT '会员id',
    `name`           varchar(64) NOT NULL COMMENT '著作名称/出版社名称',
    `publish_date`   date        NOT NULL DEFAULT '0000-00-00' COMMENT '出版日期',
    `words`          varchar(32) NOT NULL DEFAULT '' COMMENT '字（万）',
    `publish_amount` varchar(32) NOT NULL DEFAULT '' COMMENT '发行数量',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 13319
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历学术专著';

-- ----------------------------
-- Table structure for resume_academic_page
-- ----------------------------
DROP TABLE IF EXISTS `resume_academic_page`;
CREATE TABLE `resume_academic_page`
(
    `id`               int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`           tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`        int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`        int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `title`            varchar(256) NOT NULL DEFAULT '' COMMENT '题目',
    `serial_number`    varchar(128) NOT NULL DEFAULT '' COMMENT '刊物名/卷（期）号',
    `publish_date`     date         NOT NULL DEFAULT '0000-00-00' COMMENT '发表日期',
    `record_situation` varchar(128) NOT NULL DEFAULT '' COMMENT '收录情况',
    `position`         varchar(128) NOT NULL DEFAULT '' COMMENT '本人轮次',
    `impact_factor`    varchar(128) NOT NULL DEFAULT '' COMMENT '影响因子',
    `description`      text COMMENT '描述',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 400083
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历学术论文';

-- ----------------------------
-- Table structure for resume_academic_patent
-- ----------------------------
DROP TABLE IF EXISTS `resume_academic_patent`;
CREATE TABLE `resume_academic_patent`
(
    `id`                 int(11)     NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`             tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`          int(11)     NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`          int(11)     NOT NULL DEFAULT '0' COMMENT '会员id',
    `name`               varchar(64) NOT NULL DEFAULT '' COMMENT '名称',
    `number`             varchar(64) NOT NULL DEFAULT '' COMMENT '刊物名/卷(期)号',
    `authorization_date` date        NOT NULL DEFAULT '0000-00-00' COMMENT '授权日期',
    `finish_status`      varchar(32) NOT NULL DEFAULT '' COMMENT '完成状态',
    `position`           varchar(32) NOT NULL DEFAULT '' COMMENT '本人位次',
    `description`        text COMMENT '论文描述',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 62264
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历学术专利';

-- ----------------------------
-- Table structure for resume_academic_reward
-- ----------------------------
DROP TABLE IF EXISTS `resume_academic_reward`;
CREATE TABLE `resume_academic_reward`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `obtain_date` date         NOT NULL DEFAULT '0000-00-00' COMMENT '获得时间',
    `name`        varchar(128) NOT NULL DEFAULT '' COMMENT '获奖名称',
    `level`       varchar(32)  NOT NULL DEFAULT '' COMMENT '奖励级别',
    `role`        varchar(32)  NOT NULL DEFAULT '' COMMENT '获奖角色',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 405055
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历学术奖励';

-- ----------------------------
-- Table structure for resume_additional_info
-- ----------------------------
DROP TABLE IF EXISTS `resume_additional_info`;
CREATE TABLE `resume_additional_info`
(
    `id`          int(11)                           NOT NULL AUTO_INCREMENT,
    `add_time`    datetime                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                          NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)                           NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`   int(11)                           NOT NULL DEFAULT '0' COMMENT '会员id',
    `theme_id`    tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '主题编号',
    `theme_name`  varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '自定义主题名称',
    `content`     text CHARACTER SET utf8mb4        NOT NULL COMMENT '内容',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 122875
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='简历附加信息表';

-- ----------------------------
-- Table structure for resume_announcement_footprint
-- ----------------------------
DROP TABLE IF EXISTS `resume_announcement_footprint`;
CREATE TABLE `resume_announcement_footprint`
(
    `id`              int(11)  NOT NULL AUTO_INCREMENT,
    `add_time`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `date`            date     NOT NULL DEFAULT '0000-00-00' COMMENT '创建日期',
    `resume_id`       int(11)  NOT NULL DEFAULT '0' COMMENT '简历id',
    `announcement_id` int(11)  NOT NULL DEFAULT '0' COMMENT '公告id',
    `last_time`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最近一次的时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_date_resume_id_announcement_id` (`date`, `resume_id`, `announcement_id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 39900906
  DEFAULT CHARSET = utf8 COMMENT ='求职者公告足迹';

-- ----------------------------
-- Table structure for resume_announcement_report_record
-- ----------------------------
DROP TABLE IF EXISTS `resume_announcement_report_record`;
CREATE TABLE `resume_announcement_report_record`
(
    `id`              int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `resume_id`       int(11)  NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `announcement_id` int(11)  NOT NULL DEFAULT '0' COMMENT '公告表主键id',
    `token`           char(32) NOT NULL DEFAULT '' COMMENT '唯一标识',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2897
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者公告使用分析报告记录表';

-- ----------------------------
-- Table structure for resume_attachment
-- ----------------------------
DROP TABLE IF EXISTS `resume_attachment`;
CREATE TABLE `resume_attachment`
(
    `id`                    int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`              datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`                tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`             int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`             int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `file_url`              varchar(255) NOT NULL DEFAULT '' COMMENT '文件路径',
    `file_name`             varchar(512) NOT NULL DEFAULT '' COMMENT '文件名称',
    `is_default`            tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否是默认简历',
    `file_id`               int(11)      NOT NULL DEFAULT '0' COMMENT '文件id',
    `on_site_apply_amount`  int(11)      NOT NULL DEFAULT '0' COMMENT '站内投递次数',
    `off_site_apply_amount` int(11)      NOT NULL DEFAULT '0' COMMENT '站外投递次数',
    `download_amount`       int(11)      NOT NULL DEFAULT '0' COMMENT '简历下载次数',
    `note`                  varchar(128) NOT NULL DEFAULT '' COMMENT '备注内容',
    `token`                 varchar(64)  NOT NULL DEFAULT '' COMMENT 'token,避免id被外界知道',
    `is_upload_oss`         tinyint(1)            DEFAULT '2' COMMENT '是否上传到oss 1是 2否',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_token` (`token`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 706578
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC COMMENT ='附件简历表';

-- ----------------------------
-- Table structure for resume_certificate
-- ----------------------------
DROP TABLE IF EXISTS `resume_certificate`;
CREATE TABLE `resume_certificate`
(
    `id`                 int(11)     NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`             tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`          int(11)     NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`          int(11)     NOT NULL DEFAULT '0' COMMENT '会员id',
    `obtain_date`        date        NOT NULL DEFAULT '0000-00-00' COMMENT '获得时间',
    `certificate_id`     int(11)     NOT NULL DEFAULT '0' COMMENT '证书id',
    `score`              varchar(64) NOT NULL DEFAULT '' COMMENT '成绩',
    `certificate_custom` varchar(60) NOT NULL DEFAULT '' COMMENT '自定义证书名字',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_certificate_id` (`certificate_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 647128
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历资质证书';

-- ----------------------------
-- Table structure for resume_complete
-- ----------------------------
DROP TABLE IF EXISTS `resume_complete`;
CREATE TABLE `resume_complete`
(
    `id`                 int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`           datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`             tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`          int(11)    NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`          int(11)    NOT NULL DEFAULT '0' COMMENT '用户id',
    `basic`              tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户基本信息条数',
    `education`          tinyint(1) NOT NULL DEFAULT '0' COMMENT '教育经历信息条数',
    `research_direction` tinyint(1) NOT NULL DEFAULT '0' COMMENT '研究方向信息条数',
    `work`               tinyint(1) NOT NULL DEFAULT '0' COMMENT '工作/实习经历信息条数',
    `intention`          tinyint(1) NOT NULL DEFAULT '0' COMMENT '求职意向信息条数',
    `academic_page`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '学术论文信息条数',
    `academic_patent`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '学术专利信息条数',
    `academic_book`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '学术著作信息条数',
    `research_project`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '科研项目信息条数',
    `academic_reward`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '学术奖励信息条数',
    `other_reward`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '其他奖励信息条数',
    `certificate`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '资质证书信息条数',
    `skill`              tinyint(1) NOT NULL DEFAULT '0' COMMENT '技能语言信息条数',
    `advantage`          tinyint(1) NOT NULL DEFAULT '0' COMMENT '个人优势信息条数',
    `other_skill`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '其他技能信息条数',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_resume_Id` (`resume_id`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1166529
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for resume_contact
-- ----------------------------
DROP TABLE IF EXISTS `resume_contact`;
CREATE TABLE `resume_contact`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `mobile`      varchar(64)  NOT NULL DEFAULT '' COMMENT '联系方式',
    `email`       varchar(256) NOT NULL DEFAULT '' COMMENT '邮箱',
    `qq`          varchar(64)  NOT NULL DEFAULT '' COMMENT 'qq',
    `weixin`      varchar(64)  NOT NULL DEFAULT '' COMMENT '微信',
    `mobile_code` varchar(16)  NOT NULL DEFAULT '' COMMENT '手机号区号',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历联系人信息';

-- ----------------------------
-- Table structure for resume_download_log
-- ----------------------------
DROP TABLE IF EXISTS `resume_download_log`;
CREATE TABLE `resume_download_log`
(
    `id`                   int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`             datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `job_id`               int(11)     NOT NULL DEFAULT '0' COMMENT '职位id',
    `handler_type`         tinyint(1)  NOT NULL DEFAULT '0' COMMENT '用户类型，1:运营平台用户；2:普通用户',
    `handler_id`           int(11)     NOT NULL DEFAULT '0' COMMENT '操作用户id',
    `handler_name`         varchar(90) NOT NULL DEFAULT '' COMMENT '操作用户名称',
    `resume_member_id`     int(11)     NOT NULL DEFAULT '0' COMMENT '人才编号',
    `resume_name`          varchar(90) NOT NULL DEFAULT '' COMMENT '人才姓名',
    `resume_id`            int(11)     NOT NULL DEFAULT '0' COMMENT '简历id',
    `resume_attachment_id` int(11)     NOT NULL DEFAULT '0' COMMENT '简历附件id',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_resume_attachment_id` (`resume_attachment_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 152565
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历下载记录';

-- ----------------------------
-- Table structure for resume_education
-- ----------------------------
DROP TABLE IF EXISTS `resume_education`;
CREATE TABLE `resume_education`
(
    `id`                int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`            tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`         int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`         int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `begin_date`        date         NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间',
    `end_date`          date         NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间',
    `is_abroad`         tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否留学,0否,1是',
    `school`            varchar(64)  NOT NULL DEFAULT '' COMMENT '学校',
    `college`           varchar(100) NOT NULL DEFAULT '' COMMENT '第二院系',
    `major_id`          int(11)      NOT NULL DEFAULT '0' COMMENT '专业id',
    `education_id`      int(11)      NOT NULL DEFAULT '0' COMMENT '学历水平id',
    `is_recruitment`    int(11)      NOT NULL DEFAULT '0' COMMENT '是否统招0否1是',
    `is_highest`        tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否是最高学历',
    `is_project_school` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否985/211',
    `major_custom`      varchar(60)  NOT NULL DEFAULT '' COMMENT '自定义专业名字',
    `major_id_level_1`  int(11)      NOT NULL DEFAULT '0' COMMENT '专业一级',
    `major_id_level_2`  int(11)      NOT NULL DEFAULT '0' COMMENT '专业二级',
    `major_id_level_3`  int(11)      NOT NULL DEFAULT '0' COMMENT '专业三级',
    `mentor`            varchar(50)  NOT NULL DEFAULT '' COMMENT '导师',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_major_id` (`major_id`),
    KEY `idx_education_id` (`education_id`),
    KEY `idx_major_id_level_1` (`major_id_level_1`),
    KEY `idx_major_id_level_2` (`major_id_level_2`),
    KEY `idx_major_id_level_3` (`major_id_level_3`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1031439
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历教育经验';

-- ----------------------------
-- Table structure for resume_equity
-- ----------------------------
DROP TABLE IF EXISTS `resume_equity`;
CREATE TABLE `resume_equity`
(
    `id`            int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`      datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `resume_id`     int(11)    NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `equity_id`     int(11)    NOT NULL DEFAULT '0' COMMENT '权益表主键id',
    `begin_time`    datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '生效时间',
    `expire_time`   datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '过期时间',
    `expire_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态,0未过期,1已过期',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_equity_id` (`equity_id`) USING BTREE,
    KEY `idx_expire_time` (`begin_time`) USING BTREE,
    KEY `idx_expire_status` (`expire_status`) USING BTREE,
    KEY `idx_begin_time` (`begin_time`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 59266
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员权益明细表';

-- ----------------------------
-- Table structure for resume_equity_action_record
-- ----------------------------
DROP TABLE IF EXISTS `resume_equity_action_record`;
CREATE TABLE `resume_equity_action_record`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `resume_id`       int(11)      NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `equity_id`       int(11)      NOT NULL DEFAULT '0' COMMENT '权益表主键id',
    `equity_type`     tinyint(1)   NOT NULL DEFAULT '0' COMMENT '1:增加,2:减少',
    `action_type`     tinyint(2)   NOT NULL DEFAULT '0' COMMENT '1:购买vip,2:购买竞争力洞察,3:查看职位,4:查看公告,5:服务过期,6购买求职快,7简历置顶,8投递置顶',
    `relation_id`     int(11)      NOT NULL DEFAULT '0' COMMENT '关联的ID,用于显示备注',
    `relation_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注文案显示',
    `expire_time`     datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '会员过期时间，购买行为才会有值',
    `operation_type`  tinyint(1)   NOT NULL DEFAULT '0' COMMENT '操作者类型,1:求职者,2:系统,3:运营人员',
    `operation_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '操作者id',
    `before_amount`   int(11)      NOT NULL DEFAULT '0' COMMENT '操作前数量',
    `after_amount`    int(11)      NOT NULL DEFAULT '0' COMMENT '操作后数量',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_equity_id` (`equity_id`) USING BTREE,
    KEY `idx_equity_type` (`equity_type`) USING BTREE,
    KEY `idx_action_type` (`action_type`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 175194
  DEFAULT CHARSET = utf8mb4 COMMENT ='权益产出/消耗明细表';

-- ----------------------------
-- Table structure for resume_equity_admin_setting
-- ----------------------------
DROP TABLE IF EXISTS `resume_equity_admin_setting`;
CREATE TABLE `resume_equity_admin_setting`
(
    `id`                    int(11) unsigned NOT NULL AUTO_INCREMENT,
    `name`                  varchar(60)      NOT NULL DEFAULT '' COMMENT '名字',
    `equity_package_id`     int(11)          NOT NULL DEFAULT '0' COMMENT '产品ID',
    `real_amount`           decimal(10, 2)   NOT NULL DEFAULT '0.00' COMMENT '订单金额',
    `refund_amount`         decimal(10, 2)   NOT NULL DEFAULT '0.00' COMMENT '退款金额',
    `audit_refund_amount`   decimal(10, 2)   NOT NULL DEFAULT '0.00' COMMENT '审核退款金额',
    `resume_id`             int(11)          NOT NULL DEFAULT '0' COMMENT '简历ID',
    `member_id`             int(11)          NOT NULL DEFAULT '0' COMMENT '账号ID',
    `mobile`                varchar(16)      NOT NULL DEFAULT '' COMMENT '手机号',
    `mobile_code`           varchar(16)      NOT NULL DEFAULT '' COMMENT '手机号区号',
    `add_time`              datetime         NOT NULL COMMENT '添加时间',
    `update_time`           datetime                  DEFAULT NULL COMMENT '更新时间',
    `open_time`             datetime                  DEFAULT NULL COMMENT '开通时间',
    `is_refund`             tinyint(2)       NOT NULL DEFAULT '2' COMMENT '是否退款 2没有 1退款',
    `status`                tinyint(2)       NOT NULL DEFAULT '1' COMMENT '状态  1配置待审核  2退款待审核 3 审核通过 4审核驳回5退款审核驳回',
    `submit_admin_id`       int(11)                   DEFAULT '0' COMMENT '提交人ID',
    `trade_no`              varchar(255)     NOT NULL DEFAULT '' COMMENT '流水号(小鹅通订单号)',
    `order_no`              varchar(255)     NOT NULL DEFAULT '' COMMENT '订单号',
    `order_id`              int(11)          NOT NULL DEFAULT '0' COMMENT '订单ID',
    `is_delete`             tinyint(2)       NOT NULL DEFAULT '2' COMMENT '是否删除1删除 2否',
    `setting_remark`        varchar(255)     NOT NULL DEFAULT '' COMMENT '配置备注',
    `setting_reject_remark` varchar(255)     NOT NULL DEFAULT '' COMMENT '配置驳回备注',
    `setting_file_ids`      varchar(128)     NOT NULL DEFAULT '' COMMENT '配置凭证文件ID字符串',
    `refund_remark`         varchar(255)     NOT NULL DEFAULT '' COMMENT '退款备注',
    `refund_reject_remark`  varchar(255)     NOT NULL DEFAULT '' COMMENT '退款驳回备注',
    `refund_file_ids`       varchar(128)     NOT NULL DEFAULT '' COMMENT '退款凭证文件ID字符串',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE,
    KEY `idx_trade_no` (`trade_no`) USING BTREE,
    KEY `idx_order_no` (`order_no`) USING BTREE,
    KEY `idx_order_id` (`order_id`) USING BTREE,
    KEY `idx_equity_package_id` (`equity_package_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 256
  DEFAULT CHARSET = utf8mb4 COMMENT ='人才权益运营开通配置';

-- ----------------------------
-- Table structure for resume_equity_package
-- ----------------------------
DROP TABLE IF EXISTS `resume_equity_package`;
CREATE TABLE `resume_equity_package`
(
    `id`                  int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`            datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `resume_id`           int(11)    NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `package_category_id` int(11)    NOT NULL COMMENT '包分类ID',
    `equity_id`           int(11)    NOT NULL DEFAULT '0' COMMENT '权益表主键id',
    `begin_time`          datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '生效时间',
    `expire_time`         datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '过期时间',
    `expire_status`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态,0未过期,1已过期',
    `amount`              int(11)    NOT NULL DEFAULT '0' COMMENT '次数配置',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_rid_cid_eid` (`resume_id`, `package_category_id`, `equity_id`) USING BTREE,
    KEY `idx_equity_id` (`equity_id`) USING BTREE,
    KEY `idx_package_category_id` (`package_category_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 60096
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员权益包权益明细表';

-- ----------------------------
-- Table structure for resume_equity_package_category_setting
-- ----------------------------
DROP TABLE IF EXISTS `resume_equity_package_category_setting`;
CREATE TABLE `resume_equity_package_category_setting`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `name`        varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者权益组合分类（大类）';

-- ----------------------------
-- Table structure for resume_equity_package_detail
-- ----------------------------
DROP TABLE IF EXISTS `resume_equity_package_detail`;
CREATE TABLE `resume_equity_package_detail`
(
    `id`                  int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`            datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `resume_id`           int(11)    NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `package_category_id` int(11)    NOT NULL COMMENT '包分类ID',
    `equity_id`           int(11)    NOT NULL DEFAULT '0' COMMENT '权益表主键id',
    `begin_time`          datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '生效时间',
    `expire_time`         datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '过期时间',
    `expire_status`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态,0未过期,1已过期',
    `amount`              int(11)    NOT NULL DEFAULT '0' COMMENT '次数配置',
    `order_id`            int(11)    NOT NULL COMMENT '订单ID',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_equity_id` (`equity_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 93171
  DEFAULT CHARSET = utf8mb4 COMMENT ='会员权益包权益详情表';

-- ----------------------------
-- Table structure for resume_equity_package_relation_setting
-- ----------------------------
DROP TABLE IF EXISTS `resume_equity_package_relation_setting`;
CREATE TABLE `resume_equity_package_relation_setting`
(
    `id`                int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `equity_package_id` int(11)  NOT NULL DEFAULT '0' COMMENT '权益组合表主键id',
    `equity_id`         int(11)  NOT NULL DEFAULT '0' COMMENT '权益表主键id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 113
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者权益与组合中间表';

-- ----------------------------
-- Table structure for resume_equity_package_setting
-- ----------------------------
DROP TABLE IF EXISTS `resume_equity_package_setting`;
CREATE TABLE `resume_equity_package_setting`
(
    `id`                         int(11)                 NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                   datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                datetime                NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `name`                       varchar(255)            NOT NULL DEFAULT '' COMMENT '名称',
    `subname`                    varchar(255)            NOT NULL DEFAULT '' COMMENT '名称',
    `status`                     tinyint(1)              NOT NULL DEFAULT '0' COMMENT '状态,0待审核,1已上线,-1已下线',
    `equity_package_category_id` int(11)                 NOT NULL DEFAULT '0' COMMENT '权益组合分类表主键id',
    `original_amount`            decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '原始金额',
    `real_amount`                decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '真实金额',
    `days`                       int(11)                 NOT NULL DEFAULT '0' COMMENT '有效天数',
    `buy_desc`                   varchar(255)            NOT NULL DEFAULT '' COMMENT '购买描述',
    `buy_type`                   tinyint(1)              NOT NULL DEFAULT '0' COMMENT '购买类型,1:体验版,2:热销,3:超值,4:内测专享,5:推荐',
    `equity_package_type`        tinyint(2)              NOT NULL DEFAULT '1' COMMENT '权益包的类型:1基础包,2活动包',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 18
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者权益组合（小类）';

-- ----------------------------
-- Table structure for resume_equity_setting
-- ----------------------------
DROP TABLE IF EXISTS `resume_equity_setting`;
CREATE TABLE `resume_equity_setting`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `name`        varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态,0待审核,1已上线,-1已下线',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 12
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者权益配置';

-- ----------------------------
-- Table structure for resume_intention
-- ----------------------------
DROP TABLE IF EXISTS `resume_intention`;
CREATE TABLE `resume_intention`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`          tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`       int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`       int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `nature_type`     tinyint(1)   NOT NULL DEFAULT '0' COMMENT '工作性质(1全职，2兼职，3实习)',
    `job_category_id` int(11)      NOT NULL DEFAULT '0' COMMENT '意向职位id',
    `area_id`         varchar(256) NOT NULL DEFAULT '0' COMMENT '意向地区id',
    `min_wage`        varchar(60)  NOT NULL DEFAULT '' COMMENT '最低期望薪酬',
    `max_wage`        varchar(60)  NOT NULL DEFAULT '' COMMENT '最高期望薪酬',
    `wage_type`       tinyint(1)   NOT NULL DEFAULT '0' COMMENT '期望薪酬类型',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_job_category_id` (`job_category_id`),
    KEY `idx_area_id` (`area_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 931217
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历求职意向';

-- ----------------------------
-- Table structure for resume_intention_area_relation
-- ----------------------------
DROP TABLE IF EXISTS `resume_intention_area_relation`;
CREATE TABLE `resume_intention_area_relation`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `area_id`      int(11) NOT NULL DEFAULT '0' COMMENT '意向地区id',
    `intention_id` int(11) NOT NULL DEFAULT '0' COMMENT '地区级别 跟随area表level',
    `resume_id`    int(11) NOT NULL DEFAULT '0' COMMENT '简历id 为后续直接对人的意向分析做铺垫',
    `level`        int(11) NOT NULL DEFAULT '0' COMMENT '意向ID',
    PRIMARY KEY (`id`),
    KEY `idx_intention_id` (`intention_id`) USING BTREE,
    KEY `idx_area_id` (`area_id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1977386
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历求职意向';

-- ----------------------------
-- Table structure for resume_intention_temp
-- ----------------------------
DROP TABLE IF EXISTS `resume_intention_temp`;
CREATE TABLE `resume_intention_temp`
(
    `id`                 int(10)  NOT NULL AUTO_INCREMENT,
    `add_time`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `intention_id`       int(10)  NOT NULL DEFAULT '0' COMMENT '求职意向id',
    `top_education_code` int(11)  NOT NULL DEFAULT '0' COMMENT '最高学历code',
    `major_id`           int(11)  NOT NULL DEFAULT '0' COMMENT '专业id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_intention_id` (`intention_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 23859
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='求职意向临时信息表';

-- ----------------------------
-- Table structure for resume_job_footprint
-- ----------------------------
DROP TABLE IF EXISTS `resume_job_footprint`;
CREATE TABLE `resume_job_footprint`
(
    `id`        int(11)  NOT NULL AUTO_INCREMENT,
    `add_time`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `date`      date     NOT NULL DEFAULT '0000-00-00' COMMENT '创建日期',
    `resume_id` int(11)  NOT NULL DEFAULT '0' COMMENT '简历id',
    `job_id`    int(11)  NOT NULL DEFAULT '0' COMMENT '职位id',
    `last_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最近一次的时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_date_resume_id_job_id` (`date`, `resume_id`, `job_id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 20544478
  DEFAULT CHARSET = utf8 COMMENT ='求职者职位足迹';

-- ----------------------------
-- Table structure for resume_job_report_record
-- ----------------------------
DROP TABLE IF EXISTS `resume_job_report_record`;
CREATE TABLE `resume_job_report_record`
(
    `id`          int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `resume_id`   int(11)  NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `job_id`      int(11)  NOT NULL DEFAULT '0' COMMENT '职位表主键id',
    `token`       char(32) NOT NULL DEFAULT '' COMMENT '唯一标识',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 11328
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者职位使用分析报告记录表';

-- ----------------------------
-- Table structure for resume_library
-- ----------------------------
DROP TABLE IF EXISTS `resume_library`;
CREATE TABLE `resume_library`
(
    `id`        int(11)  NOT NULL AUTO_INCREMENT,
    `add_time`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `resume_id` int(11)  NOT NULL DEFAULT '0' COMMENT '简历id',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='人才库';

-- ----------------------------
-- Table structure for resume_library_collect
-- ----------------------------
DROP TABLE IF EXISTS `resume_library_collect`;
CREATE TABLE `resume_library_collect`
(
    `id`         int(11)  NOT NULL AUTO_INCREMENT,
    `add_time`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `company_id` int(11)  NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`  int(11)  NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`  int(11)  NOT NULL DEFAULT '0' COMMENT '单位用户id',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4830
  DEFAULT CHARSET = utf8mb4 COMMENT ='人才库收藏';

-- ----------------------------
-- Table structure for resume_library_download_log
-- ----------------------------
DROP TABLE IF EXISTS `resume_library_download_log`;
CREATE TABLE `resume_library_download_log`
(
    `id`                         int(11)  NOT NULL AUTO_INCREMENT,
    `add_time`                   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `company_id`                 int(11)  NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`                  int(11)  NOT NULL DEFAULT '0' COMMENT '简历id',
    `company_package_change_log` int(11)  NOT NULL DEFAULT '0' COMMENT '消费id',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 8586
  DEFAULT CHARSET = utf8mb4 COMMENT ='人才库下载日志';

-- ----------------------------
-- Table structure for resume_library_invite_log
-- ----------------------------
DROP TABLE IF EXISTS `resume_library_invite_log`;
CREATE TABLE `resume_library_invite_log`
(
    `id`                         int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`                   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `type`                       int(11)      NOT NULL DEFAULT '0' COMMENT '1邮件,2短信,3邮件+短信',
    `company_id`                 int(11)      NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`                  int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `job_id`                     int(11)      NOT NULL DEFAULT '0' COMMENT '职位id',
    `company_package_change_log` int(11)      NOT NULL DEFAULT '0' COMMENT '消费id',
    `email_log_id`               int(11)      NOT NULL DEFAULT '0' COMMENT '邮件id',
    `sms_log_id`                 int(11)      NOT NULL DEFAULT '0' COMMENT '短信id',
    `remark`                     varchar(256) NOT NULL DEFAULT '' COMMENT '邀约备注',
    `is_remind_check`            tinyint(1)   NOT NULL DEFAULT '1' COMMENT '是否提醒已查看,1是2否',
    `company_member_id`          int(11)      NOT NULL DEFAULT '0' COMMENT '邀约人账号id(对应member_id)',
    `is_apply`                   tinyint(1)   NOT NULL DEFAULT '2' COMMENT '是否已投递, 1是, 2否',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_is_apply` (`is_apply`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 788571
  DEFAULT CHARSET = utf8mb4 COMMENT ='人才库邀约日志';

-- ----------------------------
-- Table structure for resume_match_job
-- ----------------------------
DROP TABLE IF EXISTS `resume_match_job`;
CREATE TABLE `resume_match_job`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`        datetime         NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '添加时间',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `job_category_id` int(11)          NOT NULL DEFAULT '0' COMMENT '意向职位',
    `area_ids`        varchar(255)     NOT NULL DEFAULT '' COMMENT '意向地区 注意该字段排序sort',
    `major_id`        int(11)          NOT NULL DEFAULT '0' COMMENT '学科专业',
    `education_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '学科专业',
    `job_id`          int(11)          NOT NULL COMMENT '职位ID',
    `match_rate`      int(11)          NOT NULL DEFAULT '0' COMMENT '匹配率',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_job_category_id` (`job_category_id`) USING BTREE,
    KEY `idx_major_id` (`major_id`) USING BTREE,
    KEY `idx_area_ids` (`area_ids`) USING BTREE,
    KEY `idx_education_id` (`education_id`) USING BTREE,
    KEY `idx+job_id` (`job_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for resume_order
-- ----------------------------
DROP TABLE IF EXISTS `resume_order`;
CREATE TABLE `resume_order`
(
    `id`                  int(11)                 NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`            datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime                NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `pay_time`            datetime                NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '支付时间',
    `status`              tinyint(1)              NOT NULL DEFAULT '0' COMMENT '状态,0未支付,1已支付,-1已取消',
    `equity_status`       tinyint(1)              NOT NULL DEFAULT '0' COMMENT '权益修改状态,0未录入,1已录入（支付状态为1,录入状态为0,则为异常订单）',
    `resume_id`           int(11)                 NOT NULL DEFAULT '0' COMMENT '在线简历表主键id',
    `equity_package_id`   int(11)                 NOT NULL DEFAULT '0' COMMENT '权益组合表主键id',
    `payway`              tinyint(1)              NOT NULL DEFAULT '0' COMMENT '支付方式:1:微信,2:支付宝,9:代付',
    `platform`            tinyint(1)              NOT NULL DEFAULT '0' COMMENT '下单渠道:1:PC,2:H5,3:MINI,9:运营端',
    `pay_channel`         tinyint(1)              NOT NULL DEFAULT '0' COMMENT '支付渠道:1:微信扫码,2:微信H5,3:微信JSAPI,9:代付',
    `ip`                  varchar(255)            NOT NULL DEFAULT '' COMMENT 'IP',
    `original_amount`     decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '原始金额',
    `real_amount`         decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '真实金额',
    `order_no`            varchar(255)            NOT NULL DEFAULT '' COMMENT '平台订单号',
    `trade_no`            varchar(255)            NOT NULL DEFAULT '' COMMENT '交易订单号',
    `remark`              varchar(255)            NOT NULL DEFAULT '' COMMENT '备注',
    `snapshot_data`       varchar(1024)           NOT NULL DEFAULT '' COMMENT '快照数据',
    `equity_package_type` tinyint(2)              NOT NULL DEFAULT '1' COMMENT '权益包的类型:1基础包,2活动包',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_order_no` (`order_no`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_pay_time` (`pay_time`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_equity_package_id` (`equity_package_id`) USING BTREE,
    KEY `idx_payway` (`payway`) USING BTREE,
    KEY `idx_platform` (`platform`) USING BTREE,
    KEY `idx_order_no` (`order_no`) USING BTREE,
    KEY `idx_trade_no` (`trade_no`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 18573
  DEFAULT CHARSET = utf8mb4 COMMENT ='支付订单表';

-- ----------------------------
-- Table structure for resume_order_snapshot
-- ----------------------------
DROP TABLE IF EXISTS `resume_order_snapshot`;
CREATE TABLE `resume_order_snapshot`
(
    `id`                  int(11)       NOT NULL AUTO_INCREMENT,
    `add_time`            datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `order_id`            int(11)       NOT NULL DEFAULT '0' COMMENT '订单表id',
    `equity_package_name` varchar(255)  NOT NULL DEFAULT '' COMMENT '产品名称',
    `service_days`        int(10)       NOT NULL DEFAULT '0' COMMENT '服务天数',
    `equity_content`      varchar(2048) NOT NULL DEFAULT '' COMMENT '权益内容，逗号连接文字',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 18485
  DEFAULT CHARSET = utf8mb4 COMMENT ='订单快照';

-- ----------------------------
-- Table structure for resume_other_reward
-- ----------------------------
DROP TABLE IF EXISTS `resume_other_reward`;
CREATE TABLE `resume_other_reward`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `obtain_date` date         NOT NULL DEFAULT '0000-00-00' COMMENT '获得时间',
    `name`        varchar(128) NOT NULL DEFAULT '' COMMENT '获奖名称',
    `level`       varchar(32)  NOT NULL DEFAULT '' COMMENT '奖励级别',
    `role`        varchar(32)  NOT NULL DEFAULT '' COMMENT '获奖角色',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 678503
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历其他荣誉';

-- ----------------------------
-- Table structure for resume_other_skill
-- ----------------------------
DROP TABLE IF EXISTS `resume_other_skill`;
CREATE TABLE `resume_other_skill`
(
    `id`          int(11)                           NOT NULL AUTO_INCREMENT,
    `add_time`    datetime                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                          NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)                           NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`   int(11)                           NOT NULL DEFAULT '0' COMMENT '会员id',
    `name`        varchar(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '技能名称',
    `degree_type` tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '掌握程度',
    `description` text CHARACTER SET utf8mb4        NOT NULL COMMENT '技能描述',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`) USING BTREE,
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 53735
  DEFAULT CHARSET = utf8
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for resume_refresh
-- ----------------------------
DROP TABLE IF EXISTS `resume_refresh`;
CREATE TABLE `resume_refresh`
(
    `id`          int(11)       NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `resume_id`   int(11)       NOT NULL DEFAULT '0' COMMENT '简历id',
    `return_type` tinyint(2)    NOT NULL DEFAULT '0' COMMENT '刷新成功类型:1当天第一次成功，2博士研究生完善简历，3简历中心，4再次刷新，5开放简历在刷新一次，8系统刷新，9除了前面说的情况',
    `return_data` varchar(2048) NOT NULL DEFAULT '' COMMENT '返回给用户的信息',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 328409
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历刷新';

-- ----------------------------
-- Table structure for resume_remind
-- ----------------------------
DROP TABLE IF EXISTS `resume_remind`;
CREATE TABLE `resume_remind`
(
    `id`                         int(11)  NOT NULL AUTO_INCREMENT,
    `add_time`                   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `resume_id`                  int(11)  NOT NULL DEFAULT '0' COMMENT '简历id',
    `job_apply_all_count`        int(11)  NOT NULL DEFAULT '0' COMMENT '全部职位投递状态数量',
    `job_apply_wait_check_count` int(11)  NOT NULL DEFAULT '0' COMMENT '已投递状态数量',
    `job_apply_check_count`      int(11)  NOT NULL DEFAULT '0' COMMENT '已查看状态数量',
    `job_apply_pass_count`       int(11)  NOT NULL DEFAULT '0' COMMENT '通过初筛状态数量',
    `job_apply_invite_count`     int(11)  NOT NULL DEFAULT '0' COMMENT '要求面试状态数量',
    `job_apply_no_pass_count`    int(11)  NOT NULL DEFAULT '0' COMMENT '不合适状态数量',
    `job_apply_employed_count`   int(11)  NOT NULL DEFAULT '0' COMMENT '已录入状态数量',
    `company_view_count`         int(11)  NOT NULL DEFAULT '0' COMMENT '单位查看数量(谁看过我)',
    `job_invite_count`           int(11)  NOT NULL DEFAULT '0' COMMENT '职位邀约数量',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1179677
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者强提醒';

-- ----------------------------
-- Table structure for resume_research_direction
-- ----------------------------
DROP TABLE IF EXISTS `resume_research_direction`;
CREATE TABLE `resume_research_direction`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '会员id',
    `content`     text COMMENT '内容',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 451893
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历研究方向';

-- ----------------------------
-- Table structure for resume_research_project
-- ----------------------------
DROP TABLE IF EXISTS `resume_research_project`;
CREATE TABLE `resume_research_project`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `name`        varchar(128) NOT NULL DEFAULT '' COMMENT '项目名称',
    `category`    varchar(64)  NOT NULL DEFAULT '' COMMENT '项目类别',
    `role`        varchar(32)  NOT NULL DEFAULT '' COMMENT '担任角色',
    `begin_date`  date         NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间（项目周期）',
    `end_date`    date         NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间（项目周期）',
    `company`     varchar(64)  NOT NULL DEFAULT '' COMMENT '所属单位',
    `description` text COMMENT '项目描述',
    `is_close`    tinyint(1)   NOT NULL COMMENT '是否结项（0否  1是）',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 328452
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历科研项目';

-- ----------------------------
-- Table structure for resume_setting
-- ----------------------------
DROP TABLE IF EXISTS `resume_setting`;
CREATE TABLE `resume_setting`
(
    `id`                 int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`           datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`             tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`          int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `is_hide_resume`     tinyint(1)   NOT NULL DEFAULT '2' COMMENT '是否隐藏简历（0否 1是）',
    `is_anonymous`       tinyint(1)   NOT NULL DEFAULT '2' COMMENT '是否匿名显示（0否 1是）',
    `is_proxy_deliver`   tinyint(1)   NOT NULL DEFAULT '2' COMMENT '是否代理投递（0否 1是）',
    `is_job_feedback`    tinyint(1)   NOT NULL DEFAULT '1' COMMENT '是否开启求职反馈（0否 1是）',
    `is_system_message`  tinyint(1)   NOT NULL DEFAULT '1' COMMENT '是否开启系统消息推送（0否  1是）',
    `is_todo_notice`     tinyint(1)   NOT NULL DEFAULT '1' COMMENT '是否开启待办通知（0否  1是）',
    `shield_company_id`  varchar(255) NOT NULL DEFAULT '' COMMENT '屏蔽的公司id',
    `is_job_invite`      tinyint(1)   NOT NULL DEFAULT '1' COMMENT '是否开启职位邀约1开启,2关闭',
    `is_company_view_me` tinyint(1)   NOT NULL DEFAULT '1' COMMENT '是否公司查看自己的简历1开启,2关闭',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`resume_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1160499
  DEFAULT CHARSET = utf8 COMMENT ='求职者设置表';

-- ----------------------------
-- Table structure for resume_share
-- ----------------------------
DROP TABLE IF EXISTS `resume_share`;
CREATE TABLE `resume_share`
(
    `id`            int(10)                    NOT NULL AUTO_INCREMENT COMMENT 'id',
    `add_time`      datetime                   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime                   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`        tinyint(1)                 NOT NULL DEFAULT '0' COMMENT '状态',
    `company_id`    int(10)                    NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`     int(10)                    NOT NULL DEFAULT '0' COMMENT '简历id',
    `password`      char(4) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '访问密码',
    `code`          varchar(255)               NOT NULL DEFAULT '' COMMENT '唯一码',
    `expire_time`   datetime                   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效时间',
    `short_link_id` int(11)                    NOT NULL DEFAULT '0' COMMENT '短链接id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_code` (`code`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 588
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历分享表';

-- ----------------------------
-- Table structure for resume_skill
-- ----------------------------
DROP TABLE IF EXISTS `resume_skill`;
CREATE TABLE `resume_skill`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '会员id',
    `skill_id`    int(11)    NOT NULL DEFAULT '0' COMMENT '技能id',
    `degree_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '掌握程度',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_skill_id` (`skill_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 643459
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历技能/语言';

-- ----------------------------
-- Table structure for resume_stat_data
-- ----------------------------
DROP TABLE IF EXISTS `resume_stat_data`;
CREATE TABLE `resume_stat_data`
(
    `id`                             int(11)    NOT NULL AUTO_INCREMENT,
    `add_time`                       datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                         tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`                      int(11)    NOT NULL DEFAULT '0' COMMENT '简历id',
    `on_site_apply_amount`           int(11)    NOT NULL DEFAULT '0' COMMENT '站内申请次数',
    `off_site_apply_amount`          int(11)    NOT NULL DEFAULT '0' COMMENT '站外申请次数',
    `interview_amount`               int(11)    NOT NULL DEFAULT '0' COMMENT '邀请面试次数',
    `resume_download_amount`         int(11)    NOT NULL DEFAULT '0' COMMENT '简历下载次数',
    `interview_record_amount`        int(11)    NOT NULL DEFAULT '0' COMMENT '邀请面试记录的总条数',
    `resume_library_download_amount` int(11)    NOT NULL DEFAULT '0' COMMENT '人才库下载次数',
    `resume_library_collect_amount`  int(11)    NOT NULL DEFAULT '0' COMMENT '人才库收藏次数',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1160499
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历数据统计表';

-- ----------------------------
-- Table structure for resume_tag
-- ----------------------------
DROP TABLE IF EXISTS `resume_tag`;
CREATE TABLE `resume_tag`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
    `update_time` datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `tag`         varchar(128)     NOT NULL DEFAULT '' COMMENT '特色标签内容',
    `admin_id`    int(11)          NOT NULL DEFAULT '0' COMMENT '添加人id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 3
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者标签字典表';

-- ----------------------------
-- Table structure for resume_tag_relation
-- ----------------------------
DROP TABLE IF EXISTS `resume_tag_relation`;
CREATE TABLE `resume_tag_relation`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT,
    `resume_id`     int(11)          NOT NULL COMMENT '求职者id',
    `resume_tag_id` int(11)          NOT NULL COMMENT '求职者标签id',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`, `resume_tag_id`),
    KEY `idx_resume_tag_id` (`resume_tag_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1289
  DEFAULT CHARSET = utf8mb4 COMMENT ='求职者标签关系表';

-- ----------------------------
-- Table structure for resume_template_config
-- ----------------------------
DROP TABLE IF EXISTS `resume_template_config`;
CREATE TABLE `resume_template_config`
(
    `id`                  int(11) unsigned NOT NULL AUTO_INCREMENT,
    `name`                varchar(128)     NOT NULL DEFAULT '' COMMENT '模板名称',
    `code`                varchar(64)      NOT NULL DEFAULT '' COMMENT '模板编号',
    `pdf_download_number` int(11)          NOT NULL COMMENT 'pdf下载次数',
    `doc_download_number` int(11)          NOT NULL COMMENT 'doc下载次数',
    `is_show`             tinyint(2)       NOT NULL DEFAULT '1' COMMENT '是否显示 1显示  2隐藏',
    `is_delete`           tinyint(2)       NOT NULL DEFAULT '2' COMMENT '是否删除 1 是 2否',
    `add_time`            datetime         NOT NULL COMMENT '添加时间',
    `update_time`         datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `description`         varchar(128)     NOT NULL DEFAULT '' COMMENT '模板描述',
    `template_image`      varchar(255)     NOT NULL COMMENT '模板图片',
    `is_vip`              tinyint(2)       NOT NULL DEFAULT '2' COMMENT '是否会员 1 是 2 不是',
    `sort_number`         int(11)          NOT NULL DEFAULT '0' COMMENT '排序 序号越大越前',
    `file_id`             int(11)          NOT NULL DEFAULT '0' COMMENT '模板文件在file表的关联ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code` (`code`) USING BTREE,
    KEY `idx_file_id` (`file_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历模板配置';

-- ----------------------------
-- Table structure for resume_template_download_record
-- ----------------------------
DROP TABLE IF EXISTS `resume_template_download_record`;
CREATE TABLE `resume_template_download_record`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `resume_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '简历ID',
    `template_id` int(11)          NOT NULL DEFAULT '0' COMMENT '模板ID',
    `type`        tinyint(2)       NOT NULL DEFAULT '1' COMMENT '下载类型 1PDF 2DOC',
    `add_time`    datetime         NOT NULL COMMENT '下载时间',
    `platform`    tinyint(2)       NOT NULL DEFAULT '0' COMMENT '下载平台 1PC 2 h5  3mini',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 57187
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历模板下载记录';

-- ----------------------------
-- Table structure for resume_top_config
-- ----------------------------
DROP TABLE IF EXISTS `resume_top_config`;
CREATE TABLE `resume_top_config`
(
    `id`                       int(11) unsigned NOT NULL AUTO_INCREMENT,
    `add_time`                 datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    `update_time`              datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `resume_id`                int(11)          NOT NULL DEFAULT '0' COMMENT '简历ID',
    `set_date`                 date             NOT NULL COMMENT '置顶设置时间',
    `status`                   tinyint(1)       NOT NULL DEFAULT '1' COMMENT '状态 1待生效 2生效中 9已过期 10权益异常失效',
    `equity_package_detail_id` int(11)          NOT NULL DEFAULT '0' COMMENT '资源真实消耗ID(也就是resume_equity_package_detail表ID)',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`) USING BTREE,
    KEY `idx_set_date` (`set_date`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 7600
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历置顶配置';

-- ----------------------------
-- Table structure for resume_work
-- ----------------------------
DROP TABLE IF EXISTS `resume_work`;
CREATE TABLE `resume_work`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`   int(11)      NOT NULL DEFAULT '0' COMMENT '会员id',
    `begin_date`  date         NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间',
    `end_date`    date         NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间',
    `is_today`    tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否至今,1是,0否',
    `is_abroad`   tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否海外经历,1是,0否',
    `is_postdoc`  tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否博士后经历,1是,0否',
    `company`     varchar(128) NOT NULL DEFAULT '' COMMENT '单位名称',
    `department`  varchar(32)  NOT NULL DEFAULT '' COMMENT '所属部门',
    `job_name`    varchar(32)  NOT NULL DEFAULT '' COMMENT '职位名称',
    `content`     text COMMENT '工作内容',
    `is_practice` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否实习（1是，2否）',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_member_id` (`member_id`),
    KEY `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 612089
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历工作/实习经历';

-- ----------------------------
-- Table structure for resume_wx_bind
-- ----------------------------
DROP TABLE IF EXISTS `resume_wx_bind`;
CREATE TABLE `resume_wx_bind`
(
    `id`           int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `status`       tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`    int(11)      NOT NULL DEFAULT '0' COMMENT '简历id',
    `openid`       varchar(64)  NOT NULL DEFAULT '' COMMENT 'openid',
    `unionid`      varchar(64)  NOT NULL DEFAULT '' COMMENT 'unionid',
    `avatar`       varchar(256) NOT NULL DEFAULT '' COMMENT '头像url',
    `is_subscribe` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否关注0否1是',
    `mini_openid`  varchar(64)  NOT NULL DEFAULT '' COMMENT '小程序openid',
    PRIMARY KEY (`id`),
    KEY `idx_openid` (`openid`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_unionid` (`unionid`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 502581
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for search_log
-- ----------------------------
DROP TABLE IF EXISTS `search_log`;
CREATE TABLE `search_log`
(
    `id`            int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`        tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态，1:使用；2:删除',
    `title`         varchar(256) NOT NULL DEFAULT '' COMMENT '检索文案',
    `resume_id`     int(11)      NOT NULL DEFAULT '0' COMMENT '人才id',
    `platform_type` tinyint(2)   NOT NULL DEFAULT '0' COMMENT '平台类型,1、运营系统；2:单位端；3:求职端；4:H5；5:小程序',
    `type`          tinyint(2)   NOT NULL DEFAULT '0' COMMENT '搜索类型，1:职位；2:公告；3:单位',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_resume_id` (`resume_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 112666
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='检索记录';

-- ----------------------------
-- Table structure for seo_hot_word_config
-- ----------------------------
DROP TABLE IF EXISTS `seo_hot_word_config`;
CREATE TABLE `seo_hot_word_config`
(
    `id`       int(10) unsigned NOT NULL AUTO_INCREMENT,
    `code`     varchar(32)      NOT NULL COMMENT '唯一码',
    `keyword`  varchar(128)     NOT NULL DEFAULT '' COMMENT '关键字',
    `add_time` datetime         NOT NULL COMMENT '添加时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code` (`code`) USING BTREE,
    KEY `idx_keyword` (`keyword`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 323566
  DEFAULT CHARSET = utf8mb4 COMMENT ='热词关键字配置表';

-- ----------------------------
-- Table structure for seo_job_wiki
-- ----------------------------
DROP TABLE IF EXISTS `seo_job_wiki`;
CREATE TABLE `seo_job_wiki`
(
    `id`               int(10) unsigned NOT NULL AUTO_INCREMENT,
    `code`             varchar(32)      NOT NULL COMMENT '唯一码',
    `keyword`          varchar(128)     NOT NULL DEFAULT '' COMMENT '关键字',
    `add_time`         datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    `job_introduction` varchar(800)     NOT NULL DEFAULT '' COMMENT '职位简介',
    `job_content`      varchar(2000)    NOT NULL DEFAULT '' COMMENT '职位内容',
    `jump_link`        varchar(255)     NOT NULL DEFAULT '' COMMENT '跳转链接',
    `is_delete`        tinyint(2)       NOT NULL DEFAULT '0' COMMENT '0:正常；1:删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_code` (`code`) USING BTREE,
    KEY `idx_keyword` (`keyword`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1175
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位百科';

-- ----------------------------
-- Table structure for seo_sitemap_file
-- ----------------------------
DROP TABLE IF EXISTS `seo_sitemap_file`;
CREATE TABLE `seo_sitemap_file`
(
    `id`            int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `file_name`     varchar(100) NOT NULL DEFAULT '' COMMENT '文件名字',
    `file_path`     varchar(255) NOT NULL DEFAULT '' COMMENT '文件路劲',
    `platform_type` tinyint(2)   NOT NULL DEFAULT '0' COMMENT '平台类型 1 pc 5 H5',
    `type`          tinyint(2)   NOT NULL DEFAULT '0' COMMENT '文件类型1招聘单位详情 2招聘公告详情 3招聘公告职位列表详情 4招聘职位详情 5资讯文章详情 6每日汇总详情 7首页',
    `number`        int(10)      NOT NULL DEFAULT '0' COMMENT '文件数据量',
    `add_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10145
  DEFAULT CHARSET = utf8mb4 COMMENT ='seo文件记录表';

-- ----------------------------
-- Table structure for shield_company
-- ----------------------------
DROP TABLE IF EXISTS `shield_company`;
CREATE TABLE `shield_company`
(
    `id`          int(11)    NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `resume_id`   int(11)    NOT NULL DEFAULT '0' COMMENT '求职者简历id',
    `company_id`  int(11)    NOT NULL COMMENT '公司id',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`resume_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 9149
  DEFAULT CHARSET = utf8 COMMENT ='屏蔽公司表';

-- ----------------------------
-- Table structure for short_link
-- ----------------------------
DROP TABLE IF EXISTS `short_link`;
CREATE TABLE `short_link`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态',
    `code`        varchar(255) NOT NULL DEFAULT '' COMMENT '短链唯一码code',
    `url`         varchar(255) NOT NULL DEFAULT '' COMMENT '长链接地址',
    `remark`      varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_code` (`code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 588
  DEFAULT CHARSET = utf8 COMMENT ='短链接表';

-- ----------------------------
-- Table structure for showcase
-- ----------------------------
DROP TABLE IF EXISTS `showcase`;
CREATE TABLE `showcase`
(
    `id`                         int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`                   datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`                     tinyint(1)    NOT NULL DEFAULT '2' COMMENT '广告状态，0:已下线，1:生效中；2:未生效；9:已删除',
    `is_show`                    tinyint(1)    NOT NULL DEFAULT '1' COMMENT '是否显示，1:是；2:否',
    `is_packing`                 tinyint(1)    NOT NULL DEFAULT '2' COMMENT '是否打包管理(打包管理的广告位更新是需要一起更新的，1:是；2:否',
    `update_time`                datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_time`                datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `company_id`                 int(11)       NOT NULL DEFAULT '0' COMMENT '公司单位id',
    `company_name`               varchar(32)   NOT NULL DEFAULT '' COMMENT '单位名称',
    `title`                      varchar(150)  NOT NULL DEFAULT '' COMMENT '广告标题',
    `sub_title`                  varchar(1024) NOT NULL DEFAULT '' COMMENT '副标题',
    `second_title`               varchar(150)  NOT NULL DEFAULT '' COMMENT '次标题',
    `image_url`                  varchar(150)  NOT NULL DEFAULT '' COMMENT '图片地址',
    `image_link`                 varchar(200)  NOT NULL DEFAULT '' COMMENT '图片链接地址',
    `image_alt`                  varchar(256)  NOT NULL DEFAULT '' COMMENT '图片说明文字',
    `online_time`                datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
    `offline_time`               datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '失效时间',
    `target_link`                varchar(512)  NOT NULL DEFAULT '' COMMENT '广告落地页地址',
    `home_position_id`           int(11)       NOT NULL DEFAULT '0' COMMENT '广告位置对应id',
    `contact_home_position_id`   varchar(32)   NOT NULL DEFAULT '0' COMMENT '关联位置id',
    `contact_home_position_sort` int(11)       NOT NULL DEFAULT '1' COMMENT '关联位置排序',
    `sort`                       int(11)       NOT NULL DEFAULT '1' COMMENT '广告排序',
    `describe`                   varchar(2048) NOT NULL DEFAULT '' COMMENT '广告描述',
    `creator_type`               tinyint(1)    NOT NULL DEFAULT '1' COMMENT '创建人类型，1:平台，2:其他',
    `creator`                    varchar(255)  NOT NULL DEFAULT '' COMMENT '创建人',
    `creator_id`                 int(11)       NOT NULL DEFAULT '0',
    `type`                       varchar(20)   NOT NULL DEFAULT '' COMMENT '广告位类型1付费 2RPO 3异议 4客情 8免费 9其他',
    `target_link_type`           tinyint(2)    NOT NULL DEFAULT '0' COMMENT '跳转类型，1：小程序站内指定页面；2:小程序网页；3：小程序视频号；4:小程序小程序',
    `page_link_type`             tinyint(2)    NOT NULL DEFAULT '0' COMMENT '页面类型，1、职位详情；2:公告详情；3:单位详情；4:职位搜索结果页；5:公告搜索结果页；6:单位搜索结果页',
    `other_image_url`            varchar(150)  NOT NULL DEFAULT '' COMMENT '其他图片地址',
    PRIMARY KEY (`id`),
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_home_position_id` (`home_position_id`) USING BTREE,
    KEY `idx_creator_id` (`creator_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 48681
  DEFAULT CHARSET = utf8mb4 COMMENT ='广告表';

-- ----------------------------
-- Table structure for showcase_browse_log
-- ----------------------------
DROP TABLE IF EXISTS `showcase_browse_log`;
CREATE TABLE `showcase_browse_log`
(
    `id`               int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`         datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `showcase_id`      int(11)       NOT NULL DEFAULT '0' COMMENT '广告id',
    `position_number`  varchar(128)  NOT NULL DEFAULT '' COMMENT '位置编码',
    `showcase_title`   varchar(128)  NOT NULL DEFAULT '' COMMENT '广告标题',
    `platform`         varchar(32)   NOT NULL DEFAULT '' COMMENT '平台文字',
    `platform_type`    int(11)       NOT NULL DEFAULT '0' COMMENT '平台类型',
    `position`         varchar(32)   NOT NULL DEFAULT '' COMMENT '位置文字',
    `home_position_id` int(11)       NOT NULL DEFAULT '0' COMMENT '位置id（区块）',
    `type`             tinyint(1)    NOT NULL DEFAULT '0' COMMENT '统计类型，1:点击；2:阅读',
    `useragent`        varchar(2048) NOT NULL DEFAULT '' COMMENT '请求表头信息',
    `user_cookies`     varchar(64)   NOT NULL DEFAULT '' COMMENT '用户的token',
    `company_id`       varchar(32)   NOT NULL DEFAULT '0' COMMENT '公司id(可无)',
    `company_name`     varchar(32)   NOT NULL DEFAULT '' COMMENT '公司名称（可空）',
    PRIMARY KEY (`id`),
    KEY `idx_showcase_id` (`showcase_id`) USING BTREE,
    KEY `idx_home_position_id` (`home_position_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`) USING BTREE,
    KEY `idx_user_cookies` (`user_cookies`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 18765348
  DEFAULT CHARSET = utf8mb4 COMMENT ='广告数据统计';

-- ----------------------------
-- Table structure for showcase_packing
-- ----------------------------
DROP TABLE IF EXISTS `showcase_packing`;
CREATE TABLE `showcase_packing`
(
    `id`       int(11)      NOT NULL AUTO_INCREMENT,
    `add_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `remark`   varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 16847
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for showcase_packing_relationship
-- ----------------------------
DROP TABLE IF EXISTS `showcase_packing_relationship`;
CREATE TABLE `showcase_packing_relationship`
(
    `id`                  int(11)  NOT NULL AUTO_INCREMENT,
    `add_time`            datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `showcase_id`         int(11)  NOT NULL DEFAULT '0' COMMENT '广告位id',
    `showcase_packing_id` int(11)  NOT NULL DEFAULT '0' COMMENT '打包的id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_showcase_id` (`showcase_id`) USING BTREE,
    KEY `idx_showcase_packing_id` (`showcase_packing_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 136166
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for skill
-- ----------------------------
DROP TABLE IF EXISTS `skill`;
CREATE TABLE `skill`
(
    `id`          int(11)                           NOT NULL AUTO_INCREMENT,
    `add_time`    datetime                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `status`      tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '状态',
    `parent_id`   int(11)                           NOT NULL DEFAULT '0' COMMENT '父id',
    `name`        varchar(64) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '名称',
    `sort`        int(11)                           NOT NULL DEFAULT '0' COMMENT '排序',
    `level`       tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '等级',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 102
  DEFAULT CHARSET = utf8 COMMENT ='技能语言表';

-- ----------------------------
-- Table structure for sms_log
-- ----------------------------
DROP TABLE IF EXISTS `sms_log`;
CREATE TABLE `sms_log`
(
    `id`             int(11)       NOT NULL AUTO_INCREMENT COMMENT 'id;主键id',
    `add_time`       datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `status`         tinyint(1)    NOT NULL DEFAULT '0' COMMENT '状态1成功,0失败',
    `mobile_code`    varchar(16)   NOT NULL DEFAULT '' COMMENT '手机号区号',
    `mobile`         varchar(16)   NOT NULL DEFAULT '' COMMENT '手机号',
    `content`        varchar(512)  NOT NULL DEFAULT '' COMMENT '发送内容',
    `type`           tinyint(1)    NOT NULL DEFAULT '0' COMMENT '类型, 1注册登录',
    `reason`         varchar(255)  NOT NULL DEFAULT '' COMMENT '发送失败原因',
    `ext_params`     varchar(255)  NOT NULL DEFAULT '' COMMENT '扩展参数',
    `sid`            varchar(128)  NOT NULL DEFAULT '' COMMENT '运营商sid(可用于回调查找)',
    `receive_status` tinyint(1)    NOT NULL DEFAULT '0' COMMENT '回调状态,0未回调，1回调告知成功，-1，回调告知失败)',
    `receive_data`   varchar(1024) NOT NULL DEFAULT '' COMMENT '回调数据，json格式',
    PRIMARY KEY (`id`),
    KEY `idx_add_time` (`add_time`),
    KEY `idx_mobile` (`mobile`),
    KEY `idx_sid` (`sid`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1998285
  DEFAULT CHARSET = utf8mb4 COMMENT ='短信发送记录';

-- ----------------------------
-- Table structure for special_need_apply_limit
-- ----------------------------
DROP TABLE IF EXISTS `special_need_apply_limit`;
CREATE TABLE `special_need_apply_limit`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `name`            varchar(100)     NOT NULL DEFAULT '' COMMENT '限制名称',
    `company_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '单位ID',
    `limit_type`      varchar(20)      NOT NULL DEFAULT 'count' COMMENT '限制类型：count=次数限制,condition=条件限制',
    `time_limit`      int(11)          NOT NULL DEFAULT '0' COMMENT '时间限制（天）',
    `count_limit`     int(11)          NOT NULL DEFAULT '1' COMMENT '次数限制',
    `condition_field` varchar(50)      NOT NULL DEFAULT '' COMMENT '条件字段（如is_abroad）',
    `condition_value` varchar(100)     NOT NULL DEFAULT '' COMMENT '条件值',
    `error_message`   varchar(255)     NOT NULL DEFAULT '' COMMENT '错误提示信息',
    `status`          tinyint(1)       NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
    `remark`          text COMMENT '备注说明',
    `created_by`      int(11)          NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `add_time`        datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_company_status` (`company_id`, `status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 7
  DEFAULT CHARSET = utf8mb4 COMMENT ='投递限制配置表';

-- ----------------------------
-- Table structure for special_need_config
-- ----------------------------
DROP TABLE IF EXISTS `special_need_config`;
CREATE TABLE `special_need_config`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `name`        varchar(100)     NOT NULL DEFAULT '' COMMENT '配置名称',
    `type`        varchar(20)      NOT NULL DEFAULT '' COMMENT '类型：company,announcement,job,apply_limit',
    `target_id`   int(11)          NOT NULL DEFAULT '0' COMMENT '目标ID（单位ID/公告ID/职位ID）',
    `field_name`  varchar(50)      NOT NULL DEFAULT '' COMMENT '字段名称',
    `field_value` text COMMENT '字段值',
    `platform`    varchar(20)      NOT NULL DEFAULT 'ALL' COMMENT '适用平台：ALL,PC,H5,MINI',
    `status`      tinyint(1)       NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
    `start_time`  datetime                  DEFAULT NULL COMMENT '生效开始时间',
    `end_time`    datetime                  DEFAULT NULL COMMENT '生效结束时间',
    `remark`      text COMMENT '备注说明',
    `created_by`  int(11)          NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `add_time`    datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_type_target` (`type`, `target_id`),
    KEY `idx_status_time` (`status`, `start_time`, `end_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 130
  DEFAULT CHARSET = utf8mb4 COMMENT ='特殊需求配置表';

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`
(
    `id`            int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `platform_type` tinyint(1)   NOT NULL DEFAULT '0' COMMENT '0全局,1pc,2h5',
    `name`          varchar(128) NOT NULL DEFAULT '' COMMENT 'name',
    `value`         text,
    `remark`        varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
    `is_business`   tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否业务配置(业务方的就配置这里1,系统这边的就设置为2)',
    `is_secret`     tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否保密,1是,2否',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_name` (`name`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 18
  DEFAULT CHARSET = utf8mb4 COMMENT ='系统级别配置';

-- ----------------------------
-- Table structure for topic
-- ----------------------------
DROP TABLE IF EXISTS `topic`;
CREATE TABLE `topic`
(
    `id`             int(11)      NOT NULL AUTO_INCREMENT COMMENT '话题id',
    `add_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    `status`         tinyint(2)   NOT NULL DEFAULT '0' COMMENT '状态，1:在线，3:编辑中；7:待审核；9:已删除',
    `title`          varchar(255) NOT NULL DEFAULT '' COMMENT '专题名称',
    `sub_title`      varchar(255) NOT NULL DEFAULT '' COMMENT '副标题',
    `target_url`     varchar(500) NOT NULL DEFAULT '' COMMENT '话题跳转链接',
    `cover_url`      varchar(500) NOT NULL DEFAULT '' COMMENT '专题图片',
    `news_list_json` text         NOT NULL COMMENT '相关资讯',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for trade
-- ----------------------------
DROP TABLE IF EXISTS `trade`;
CREATE TABLE `trade`
(
    `id`          int(11)                           NOT NULL AUTO_INCREMENT,
    `add_time`    datetime                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `status`      tinyint(1) unsigned               NOT NULL DEFAULT '1' COMMENT '状态',
    `parent_id`   int(11)                           NOT NULL DEFAULT '0' COMMENT '父id',
    `name`        varchar(64) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '名称',
    `sort`        int(11)                           NOT NULL DEFAULT '0' COMMENT '排序',
    `level`       tinyint(1)                        NOT NULL DEFAULT '0' COMMENT '等级',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 94
  DEFAULT CHARSET = utf8 COMMENT ='行业类别表';

-- ----------------------------
-- Table structure for verification_code_log
-- ----------------------------
DROP TABLE IF EXISTS `verification_code_log`;
CREATE TABLE `verification_code_log`
(
    `id`                  int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`            datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`              tinyint(1)   NOT NULL DEFAULT '9' COMMENT '9申请中，1获取成功，-1获取失败',
    `admin_id`            int(11)               DEFAULT '0' COMMENT '获取的人员',
    `type`                tinyint(1)   NOT NULL DEFAULT '0' COMMENT '1手机号，2邮箱',
    `verification_value`  varchar(256) NOT NULL DEFAULT '' COMMENT '内容',
    `attachment_file_ids` varchar(256) NOT NULL DEFAULT '' COMMENT '附件ids',
    `remark`              varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
    `reason`              varchar(512) NOT NULL DEFAULT '' COMMENT '失败原因',
    PRIMARY KEY (`id`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_add_time` (`add_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 6
  DEFAULT CHARSET = utf8mb4 COMMENT ='验证码获取日志';

-- ----------------------------
-- Table structure for welfare_label
-- ----------------------------
DROP TABLE IF EXISTS `welfare_label`;
CREATE TABLE `welfare_label`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime    NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `status`      tinyint(1)  NOT NULL DEFAULT '0' COMMENT '状态',
    `name`        varchar(90) NOT NULL DEFAULT '' COMMENT '福利标签名称',
    `is_system`   tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否系统标签0否，1是',
    `is_delete`   tinyint(1)  NOT NULL DEFAULT '0' COMMENT '是否删除0否，1是',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 803
  DEFAULT CHARSET = utf8mb4 COMMENT ='福利标签表';

-- ----------------------------
-- Table structure for wx_public_callback_log
-- ----------------------------
DROP TABLE IF EXISTS `wx_public_callback_log`;
CREATE TABLE `wx_public_callback_log`
(
    `id`       int(11)       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `add_time` datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `openid`   varchar(64)   NOT NULL DEFAULT '' COMMENT 'openid',
    `data`     varchar(2048) NOT NULL DEFAULT '' COMMENT '实际的内容，使用json保存',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_openid` (`openid`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1520718
  DEFAULT CHARSET = utf8mb4;

-- ----------------------------
-- Table structure for wx_work_robot_config
-- ----------------------------
DROP TABLE IF EXISTS `wx_work_robot_config`;
CREATE TABLE `wx_work_robot_config`
(
    `id`       int(11)      NOT NULL AUTO_INCREMENT,
    `add_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `name`     varchar(128) NOT NULL DEFAULT '' COMMENT '名字',
    `key`      varchar(128) NOT NULL DEFAULT '' COMMENT 'key',
    `token`    varchar(128) NOT NULL DEFAULT '' COMMENT 'token',
    `remark`   varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 15
  DEFAULT CHARSET = utf8mb4 COMMENT ='微信机器人配置';

-- ----------------------------
-- Table structure for wx_work_robot_log
-- ----------------------------
DROP TABLE IF EXISTS `wx_work_robot_log`;
CREATE TABLE `wx_work_robot_log`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    `status`      tinyint(1)   NOT NULL DEFAULT '1' COMMENT '发送状态，1成功，2失败',
    `robot_id`    int(11)      NOT NULL COMMENT '关联的机器人ID，关联wx_work_robot_config.id',
    `token`       varchar(128) NOT NULL DEFAULT '' COMMENT '发送时使用的token快照',
    `msg_type`    varchar(32)  NOT NULL DEFAULT '' COMMENT '消息类型，如 text、markdown、image 等',
    `msg_content` text         NOT NULL COMMENT '发送的消息内容，原始内容',
    `response`    text         NOT NULL COMMENT '接口返回结果',
    PRIMARY KEY (`id`),
    KEY `idx_robot_id` (`robot_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 18052
  DEFAULT CHARSET = utf8mb4 COMMENT ='企业微信机器人发送日志';

SET FOREIGN_KEY_CHECKS = 1;
