<?php

namespace frontendPc\models;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyInterview;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseCompanyViewResume;
use common\base\models\BaseDictionary;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyHandleLog;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobApplyTopEquityRecord;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeWork;
use common\base\models\BaseJob;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\helpers\UUIDHelper;
use common\helpers\ValidateHelper;
use common\libs\Cache;
use common\libs\CompanyAuthority\CompanyAuthorityClassify;
use common\models\Announcement;
use mysql_xdevapi\XSession;
use Yii;
use yii\base\Exception;
use yii\db\Query;
use yii\helpers\Url;

class JobApply extends BaseJobApply
{
    /**
     * 全部简历列表
     * @param $keywords
     * @param $memberId
     * @return array
     * @throws \Exception
     */
    public static function getSearchJobApplyList($keywords, $memberId): array
    {
        //验证字段并返回列表
        $jobList = self::validateSearchKeyWords($keywords, $memberId);
        //获取地区表缓存
        $cache     = Yii::$app->cache;
        $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
        if (!$areaCache) {
            $areaCache = BaseArea::setAreaCache();
        }

        //获取是否代投字典
        $sourceList = self::SOURCE_LIST;

        //状态字典
        $statusList = self::STATUS_LIST;

        $workYearsList     = BaseDictionary::WORK_YEARS_LIST;
        $workYearsSortList = [];

        $changeDateList = [
            'add_time',
            'refresh_time',
            'work_begin_date',
            'work_end_date',
            'education_begin_date',
            'education_end_date',
        ];

        foreach ($jobList['list'] as $k => $job) {
            $jobList['list'][$k]['avatar'] = FileHelper::getFullUrl($job['avatar']);
            //根据生日计算年龄
            $age = TimeHelper::countYears($job['birthday']);
            //获取最早工作年限
            $workBeginDate = BaseResumeWork::selectInfo(['resume_id' => $job['resume_id']], ['begin_date'],
                'begin_date asc');

            //根据开始工作时间计算工作年限
            $workYears = TimeHelper::countYears($workBeginDate['begin_date']);
            //获取简历附件，如果没有返回null
            $jobList['list'][$k]['resumeAccessory']       = BaseResumeAttachment::selectInfo(['resume_id' => $job['resume_id']],
                ['file_url']);
            $jobList['list'][$k]['cityName']              = $areaCache[$job['city_id']]['name'];
            $jobList['list'][$k]['householdRegisterName'] = $areaCache[$job['household_register_id']]['name'];
            $jobList['list'][$k]['age']                   = $age;
            $jobList['list'][$k]['sourceText']            = $sourceList[$job['source']];
            $jobList['list'][$k]['workYears']             = $workYears;
            $jobList['list'][$k]['companyTag']            = [];
            if (strlen($jobList['list'][$k]['company_tag']) > 0) {
                $jobList['list'][$k]['companyTag'] = explode(',', $jobList['list'][$k]['company_tag']);
            }
            $jobList['list'][$k]['educationMajorName'] = BaseMajor::getMajorName($jobList['list'][$k]['education_major_id']);
            $jobList['list'][$k]['statusName']         = $statusList[$jobList['list'][$k]['status']];

            $educationId                            = BaseResumeEducation::findOneVal(['id' => $job['last_education_id']],
                'education_id');
            $jobList['list'][$k]['educationName']   = BaseDictionary::getEducationName($educationId);
            $getInterviewAmountWhere                = [
                'job_apply_id' => $jobList['list'][$k]['apply_id'],
            ];
            $jobList['list'][$k]['interviewAmount'] = BaseCompanyInterview::getInterviewAmount($getInterviewAmountWhere);
            if ($keywords['work_years']) {
                if ($workYears >= $workYearsList[$keywords['work_years']]['min'] && $workYears <= $workYearsList[$keywords['work_years']]['max']) {
                    $workYearsSortList[] = $jobList['list'][$k];
                } else {
                    $jobList['page']['count'] = --$jobList['page']['count'];
                }
            }

            foreach ($changeDateList as $item) {
                if (strtotime($job[$item]) < 1) {
                    $jobList['list'][$k][$item] = '-';
                }
            }
        }
        if ($keywords['work_years']) {
            $jobList['list'] = $workYearsSortList;
        }

        //工作年限排序
        if ($keywords['sort_work_years'] == self::ORDER_BY_DESC) {
            $jobList['list'] = array_values(ArrayHelper::arraySort($jobList['list'], 'workYears', 'desc'));
        } elseif ($keywords['sort_work_years'] == self::ORDER_BY_ASC) {
            $jobList['list'] = array_values(ArrayHelper::arraySort($jobList['list'], 'workYears'));
        }

        return $jobList;
    }

    /**
     * 验证搜索关键词并返回列表
     * @param $keywords
     * @param $memberId
     * @return array
     */
    public static function validateSearchKeyWords($keywords, $memberId): array
    {
        $limit   = $keywords['limit'];
        $offset  = ($keywords['page'] - 1) * $limit;
        $where   = ['and'];
        $orderBy = 'apply.add_time desc';
        $where[] = [
            'apply.company_member_id' => $memberId,
        ];
        //职位名称
        if ($keywords['job_name']) {
            $where[] = [
                'or',
                [
                    'like',
                    'apply.job_name',
                    $keywords['job_name'],
                ],
                [
                    'like',
                    'apply.id',
                    $keywords['job_name'],
                ],
            ];
        }
        //职位id
        if ($keywords['id']) {
            $where[] = [
                'apply.id' => $keywords['id'],
            ];
        }
        //职位类型
        if ($keywords['job_category_id']) {
            $where[] = [
                'j.job_category_id' => $keywords['job_category_id'],
            ];
        }
        //最高学历
        if ($keywords['education']) {
            $where[] = [
                'r.education' => $keywords['education'],
            ];
        }
        //国籍户籍
        if ($keywords['household_register_id']) {
            $where[] = [
                'r.household_register_id' => $keywords['household_register_id'],
            ];
        }
        //应聘时间
        if ($keywords['add_time']) {
            $where[] = [
                'apply.add_time' => $keywords['add_time'],
            ];
        }
        //是否海外经历
        if ($keywords['is_abroad']) {
            $where[] = [
                'education.is_abroad' => $keywords['is_abroad'],
            ];
        }
        //职称
        if ($keywords['title_id']) {
            // $where[] = [
            //     'r.title_id' => $keywords['title_id'],
            // ];

            $titleArray = explode(',', $keywords['title_id']);
            $titleList  = BaseDictionary::getAllChildTitleArray($titleArray);

            $titleCondition = ['or'];
            foreach ($titleList as $item) {
                $titleCondition[] = "find_in_set(" . $item . ",r.title_id)";
            }
            $where[] = $titleCondition;
        }
        //性别
        if ($keywords['gender']) {
            $where[] = [
                'r.gender' => $keywords['gender'],
            ];
        }
        //简历来源
        if ($keywords['source']) {
            $where[] = [
                'apply.source' => $keywords['source'],
            ];
        }
        //状态
        if ($keywords['status']) {
            //待处理
            if ($keywords['status'] == self::STATUS_HANDLE_WAIT) {
                $where[] = [
                    'apply.status' => [
                        $keywords['status'],
                        // self::STATUS_EMPLOYED_REVOCATION,
                        // self::STATUS_ENTRY_NO_REVOCATION,
                        // self::STATUS_ENTRY_REVOCATION,
                        // self::STATUS_INAPPROPRIATE_REVOCATION,
                    ],
                ];
                //通过初筛
            } elseif ($keywords['status'] == self::STATUS_THROUGH_FIRST) {
                $where[] = [
                    'apply.status' => [
                        $keywords['status'],
                        // self::STATUS_BLOCK_CALL,
                        // self::STATUS_INTENTION_NO,
                    ],
                ];
                //不合适
            } elseif ($keywords['status'] == self::STATUS_INAPPROPRIATE) {
                $where[] = [
                    'apply.status' => [
                        $keywords['status'],
                    ],
                ];
                //已录用
            } elseif ($keywords['status'] == self::STATUS_EMPLOYED) {
                $where[] = [
                    'apply.status' => [
                        $keywords['status'],
                        // self::STATUS_ENTRY,
                        // self::STATUS_ENTRY_NO,
                    ],
                ];
                //已邀面
            } elseif ($keywords['status'] == self::STATUS_SEND_INVITATION) {
                //根据面试日志获取简历信息
                //获取已邀面id
                $where[] = [
                    'apply.is_invitation' => self::IS_INVITATION_YES,
                ];
            }
        }
        //年龄搜索
        if ($keywords['age']) {
            $ageList = BaseDictionary::getBirthday();
            $where[] = [
                'between',
                'r.birthday',
                $ageList[$keywords['age']]['min'],
                $ageList[$keywords['age']]['max'],
            ];
        }
        //职位名称排序
        if ($keywords['sort_job_name'] == self::ORDER_BY_DESC) {
            $orderBy = 'apply.job_name desc';
        } else {
            if ($keywords['sort_job_name'] == self::ORDER_BY_ASC) {
                $orderBy = 'apply.job_name asc';
            }
        }
        //学历排序
        if ($keywords['sort_education'] == self::ORDER_BY_DESC) {
            $orderBy = 'education desc';
        } else {
            if ($keywords['sort_education'] == self::ORDER_BY_ASC) {
                $orderBy = 'education asc';
            }
        }
        //投递时间排序
        if ($keywords['sort_add_time'] == self::ORDER_BY_DESC) {
            $orderBy = 'apply.add_time desc';
        } elseif ($keywords['sort_add_time'] == self::ORDER_BY_ASC) {
            $orderBy = 'apply.add_time asc';
        }
        //工作地点排序
        if ($keywords['sort_city'] == self::ORDER_BY_DESC) {
            $orderBy = 'j.city_id desc';
        } elseif ($keywords['sort_city'] == self::ORDER_BY_ASC) {
            $orderBy = 'j.city_id asc';
        }
        //性别排序
        if ($keywords['sort_gender'] == self::ORDER_BY_DESC) {
            $orderBy = 'r.gender desc';
        } elseif ($keywords['sort_gender'] == self::ORDER_BY_ASC) {
            $orderBy = 'r.gender asc';
        }
        $select = [
            'apply.id as apply_id',
            'apply.resume_id',
            'apply.job_id',
            'apply.resume_name',
            'apply.job_name',
            'r.gender',
            'apply.resume_member_id',
            'r.education',
            'r.last_education_id',
            //学历水平
            'r.birthday',
            //根据生日计算年龄
            'education.major_id as education_major_id',
            //学校专业/写一个获取专业名称的方法
            'r.household_register_id',
            //户籍所在地
            'j.city_id',
            //工作地点
            'apply.company_tag',
            //企业添加的简历标签
            'education.begin_date as education_begin_date',
            //教育开始时间
            'education.end_date as education_end_date',
            //结束时间
            'education.school as education_school',
            //学校
            'work.begin_date as work_begin_date',
            //工作开始时间
            'work.end_date as work_end_date',
            //工作结束时间
            'work.company as work_company',
            //上一家公司名称
            'work.job_name as work_job_name',
            //上一家工作名称
            'apply.add_time',
            //投递时间
            'r.refresh_time',
            'apply.status',
            //投递表状态
            'apply.source',
            //投递来源
            'apply.is_check',
            //是否查看
            'm.avatar',
        ];

        $query = self::find()
            ->alias('apply')
            ->leftJoin('member as m', 'apply.resume_member_id = m.id')
            ->leftJoin('resume as r', 'r.id = apply.resume_id')
            ->leftJoin('resume_education as education',
                'education.resume_id = r.id and education.id = (select id from resume_education where resume_id = r.id order by begin_date desc limit 1)')
            ->leftJoin('resume_intention as intention',
                'intention.resume_id = r.id and intention.id = (select id from resume_intention where resume_id = r.id order by id desc limit 1)')
            ->leftJoin('job as j', 'j.id = apply.job_id')
            ->leftJoin('resume_work as work',
                'work.resume_id = r.id and work.id = (select id from resume_work where resume_id = r.id order by begin_date desc limit 1)')
            ->where($where);

        $count = $query->count();

        $list = $query->limit($limit)
            ->select($select)
            ->offset($offset)
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as $k => $item) {
            $resumeAttachmentAccount = BaseResumeAttachment::find()
                ->where(['resume_id' => $item['resume_id']])
                ->asArray()
                ->count();

            if ($resumeAttachmentAccount > 0) {
                $list [$k]['has_attachment'] = 1;
            } else {
                $list [$k]['has_attachment'] = 2;
            }
        }

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => $limit,
                'page'  => $keywords['page'],
            ],
        ];
    }

    /**
     * 更新标签
     * @param $request
     * @throws Exception
     */
    public static function saveCompanyTag($request)
    {
        if ($request['id'] < 0) {
            throw new Exception('参数错误');
        }
        $memberId       = Yii::$app->user->id;
        $request['tag'] = ($request['tag'] != BaseJob::DELIVERY_TYPE_OUTSIDE) ? BaseJob::DELIVERY_TYPE_INSIDE : $request['tag'];
        if ($request['tag'] == BaseJob::DELIVERY_TYPE_INSIDE) {
            $jobApplyModel = BaseJobApply::findOne([
                'id'         => $request['id'],
                //'company_member_id' => $memberId,
                'company_id' => BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id'),
            ]);
            if (!$jobApplyModel) {
                throw new Exception('非法请求');
            }
            $jobApplyModel->company_tag = $request['company_tag'];

            if (!$jobApplyModel->save()) {
                throw new Exception('更新标签失败');
            }
        } elseif ($request['tag'] == BaseJob::DELIVERY_TYPE_OUTSIDE) {
            $jobApplyModel = BaseOffSiteJobApply::findOne($request['id']);
            if (!$jobApplyModel) {
                throw new Exception('非法请求');
            }
            $jobApplyModel->company_tag = $request['company_tag'];
            if (!$jobApplyModel->save()) {
                throw new Exception('更新标签失败');
            }
        } else {
            throw new Exception('参数错误');
        }
    }

    /**
     * 获取所有状态下简历数量
     * @return int[]
     */
    public static function getStatusAmount($memberId): array
    {
        return BaseJobApply::find()
            ->alias('a')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id=a.job_id')
            ->select([
                'count(a.id) as allStatusAmount',
                'count(CASE a.status WHEN 1 THEN 1 ElSE null END) as waitStatusAmount',
                'count(CASE a.status WHEN 2 THEN 1 ELSE null END) as throughFirstStatusAmount',
                'count(CASE a.is_invitation WHEN 1 THEN 1 ELSE null END) as interviewAmount',
                'count(CASE a.status WHEN 4 THEN 1 ELSE null END) as inappropriateStatusAmount',
                'count(CASE a.status WHEN 5 THEN 1 ELSE null END) as employedStatusAmount',
            ])
            ->where([
                'a.company_member_id' => $memberId,
                'j.is_show'           => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere([
                'in',
                'j.status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->asArray()
            ->one();
    }

    /**
     * 获取待处理状态总量
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getWaitStatusAmount($memberId)
    {
        return self::find()
            ->alias('a')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id=a.job_id')
            ->where([
                'a.status'            => self::STATUS_HANDLE_WAIT,
                'a.company_member_id' => $memberId,
                'j.is_show'           => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere([
                'in',
                'j.status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->count();
    }

    /**
     * 根据状态统计数量
     * @param        $memberId
     * @param        $status
     * @param string $isCheck
     * @return int
     */
    public static function count($memberId, $status, string $isCheck = '1'): int
    {
        if ($isCheck == self::IS_CHECK_NO) {
            $orWhere = [
                'is_check'          => self::IS_CHECK_NO,
                'company_member_id' => $memberId,
            ];
        } else {
            $orWhere = [];
        }
        $where = [
            'company_member_id' => $memberId,
            'status'            => $status,
        ];

        $select = 'id,status,is_check';

        return self::getAmount($where, $select, $orWhere);
    }

    /**
     * 更改简历申请状态
     * @param $request
     * @param $status
     * @param $memberId
     * @return void
     * @throws Exception
     */
    public static function saveStatus($request, $status, $memberId)
    {
        // $jobApplyIds = explode(',', $request['ids']);
        //
        // try {
        //     foreach ($jobApplyIds as $jobApplyId) {
        //         $jobApplyInfo = self::findOne([
        //             'company_member_id' => $memberId,
        //             'id'                => $jobApplyId,
        //         ]);
        //         if (!$jobApplyInfo) {
        //             throw new Exception('非法操作');
        //         }
        //
        //         $statusOperation = self::STATUS_OPERATION;
        //
        //         if (in_array($status, $statusOperation[$jobApplyInfo->status])) {
        //             $jobApplyInfo->status = $status;
        //             if ($status == self::STATUS_SEND_INVITATION) {
        //                 // 邀请面试,更改状态
        //                 $jobApplyInfo->is_invitation = self::IS_INVITATION_YES;
        //             }
        //             $companyInfo = BaseCompany::getCompanyInfo($memberId);
        //             BaseJobApplyHandleLog::createJobApplyHandleLogInfo($jobApplyId, BaseMember::TYPE_COMPANY,
        //                 $companyInfo['full_name'], $status);
        //             if (!$jobApplyInfo->save()) {
        //                 throw new Exception($jobApplyInfo->getFirstErrorsMessage());
        //             }
        //         } else {
        //             throw new Exception('此状态下无法进行该操作');
        //         }
        //     }
        // } catch (\Exception $e) {
        //     throw new Exception($e->getMessage());
        // }
    }

    /**
     * @throws Exception
     */
    public static function saveNote($data)
    {
        $model = self::findOne($data['id']);

        $model->note = $data['note'];

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
    }

    //////////////////////////////新接口

    /**
     * 按公告查看--简历列表
     * @param $keywords
     * @return array
     * @throws \Exception
     */
    public static function getResumeByAnnouncement($keywords): array
    {
        $memberId = Yii::$app->user->id;
        $nowTime  = CUR_DATETIME;

        $select = [
            'a.add_time',
            'a.id',
            'c.status',
            'a.audit_status',
            'a.title',
            'a.period_date',
            'a.offline_time',
            'a.major_ids',
            'c.refresh_time',
            'c.real_refresh_time',
            'c.release_time',
            'c.first_release_time',
            'GROUP_CONCAT(distinct j.city_id SEPARATOR "|") as city_ids',
            'GROUP_CONCAT(distinct j.id SEPARATOR "|") as job_ids',
            'GROUP_CONCAT(distinct y.id SEPARATOR "|") as apply_ids',
            'GROUP_CONCAT(y.add_time SEPARATOR "|") as apply_times',
            'count(distinct j.id) as jobAccount',
            'GROUP_CONCAT(j.amount SEPARATOR ",") as amountList',
        ];

        $statusList = [
            BaseArticle::STATUS_ONLINE,
            BaseArticle::STATUS_OFFLINE,
        ];

        $jobStatusList = [
            BaseJob::STATUS_ONLINE,
            BaseJob::STATUS_OFFLINE,
        ];

        $query = BaseAnnouncement::find()
            ->alias('a')
            ->leftJoin(['c' => BaseArticle::tableName()], 'c.id = a.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id = a.id')
            ->leftJoin(['y' => BaseJobApply::tableName()], 'y.job_id = j.id')
            ->select($select)
            ->where([
                'in',
                'c.status',
                $statusList,
            ])
            ->andwhere([
                //'a.member_id' => $memberId,
                'c.type'    => BaseArticle::TYPE_ANNOUNCEMENT,
                'c.is_show' => BaseArticle::IS_SHOW_YES,
            ])
            ->groupBy(['a.id']);

        /** 职位协同*/
        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'a.company_id',
            'memberId'        => $memberId,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);
        if ($authorityList) {
            $query = $authorityList['query'];
        }

        $query->andFilterCompare('j.status', $jobStatusList, 'in');
        if (strlen($keywords['status']) > 0) {
            if ($keywords['status'] == 0) {
                $keywords['status'] = BaseArticle::STATUS_OFFLINE;
            }
            $query->andFilterCompare('c.status', $keywords['status']);
        }

        if ($keywords['title']) {
            $query->andFilterCompare('a.title', $keywords['title'], 'like');
        }
        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'c.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'c.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        $orderBy = 'c.status desc,c.release_time desc,a.add_time desc';

        $historyList = $query->orderBy($orderBy)
            ->asArray()
            ->all();

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        // 日期时间格式数据数组
        $timeArray           = [
            'add_time',
            'period_date',
            'refresh_time',
            'release_time',
            'offline_time',
            'real_refresh_time',
        ];
        $statusAuditList     = BaseArticle::STATUS_LIST;
        $announcementIds     = [];
        $companyMemberInfoId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'id');
        foreach ($list as $k => $item) {
            if ($item['first_release_time'] != TimeHelper::ZERO_TIME) {
                $list[$k]['announcementUrl'] = UrlHelper::createAnnouncementDetailPath($item['id']);
            }

            array_push($announcementIds, $item['id']);
            $list[$k]['uid']          = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['id']);
            $list[$k]['status_title'] = $statusAuditList[$item['status']];

            //统计信息处理---职位累计简历、职位近7天简历、城市、职位个数等
            $major = "";
            if ($item['major_ids'] && strlen($item['major_ids'] > 0)) {
                $majorIds = explode(',', $item['major_ids']);
                foreach ($majorIds as $v) {
                    $major .= BaseMajor::getMajorName($v) . ",";
                }
            }
            $list[$k]['major'] = substr($major, 0, -1);

            $announcementInfoList                  = BaseAnnouncement::getAnnouncementInfoList($item['id']);
            $list[$k]['job_amount']                = $announcementInfoList['jobAccount'];
            $list[$k]['amount']                    = $announcementInfoList['amountAccount'];
            $list[$k]['city']                      = $announcementInfoList['cityName'];
            $list[$k]['announcementEducationName'] = $announcementInfoList['announcementEducationName'];
            //            $list[$k]['week_amount']  = $announcementInfoList['jobApplyWeekAccount'];
            //            $list[$k]['apply_amount'] = $announcementInfoList['jobApplyAccount'];

            // 职位投递统计
            $jobApplyList                       = BaseJobApply::announcementJobApplyStatistics($item['id']);
            $list[$k]['unreadAccount']          = $jobApplyList['unreadApplyNum'] ?: 0;
            $list[$k]['waitInterviewAccount']   = $jobApplyList['jobInterviewNum'] ?: 0;
            $list[$k]['allApplyAccount']        = $jobApplyList['allJobApplyNum'] ?: 0;
            $list[$k]['outApplyAccount']        = $jobApplyList['linkApplyNum'] ?: 0;
            $list[$k]['isPlatformOrEmailApply'] = $jobApplyList['isPlatformOrEmailApply'];
            $list[$k]['isLinkApply']            = $jobApplyList['isLinkApply'];

            $attentionDays = '';
            $countdownDays = (int)TimeHelper::reduceDates($item['period_date'], $nowTime);
            if ($countdownDays > 0) {
                $attentionDays = ' (' . $countdownDays . '天后下线)';
            }
            if (strtotime($item['period_date']) < 1) {
                if (strtotime($item['release_time']) < 1) {
                    $releaseTime = CUR_DATETIME;
                } else {
                    $releaseTime = $item['release_time'];
                }

                // 没有填截止日期，默认给发布时间后的365天
                $periodDate          = date('Y-m-d H:i:s',
                    strtotime($releaseTime) + 60 * 60 * 24 * BaseAnnouncement::ADD_RELEASE_AGAIN_DAY);
                $item['period_date'] = $periodDate;
                $countdownDays       = (int)TimeHelper::reduceDates($periodDate, $nowTime);
                if ($countdownDays > 0) {
                    $attentionDays = ' (' . $countdownDays . '天后下线)';
                }
            }

            $list[$k]['period_date_title'] = $item['period_date'] . $attentionDays;

            // 日期时间类型处理
            foreach ($timeArray as $value) {
                if (strtotime($item[$value]) < 1) {
                    $list[$k][$value] = "";
                } else {
                    $list[$k][$value] = TimeHelper::formatDateByYear($item[$value]);
                }
            }
            //公告过期时间
            if ($item['status'] == BaseArticle::STATUS_ONLINE || ($item['status'] == BaseArticle::STATUS_OFFLINE && $item['offline_time'] == '0000-00-00 00:00:00')) {
                $list[$k]['offline_time'] = TimeHelper::formatDateByYear($item['period_date']);
            }
            /** 新增协同职位信息 */
            /** 显示该公告下，该子账号协同的职位数量*/
            $jobContactAccount               = BaseJobContactSynergy::find()
                ->where([
                    'company_member_info_id' => $companyMemberInfoId,
                    'announcement_id'        => $item['id'],
                ])
                ->count();
            $list[$k]['job_contact_account'] = $jobContactAccount;
        }

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 按职位查看--简历列表
     * @param $keywords
     * @return array
     * @throws \Exception
     */
    public static function getResumeByJob($keywords): array
    {
        $memberId  = Yii::$app->user->id;
        $nowTime   = CUR_DATETIME;
        $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');

        $select = [
            'j.add_time',
            'j.status',
            'a.resume_name',
            'a.resume_member_id',
            'j.id',
            'j.name as job_name',
            'j.period_date',
            'j.education_type',
            'j.wage_type',
            'j.experience_type',
            'j.department',
            'j.city_id',
            'j.refresh_time',
            'j.delivery_type',
            'j.delivery_way',
            'j.release_time',
            'j.offline_time',
            'j.offline_type',
            'j.announcement_id',
            'j.real_refresh_time',
            'j.first_release_time',
            'j.min_wage',
            'j.max_wage',
            'j.code',
            'count(distinct a.id) as apply_amount',
            'sum(if(a.status=1,1,0)) as wait_amount',
        ];

        $statusList = [
            BaseJob::STATUS_ONLINE,
            BaseJob::STATUS_OFFLINE,
        ];

        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['a' => BaseJobApply::tableName()], 'a.job_id = j.id')
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'an.id=j.announcement_id')
            ->select($select)
            ->andWhere([
                'or',
                ['j.delivery_type' => BaseJob::DELIVERY_TYPE_INSIDE],
                [
                    'j.delivery_type'  => BaseJob::DELIVERY_TYPE_UP_ANNOUNCEMENT,
                    'an.delivery_type' => BaseAnnouncement::DELIVERY_TYPE_INSIDE,
                ],
            ])
            ->groupBy(['j.id']);

        /** 职位协同信息 */
        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'j.company_id',
            'memberId'        => $memberId,
            'query'           => $query,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);

        if ($authorityList) {
            $query = $authorityList['query'];
        }
        $historyQuery         = BaseJobApply::find()
            ->select([
                'count(ja.id) as allApplyAmount',
                'sum(if(ja.status=1,1,0)) as waitAllAmount',
            ])
            ->alias('ja')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id=ja.job_id')
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'an.id=j.announcement_id');
        $historyAuthorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'ja.company_id',
            'memberId'        => $memberId,
            'query'           => $historyQuery,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);
        if ($historyAuthorityList) {
            $historyQuery = $historyAuthorityList['query'];
        }
        if (strlen($keywords['status']) > 0) {
            $query->andFilterCompare('j.status', $keywords['status']);
            $historyQuery->andFilterCompare('j.status', $keywords['status']);
        } else {
            $query->andWhere([
                'in',
                'j.status',
                $statusList,
            ]);
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $query->andFilterCompare('j.job_category_id', $jobCategoryId, 'in');
            $historyQuery->andFilterCompare('j.job_category_id', $jobCategoryId, 'in');
        }

        if ($keywords['name']) {
            if (is_numeric($keywords['name'])) {
                if (strlen($keywords['name'] == 8)) {
                    $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                    $query->andFilterCompare('j.id', (int)$nameChangeUid);
                    $historyQuery->andFilterCompare('j.id', (int)$nameChangeUid);
                } else {
                    $query->andFilterCompare('j.id', (int)$keywords['name']);
                    $historyQuery->andFilterCompare('j.id', (int)$keywords['name']);
                }
            } else {
                $query->andFilterCompare('j.name', $keywords['name'], 'like');
                $historyQuery->andFilterCompare('j.name', $keywords['name'], 'like');
            }
        }

        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
            $historyQuery->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
            $historyQuery->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        $orderBy = 'j.refresh_time desc,j.release_time desc,j.add_time desc';

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        // todo 返回数据处理
        $statusList        = BaseJob::JOB_STATUS_NAME;
        $educationTypeList = BaseDictionary::getEducationList();
        $experienceList    = BaseDictionary::getExperienceList();

        // 日期时间格式数据数组
        $timeArray = [
            'add_time',
            'period_date',
            'refresh_time',
            'release_time',
            'offline_time',
            'real_refresh_time',
        ];
        foreach ($list as $k => $item) {
            if ($list['first_release_time'] != TimeHelper::ZERO_TIME) {
                $list[$k]['announcementUrl'] = UrlHelper::createAnnouncementDetailPath($item['announcement_id']);
                $list[$k]['jobUrl'] = UrlHelper::createJobDetailPath($item['id']);
            }

            $list[$k]['uid']          = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $item['id']);
            $list[$k]['status_title'] = $statusList[$item['status']];

            $wage                = BaseJob::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']);
            $wage                = $wage ?: '';
            $experienceTypeTitle = $experienceList[$item['experience_type']] ?: '';
            $educationTypeTitle  = $educationTypeList[$item['education_type']] ?: '';
            $department          = $item['department'] ?: '';
            $cityName            = BaseArea::getAreaName($item['city_id']);
            $content             = [];
            if ($cityName) {
                $content[] = $cityName;
            }
            if ($educationTypeTitle) {
                $content[] = $educationTypeTitle;
            }
            if ($experienceTypeTitle) {
                $content[] = $experienceTypeTitle;
            }
            if ($wage) {
                $content[] = $wage;
            }
            if ($department) {
                $content[] = $department;
            }
            $list[$k]['content'] = implode(' | ', $content);
            $title               = '';
            if ($item['delivery_way'] > 0) {
                $list[$k]['deliveryWayTxt'] = BaseJob::DELIVERY_WAY_NAME[$item['delivery_way']];
            }
            if ($item['announcement_id']) {
                $announcementInfo               = BaseAnnouncement::findOne($item['announcement_id']);
                $title                          = $announcementInfo->title;
                $list[$k]['announcementStatus'] = BaseArticle::findOneVal(['id' => $announcementInfo->article_id],
                    'status');
                if ($item['delivery_way'] == 0) {
                    $list[$k]['deliveryWayTxt'] = BaseAnnouncement::DELIVERY_WAY_NAME[$announcementInfo->delivery_way];
                }
            }
            $list[$k]['title'] = $title;

            //跟公告
            if ($item['delivery_type'] == 0 && $item['delivery_way'] == 0 && $item['announcement_id'] > 0) {
                $announcementInfo          = BaseAnnouncement::findOne($item['announcement_id']);
                $list[$k]['delivery_type'] = $announcementInfo->delivery_type;
                $list[$k]['delivery_way']  = $announcementInfo->delivery_way;
            }

            // 日期时间类型处理
            $attentionDays = '';
            $countdownDays = (int)TimeHelper::reduceDates($item['period_date'], $nowTime);
            if ($countdownDays > 0) {
                $attentionDays = ' (' . $countdownDays . '天后下线)';
            }
            if ($item['status'] == BaseJob::STATUS_OFFLINE && $item['offline_type'] == BaseJob::OFFLINE_TYPE_AUTO) {
                if ($item['period_date'] == '0000-00-00 00:00:00') {
                    $dateBool = true;
                    if ($item['announcement_id'] > 0) {
                        $announcementInfo = BaseAnnouncement::findOne($item['announcement_id']);
                        $articleInfo      = BaseArticle::findOne($announcementInfo->article_id);
                        if ($announcementInfo->period_date != '0000-00-00 00:00:00' && $articleInfo->status == BaseArticle::STATUS_OFFLINE) {
                            $periodDate = $announcementInfo->period_date;
                            $dateBool   = false;
                        }
                    }
                    if ($dateBool) {
                        if (strtotime($item['release_time']) < 1) {
                            $releaseTime = CUR_DATETIME;
                        } else {
                            $releaseTime = $item['release_time'];
                        }
                        // 没有填截止日期，默认给发布时间后的365天
                        $periodDate = date('Y-m-d H:i:s',
                            strtotime($releaseTime) + 60 * 60 * 24 * BaseAnnouncement::ADD_RELEASE_AGAIN_DAY);
                    }
                } else {
                    $periodDate = $item['period_date'];
                }
                $item['offline_time'] = $periodDate;
            }
            foreach ($timeArray as $value) {
                if (strtotime($item[$value]) < 1) {
                    $list[$k][$value] = "";
                } else {
                    $list[$k][$value] = TimeHelper::formatDateByYear($item[$value]);
                }
            }

            $list[$k]['period_date_title'] = $list[$k]['period_date'] . $attentionDays;
        }

        //todo 统计历史信息--职位总个数、近一周累计简历总数、历史累计简历总数

        $historyTotal = $historyQuery->asArray()
            ->one();

        return [
            'list'       => $list,
            'page'       => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
            'statistics' => [
                'job_amount'     => (int)$count,
                'waitAllAmount'  => $historyTotal['waitAllAmount'],
                'allApplyAmount' => $historyTotal['allApplyAmount'],
            ],
        ];
    }

    /**
     * 全部简历列表（条件查询）
     * @param $keywords
     * @return array
     * @throws \Exception
     */
    public static function getResumeList($keywords): array
    {
        $memberId = Yii::$app->user->id;

        $select = [
            'a.status',
            'a.source',
            'a.resume_id',
            'a.add_time',
            'a.status',
            'a.job_id',
            'a.resume_attachment_id',
            'a.company_tag',
            'a.id',
            'a.company_mark_status',
            'a.is_check',
            'a.is_invitation',
            'j.name as job_name',
            'j.announcement_id',
            'r.name',
            'r.gender',
            'r.age',
            'r.work_experience',
            'r.last_education_id',
            'r.last_work_id',
            'r.education',
            'r.refresh_time',
            'i.area_id',
            'i.min_wage',
            'i.max_wage',
            'm.id as member_id',
            'm.avatar',
            'm.mobile',
            'ann.title as announcement_title',
        ];

        $query = BaseJobApply::find()
            ->alias('a')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id=a.resume_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.job_id = j.id')
            ->leftJoin(['ann' => BaseAnnouncement::tableName()], 'ann.id = j.announcement_id')
            ->leftJoin(['el' => BaseResumeEducation::tableName()], 'el.id = r.last_education_id and el.status = 1')
            ->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.resume_id = r.id and e.status = 1')
            ->leftJoin(['wk' => BaseResumeWork::tableName()], 'wk.resume_id = r.id and wk.status = 1')
            ->leftJoin(['i' => BaseResumeIntention::tableName()], 'i.resume_id = r.id')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = a.resume_member_id')
            ->where([
                'j.status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('a.id');

        $authorityList        = (new CompanyAuthorityClassify())->run([
            'associatedField'  => 'a.company_id',
            'memberId'         => $memberId,
            'query'            => $query,
            'returnType'       => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
            'companyAuthority' => 'resumeList',
        ]);
        $companyAuthorityList = [];
        if ($authorityList) {
            $query                = $authorityList['query'];
            $companyAuthorityList = $authorityList['companyAuthorityList'];
        }

        $query->andFilterCompare('a.status', BaseJob::STATUS_DELETE, '<>');
        $query->andFilterCompare('r.household_register_id', $keywords['household_register_id']);
        if ($keywords['education']) {
            $educationArray = explode(',', $keywords['education']);
            $query->andWhere(['el.education_id' => $educationArray]);
        }
        if ($keywords['title_id']) {
            // $title_id = explode(',', $keywords['title_id']);
            // $query->andWhere(['r.title_id' => $title_id]);

            $titleArray = explode(',', $keywords['title_id']);
            $titleList  = BaseDictionary::getAllChildTitleArray($titleArray);

            $titleCondition = ['or'];
            foreach ($titleList as $item) {
                $titleCondition[] = "find_in_set(" . $item . ",r.title_id)";
            }
            $query->andWhere($titleCondition);
        }
        $query->andFilterCompare('r.gender', $keywords['gender']);
        $query->andFilterCompare('a.source', $keywords['source']);
        $query->andFilterCompare('a.job_id', $keywords['job_id']);
        //求职者身份选择（-1缺失，1职场人，2应届生）
        if (!empty($keywords['identityType'])) {
            $query->andWhere(['r.identity_type' => $keywords['identityType']]);
        }

        //毕业时间
        if ($keywords['graduate_begin_date'] && $keywords['graduate_end_date']) {
            $query->andFilterWhere([
                'between',
                'el.end_date',
                TimeHelper::dayToBeginTime($keywords['graduate_begin_date']),
                TimeHelper::dayToEndTime($keywords['graduate_end_date']),
            ]);
        }

        //这里做个数据查询兼容
        //海外经历
        if ($keywords['is_abroad'] > 0) {
            if ($keywords['is_abroad'] == 1) {
                $query->andWhere([
                    'or',
                    [
                        'e.is_abroad' => $keywords['is_abroad'],
                        'e.status'    => BaseResumeEducation::STATUS_ACTIVE,
                    ],
                    [
                        'wk.is_abroad' => $keywords['is_abroad'],
                        'wk.status'    => BaseResumeWork::STATUS_ACTIVE,
                    ],
                ]);
            } elseif ($keywords['is_abroad'] == 2) {
                $resume_ids = BaseJobApply::find()
                    ->alias('a')
                    ->select('r.id')
                    ->leftJoin(['r' => BaseResume::tableName()], 'r.id=a.resume_id')
                    ->leftJoin(['j' => BaseJob::tableName()], 'a.job_id = j.id')
                    ->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.resume_id = r.id and e.status = 1')
                    ->leftJoin(['wk' => BaseResumeWork::tableName()], 'wk.resume_id = r.id  and wk.status = 1')
                    ->andWhere(['a.company_member_id' => $memberId])
                    ->andWhere([
                        'or',
                        [
                            'e.is_abroad' => 1,
                        ],
                        [
                            'wk.is_abroad' => 1,
                        ],
                    ])
                    ->groupBy('a.id')
                    ->column();
                $query->andWhere([
                    'not in',
                    'a.resume_id',
                    $resume_ids,
                ]);
            }
        }

        if ($keywords['is_project_school']) {
            if ($keywords['is_project_school'] == 2) {
                $query->andFilterWhere([
                    'r.is_project_school' => 1,
                ]);
            } else {
                $query->andFilterWhere([
                    '<>',
                    'r.is_project_school',
                    1,
                ]);
            }
        }

        if ($keywords['job_name']) {
            if (is_numeric($keywords['job_name']) && strlen($keywords['job_name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['job_name']);
                $query->andFilterCompare('a.job_id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('j.job_name', $keywords['job_name'], 'like');
            }
        }

        if ($keywords['announcement_name']) {
            if (is_numeric($keywords['announcement_name']) && strlen($keywords['announcement_name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['announcement_name']);
                $query->andWhere([
                    'or',
                    ['j.announcement_id' => (int)$nameChangeUid],
                    // 这里是like
                    [
                        'like',
                        'ann.title',
                        $keywords['announcement_name'],
                    ],
                ]);
            } else {
                $query->andFilterCompare('ann.title', $keywords['announcement_name'], 'like');
            }
        }

        if ($keywords['announcement_id']) {
            if (is_numeric($keywords['announcement_id'])) {
                if (strlen($keywords['announcement_id']) == 8) {
                    $nameChangeUid = UUIDHelper::decryption($keywords['announcement_id']);
                } else {
                    $nameChangeUid = $keywords['announcement_id'];
                }
                $query->andFilterCompare('j.announcement_id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('ann.title', $keywords['announcement_id'], 'like');
            }
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $query->andFilterCompare('j.job_category_id', $jobCategoryId, 'in');
        }

        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'a.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'a.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }

        if ($keywords['age']) {
            //最远范围
            $before = $keywords['age'] + 1;
            $min    = date('Y-m-d', strtotime("-" . $before . "year"));
            $query->andWhere([
                '>',
                'r.birthday',
                $min,
            ]);

            //最近范围
            $back = $keywords['age'];
            $max  = date('Y-m-d', strtotime("-" . $back . "year"));
            $query->andWhere([
                '<=',
                'r.birthday',
                $max,
            ]);
        }

        $workYearsList = BaseDictionary::WORK_YEARS_LIST;
        if ($keywords['work_years']) {
            $maxDate = $workYearsList[$keywords['work_years']]['max'];
            $minDate = $workYearsList[$keywords['work_years']]['min'];
            $query->andWhere([
                '>=',
                'r.work_experience',
                $minDate,
            ]);
            $query->andWhere([
                '<=',
                'r.work_experience',
                $maxDate,
            ]);
        }

        $orderBy = 'a.add_time desc';

        if ($keywords['sort_job_name']) {
            $sort    = $keywords['sort_job_name'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'a.job_name' . $sort;
        }
        if ($keywords['sort_education']) {
            $sort    = $keywords['sort_education'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'r.education' . $sort;
        }
        if ($keywords['sort_age']) {
            $sort    = $keywords['sort_age'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'r.birthday' . $sort;
        }
        if ($keywords['sort_gender']) {
            $sort    = $keywords['sort_gender'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'r.gender' . $sort;
        }
        if ($keywords['sort_work_experience']) {
            $sort    = $keywords['sort_work_experience'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'r.work_experience ' . $sort;
        }
        if ($keywords['sort_add_time']) {
            $sort    = $keywords['sort_add_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'a.add_time' . $sort;
        }
        if ($keywords['sort_city_id']) {
            $sort    = $keywords['sort_city_id'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'j.city_id' . $sort;
        }
        //最后对排序处理一下
        $orderBy = 'a.equity_status desc,' . $orderBy;

        $statisticsList = [
            'waitStatusAmount',
            'throughFirstStatusAmount',
            'interviewAmount',
            'inappropriateStatusAmount',
            'employedStatusAmount',
            'allStatusAmount',
        ];

        if ($keywords['status'] && sizeof($keywords) == 1) {
            $statistics = self::getStatusAmount($memberId);
        } else {
            $allStatusAmount = 0;
            foreach ($statisticsList as $key => $item) {
                $temp = [];
                $num  = (string)$key + 1;
                if ($item != 'allStatusAmount') {
                    $temp = ['a.status' => $num];
                }
                $statistics[$item] = $query->having($temp)
                    ->groupBy('a.id')
                    ->count();
                if ($item != 'allStatusAmount') {
                    $allStatusAmount = $allStatusAmount + $statistics[$item];
                }
                array_pop($temp);
                if ($item == 'interviewAmount') {
                    $temp              = ['a.is_invitation' => BaseJobApply::IS_INVITATION_YES];
                    $statistics[$item] = $query->having($temp)
                        ->groupBy('a.id')
                        ->count();
                }
            }
            $statistics['allStatusAmount'] = $allStatusAmount;
        }

        //这里拿所有有邀面历史的记录
        if ($keywords['status']) {
            if ($keywords['status'] == BaseJobApply::STATUS_SEND_INVITATION) {
                $query->andFilterCompare('a.is_invitation', BaseJobApply::IS_INVITATION_YES);
            } else {
                $query->andFilterCompare('a.status', $keywords['status']);
            }
        }

        $count = $query->groupBy('a.id')
            ->count();

        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        //置顶的排序拼接
        $orderBy = 'a.equity_status desc,' . $orderBy;
        $list    = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->select($select)
            ->groupBy('a.id')
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        // todo 返回数据处理
        $sourceList     = BaseJobApply::SOURCE_LIST;
        $statusList     = BaseJobApply::STATUS_LIST;
        $markStatusList = BaseJobApply::MARK_STATUS_LIST;

        // 日期时间格式数据数组
        $timeArray   = [
            'add_time',
            'refresh_time',
        ];
        $companyInfo = BaseCompany::findOne(['member_id' => $memberId]);
        foreach ($list as &$item) {
            $last_education_info      = BaseResumeEducation::findOne($item['last_education_id']);
            $item['tag']              = BaseJobApplyRecord::DELIVERY_TYPE_INSIDE;
            $item['status_title']     = $statusList[$item['status']];
            $item['source_title']     = $sourceList[$item['source']];
            $item['avatar']           = FileHelper::getFullUrl($item['avatar']) ?: \Yii::$app->params['defaultMemberAvatar'];
            $last_major_name          = BaseMajor::getMajorName($last_education_info->major_id);
            $last_education_name      = BaseDictionary::getEducationName($last_education_info->education_id);
            $item['major']            = $last_major_name;
            $item['education_school'] = $last_education_info->school;
            $item['education_list']   = BaseResumeEducation::getLastRecord($item['resume_id'], 3);
            $item['work_list']        = BaseResumeWork::getLastRecord($item['resume_id'], 1);
            //简历类型便签
            $item['resume_type_tag']  = BaseResume::getResumeLevel($item['resume_id']);
            $item['resume_title_tag'] = BaseResume::getUserSpecialInfo($item['resume_id']);
            //活跃标签
            $item['active_tag']      = BaseMember::getUserActiveTime($item['member_id']);
            $item['education_title'] = $last_education_name;
            //获取30天内是否被查看
            $item['is_resume_check'] = BaseCompanyViewResume::getCheckStatus($item['resume_id'], $companyInfo->id);

            //根据求职者身份，获取求职者工作经验或者毕业时间文案
            $identityExperienceText = BaseResume::getIdentityExperienceText($item['resume_id']);
            $resume_info_arr        = [
                ($item['age']) . '岁',
                $last_education_name,
                $last_major_name ?: $last_education_info->major_custom,
            ];
            if ($identityExperienceText) {
                $resume_info_arr[] = $identityExperienceText;
            }
            $item['user_info'] = implode('·', $resume_info_arr);
            $area              = '';
            if ($item['area_id']) {
                $areaIds = explode(',', $item['area_id']);
                foreach ($areaIds as $areaId) {
                    $area .= BaseArea::getAreaName($areaId) . ',';
                }
            }
            $item['area']                      = substr($area, 0, -1);
            $item['company_mark_status_title'] = $item['company_mark_status'] ? $markStatusList[$item['company_mark_status']] : '';
            $item['announcement_title']        = $item['announcement_title'] ?? '';
            if (strlen($item['company_tag']) > 0) {
                $item['company_tag'] = explode(',', $item['company_tag']);
            }
            foreach ($timeArray as $value) {
                if (strtotime($item[$value]) < 1) {
                    $item[$value] = "-";
                } else {
                    $item[$value] = date("Y-m-d", strtotime($item[$value]));
                }
            }

            if ($item['is_invitation'] == BaseJobApply::IS_INVITATION_YES) {
                $companyInterviewInfo = BaseCompanyInterview::find()
                    ->select([
                        'id',
                        'count(*) as interview_num',
                    ])
                    ->where(['job_apply_id' => $item['id']])
                    ->orderBy('add_time desc')
                    ->asArray()
                    ->all();

                $item['company_interview_id'] = $companyInterviewInfo[0]['id'];
                $item['interview_num']        = ValidateHelper::number2chinese($companyInterviewInfo[0]['interview_num']) . '面';
            }
            unset($item['area_id']);
            // 这里需要找出来在投递总表的id,用于给下载使用
            $item['jobApplyMainId'] = BaseJobApplyRecord::findOneVal(['apply_id' => $item['id']], 'id');
            //出一下高意向标签
            $job_apply_info = BaseJobApplyTopEquityRecord::getLastUseEquity($item['job_id'], $item['resume_id'], 20);
            if ($job_apply_info) {
                $item['high_intention_is']   = 1;
                $item['high_intention_text'] = '高意向';
            } else {
                $item['high_intention_is']   = 0;
                $item['high_intention_text'] = '';
            }
            //做一个PV统计
            BaseCompanyResumePvTotal::updateDailyTotalPv($item['resume_id']);
        }

        return [
            'list'                 => $list,
            'page'                 => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
            'statistics'           => $statistics,
            'companyAuthorityList' => $companyAuthorityList,
        ];
    }

    /**
     * 简历列表
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public static function getResumeListNew($keywords)
    {
        //获取登录单位的用户ID
        $memberId = Yii::$app->user->id;
        //获取分页参数
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $page     = $keywords['page'];
        //对象
        $query = new Query();
        $where = [
            'j.is_show'   => BaseJob::IS_SHOW_YES,
            'c.member_id' => $memberId,
        ];
        $query->leftJoin(['r' => BaseResume::tableName()], 'r.id=a.resume_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.job_id = j.id')
            ->leftJoin(['ann' => BaseAnnouncement::tableName()], 'ann.id = j.announcement_id')
            ->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.id = r.last_education_id')
            ->leftJoin(['wk' => BaseResumeWork::tableName()], 'wk.resume_id = r.id ')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
            ->where($where)
            ->andWhere([
                'in',
                'j.status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ]);
        if ($keywords['is_abroad']) {
            if ($keywords['is_abroad'] == 1) {
                $query->andFilterCompare('wk.is_abroad', $keywords['is_abroad']);
            } else {
                $query->andFilterCompare('wk.is_abroad', 1, '<>');
            }
        }
        $query->andFilterCompare('r.household_register_id', $keywords['household_register_id']);
        if ($keywords['education']) {
            $educationArray = explode(',', $keywords['education']);
            $query->andWhere(['e.education_id' => $educationArray]);
        }
        $query->andFilterCompare('r.title_id', $keywords['title_id']);
        $query->andFilterCompare('r.gender', $keywords['gender']);
        $query->andFilterCompare('a.job_id', $keywords['job_id']);

        if ($keywords['is_project_school']) {
            if ($keywords['is_project_school'] == 2) {
                $query->andFilterWhere(['r.is_project_school' => BaseResumeEducation::IS_PROJECT_SCHOOL_YES]);
            } else {
                $query->andFilterWhere([
                    '<>',
                    'r.is_project_school',
                    BaseResumeEducation::IS_PROJECT_SCHOOL_YES,
                ]);
            }
        }

        if ($keywords['job_name']) {
            if (is_numeric($keywords['job_name']) && strlen($keywords['job_name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['job_name']);
                $query->andFilterCompare('a.job_id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('a.job_name', $keywords['job_name'], 'like');
            }
        }

        if ($keywords['announcement_id']) {
            if (is_numeric($keywords['announcement_id'])) {
                if (strlen($keywords['announcement_id']) == 8) {
                    $nameChangeUid = UUIDHelper::decryption($keywords['announcement_id']);
                } else {
                    $nameChangeUid = $keywords['announcement_id'];
                }
                $query->andFilterCompare('j.announcement_id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('ann.title', $keywords['announcement_id'], 'like');
            }
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $query->andFilterCompare('j.job_category_id', $jobCategoryId, 'in');
        }

        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'a.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'a.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }

        if ($keywords['age']) {
            //最远范围
            $before = $keywords['age'] + 1;
            $min    = date('Y-m-d', strtotime("-" . $before . "year"));
            $query->andWhere([
                '>',
                'r.birthday',
                $min,
            ]);

            //最近范围
            $back = $keywords['age'];
            $max  = date('Y-m-d', strtotime("-" . $back . "year"));
            $query->andWhere([
                '<=',
                'r.birthday',
                $max,
            ]);
        }
        $workYearsList = BaseDictionary::WORK_YEARS_LIST;
        if ($keywords['work_years']) {
            $maxDate = $workYearsList[$keywords['work_years']]['max'];
            $minDate = $workYearsList[$keywords['work_years']]['min'];
            $query->andWhere([
                '>=',
                'r.work_experience',
                $minDate,
            ]);
            $query->andWhere([
                '<=',
                'r.work_experience',
                $maxDate,
            ]);
        }
        $query->orderBy('a.id desc')
            ->groupBy('a.id');
        //公共select
        $common_select = [
            'a.id',
            'a.update_time',
            'a.add_time',
            'a.resume_attachment_id',
            'j.name as job_name',
            'j.announcement_id',
            'j.city_id',
            'j.major_id',
            'r.gender',
            'r.work_experience',
            'r.last_education_id',
            'r.id as resume_id',
            'r.last_work_id',
            'r.birthday',
            'r.education',
            'r.is_project_school',
            'm.avatar',
            'm.mobile',
        ];
        //job_apply表含有的
        $apply_select_private = [
            'a.company_tag',
            'a.resume_name',
            'a.is_invitation',
            'a.company_mark_status',
            'a.is_check',
            'a.status',
        ];
        //off_site_job_apply表含有的
        $apply_off_select_private = [
            'r.name as resume_name',
        ];
        $apply_select             = array_merge($common_select, $apply_select_private);
        $apply_off_select         = array_merge($common_select, $apply_off_select_private);
        $applyQuery               = clone $query;//克隆公共DB对象资源
        $applyQuery->from(['a' => BaseJobApply::tableName()])
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = a.resume_member_id')
            ->select($apply_select);
        $applyOffQuery = clone $query;//克隆公共DB对象资源
        $applyOffQuery->from(['a' => BaseOffSiteJobApply::tableName()])
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = a.member_id')
            ->select($apply_off_select);
        unset($query);//释放公共DB对象资源

        $statistics = self::getStatusAmount($memberId);//统计站内投递数据
        if (!empty($keywords['status']) && $keywords['status'] < 6) {
            if ($keywords['status'] == BaseJobApply::STATUS_SEND_INVITATION) {
                $applyQuery->andFilterCompare('a.is_invitation', BaseJobApply::IS_INVITATION_YES);
            } else {
                $applyQuery->andFilterCompare('a.status', $keywords['status']);
            }
        }
        $applyOffCount                 = $applyOffQuery->count();
        $applyCount                    = $applyQuery->count();
        $statistics['allStatusAmount'] = $statistics['allStatusAmount'] + $applyOffCount;//总数量
        $statistics['offApplyAmount']  = $applyOffCount;//站外投递数量
        //以下两个变量只应用于status=6时处理分页所用
        $apply_id     = 0;//站内投递
        $apply_off_id = 0;//站外投递
        if (empty($keywords['status']) || $keywords['status'] > 6) {//站内+站外
            //分页
            if ($keywords['apply_id'] > 0) {
                $applyQuery->andWhere([
                    '<',
                    'a.id',
                    $keywords['apply_id'],
                ]);
            }
            if ($keywords['apply_off_id'] > 0) {
                $applyOffQuery->andWhere([
                    '<',
                    'a.id',
                    $keywords['apply_off_id'],
                ]);
            }
            $count     = $applyOffCount + $applyCount;
            $applyList = $applyQuery->limit($pageSize)
                ->all();
            //添加标识区分站内站外数据来源--tag标识不要变更很多地方沿用
            foreach ($applyList as &$apply_val) {
                $apply_val['tag'] = BaseJob::DELIVERY_TYPE_INSIDE;
            }
            $applyOffList = $applyOffQuery->limit($pageSize)
                ->all();
            foreach ($applyOffList as &$apply_off_val) {
                $apply_off_val['tag'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            }
            if ($keywords['type'] == 1) {
                $list = $applyOffList;
            } elseif ($keywords['type'] == 2) {
                $list = $applyList;
            } else {
                $list = array_merge($applyList, $applyOffList);
                //数据排序
                $add_time = array_column($list, 'add_time');
                array_multisort($add_time, SORT_DESC, $list);
            }
            //剔除掉分页多余数据以及当前页数据的左最后ID
            $i = 1;
            foreach ($list as $k => $v) {
                if ($i > $pageSize) {
                    unset($list[$k]);
                } else {
                    if ($v['tag'] == BaseJob::DELIVERY_TYPE_INSIDE) {
                        $apply_id = $v['id'];
                    } elseif ($v['tag'] == BaseJob::DELIVERY_TYPE_OUTSIDE) {
                        $apply_off_id = $v['id'];
                    }
                }
                $i++;
            }
        } elseif ($keywords['status'] == 6) {//站外
            $applyOffPages = self::setPage($applyOffCount, $page, $pageSize);
            $list          = $applyOffQuery->offset($applyOffPages['offset'])
                ->limit($applyOffPages['limit'])
                ->all();
            foreach ($list as &$item2) {
                $item2['tag'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            }
            $count = $applyOffCount;
        } else {//站内
            $count      = $applyCount;
            $applyPages = self::setPage($applyCount, $page, $pageSize);
            $list       = $applyQuery->offset($applyPages['offset'])
                ->limit($applyPages['limit'])
                ->all();
            foreach ($list as &$item1) {
                $item1['tag'] = BaseJob::DELIVERY_TYPE_INSIDE;
            }
        }
        foreach ($list as &$item) {
            $educationInfo            = BaseResumeEducation::findOne($item['last_education_id']);
            $education_title          = BaseDictionary::getEducationName($educationInfo['education_id']);
            $item['education_school'] = $educationInfo['school'];
            $item['job_uid']          = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $item['job_id']);
            $item['status_title']     = $item['tag'] == BaseJob::DELIVERY_TYPE_INSIDE ? BaseJobApply::STATUS_LIST[$item['status']] : '站外投递';
            $item['source_title']     = BaseJobApply::SOURCE_LIST[$item['source']];
            $item['avatar']           = FileHelper::getFullUrl($item['avatar']) ?: \Yii::$app->params['defaultMemberAvatar'];
            $jobBeginDate             = BaseResumeWork::getFirstInfoList($item['resume_id']);
            $workInfo                 = BaseResumeWork::getEndInfo($item['resume_id']);
            $item['workBeginDate']    = date('Y.m', strtotime($jobBeginDate));
            $item['workEndDate']      = date('Y.m', strtotime($workInfo['end_date']));
            $item['workCompany']      = $workInfo['company'];
            $item['workJobName']      = $workInfo['job_name'];
            $item['refresh_time']     = $item['update_time'] && $item['update_time'] != '0000-00-00 00:00:00' ? $item['update_time'] : '-';
            $work_years               = TimeHelper::workTime($jobBeginDate);
            $education_major_title    = BaseMajor::getMajorName($item['major_id']);
            $city                     = BaseArea::getAreaName($item['city_id']);
            $age                      = TimeHelper::countYears($item['birthday']);
            $item['education_title']  = $education_title ?: "";
            $item['age']              = $age ? $age . '岁' : "";
            $work_years               = $work_years ? $work_years . '年经验' . " | " : '';
            $education_title          = $education_title ? $education_title . " | " : "";
            $age                      = $age ? $age . '岁 | ' : "";
            $education_major_title    = $education_major_title ? $education_major_title . " | " : "";
            $city                     = $city ?: "";
            $item['content']          = $work_years . $education_title . $age . $education_major_title . $city;
            $area                     = '';
            if ($item['area_id']) {
                $areaIds = explode(',', $item['area_id']);
                foreach ($areaIds as $areaId) {
                    $area .= BaseArea::getAreaName($areaId) . ',';
                }
            }
            $item['area']                      = substr($area, 0, -1);
            $item['company_mark_status_title'] = isset(BaseJobApply::MARK_STATUS_LIST[$item['company_mark_status']]) ? BaseJobApply::MARK_STATUS_LIST[$item['company_mark_status']] : '';
            $announcementTitle                 = '';
            if ($item['announcement_id']) {
                $announcementTitle = BaseAnnouncement::findOneVal(['id' => $item['announcement_id']], 'title');
            }
            $item['announcement_title'] = $announcementTitle;
            if (strlen($item['company_tag']) > 0) {
                $item['company_tag'] = explode(',', $item['company_tag']);
            }

            $resumeEducation              = BaseResumeEducation::findOne(['resume_id' => $item['resume_id']]);
            $item['education_school']     = $resumeEducation['school'];
            $item['education_begin_date'] = $resumeEducation['begin_date'];
            $item['education_end_date']   = $resumeEducation['end_date'];
            $item['major']                = BaseMajor::getMajorName($resumeEducation['major_id']);

            if ($item['is_invitation'] == BaseJobApply::IS_INVITATION_YES) {
                $companyInterviewInfo = BaseCompanyInterview::find()
                    ->select([
                        'id',
                        'count(*) as interview_num',
                    ])
                    ->where(['job_apply_id' => $item['id']])
                    ->orderBy('add_time desc')
                    ->asArray()
                    ->all();

                $item['company_interview_id'] = $companyInterviewInfo[0]['id'];
                $item['interview_num']        = ValidateHelper::number2chinese($companyInterviewInfo[0]['interview_num']) . '面';
            }
        }

        return [
            'list'       => $list,
            'page'       => [
                'apply_id'     => intval($apply_id),
                'off_apply_id' => intval($apply_off_id),
                'count'        => intval($count),
                'limit'        => (int)$pageSize,
                'page'         => (int)$page,
            ],
            'statistics' => $statistics,
        ];
    }

    /**
     * 其他应聘
     * @param $keywords
     * @return array
     * @throws \Exception
     */
    public static function getResumeListRest($keywords): array
    {
        $memberId = Yii::$app->user->id;
        //$companyInfo = BaseCompany::findOne(['member_id' => $memberId]);
        $select = [
            'a.status',
            'a.source',
            'a.resume_id',
            'a.add_time',
            'a.update_time',
            'a.status',
            'r.name',
            'a.job_id',
            'a.resume_attachment_id',
            'a.company_tag',
            'a.id',
            'j.name as job_name',
            'j.announcement_id',
            'j.city_id',
            'j.major_id',
            'j.company_id',
            'r.gender',
            'r.age',
            'r.work_experience',
            'r.last_education_id',
            'r.last_work_id',
            'r.education',
            'r.last_update_time',
            'r.refresh_time',
            'r.member_id',
            'i.area_id',
            'm.avatar',
            'm.mobile',
            'ann.title as announcement_title',
        ];
        $query  = BaseOffSiteJobApply::find()
            ->alias('a')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id=a.resume_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'a.job_id = j.id')
            ->leftJoin(['ann' => BaseAnnouncement::tableName()], 'ann.id = j.announcement_id')
            ->leftJoin(['i' => BaseResumeIntention::tableName()], 'i.resume_id = r.id')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id = r.member_id')
            //->andWhere(['j.company_id' => $companyInfo->id])
            ->andWhere([
                '<>',
                'a.status',
                BaseJob::STATUS_DELETE,
            ])
            ->andWhere([
                'in',
                'j.status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ]);

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField'  => 'j.company_id',
            'memberId'         => $memberId,
            'query'            => $query,
            'returnType'       => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
            'companyAuthority' => 'resumeList',
        ]);
        if (!empty($keywords['major_id'])) {
            if ($keywords['education']) {
                $query->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.id = r.last_education_id');
            } else {
                $query->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.resume_id = r.id');
            }
            $majorList = explode(',', $keywords['major_id']);
            $query->andWhere([
                'e.major_id' => $majorList,
                'e.status'   => BaseResumeEducation::STATUS_ACTIVE,
            ]);
        }
        $companyAuthorityList = [];
        if ($authorityList) {
            $query                = $authorityList['query'];
            $companyAuthorityList = $authorityList['companyAuthorityList'];
        }

        $query->andFilterCompare('a.status', BaseJob::STATUS_DELETE, '<>');
        if ($keywords['education']) {
            $educationArray = explode(',', $keywords['education']);
            $query->andWhere(['r.top_education_code' => $educationArray]);
        }
        if ($keywords['title_id']) {
            $titleArray = explode(',', $keywords['title_id']);
            $titleList  = BaseDictionary::getAllChildTitleArray($titleArray);

            $titleCondition = ['or'];
            foreach ($titleList as $item) {
                $titleCondition[] = "find_in_set(" . $item . ",r.title_id)";
            }
            $query->andWhere($titleCondition);
        }
        $query->andFilterCompare('ann.title', $keywords['announcement_name'], 'like');
        $query->andFilterCompare('a.announcement_id', $keywords['announcement_id']);
        $query->andFilterCompare('j.name', $keywords['job_name'], 'like');
        $query->andFilterCompare('a.job_id', $keywords['job_id']);
        //海外经历
        if ($keywords['is_abroad'] > 0) {
            if ($keywords['is_abroad'] == 1) {
                $query->andWhere(['r.is_abroad' => BaseResume::IS_ABROAD_YES]);
            } elseif ($keywords['is_abroad'] == 2) {
                $query->andWhere(['r.is_abroad' => BaseResume::IS_ABROAD_NO]);
            }
        }
        //政治面貌
        if (!empty($keywords['political'])) {
            $political = explode(',', $keywords['political']);
            $query->andWhere(['r.political_status_id' => $political]);
        }
        //年龄
        if (mb_strlen($keywords['age_min']) > 0 && mb_strlen($keywords['age_max']) > 0 && $keywords['age_min'] > $keywords['age_max']) {
            throw new Exception('年龄最小值不允许大于最大值');
        }
        if (mb_strlen($keywords['age_min']) > 0) {
            $query->andWhere([
                '>=',
                'r.age',
                $keywords['age_min'],
            ]);
        }
        if (mb_strlen($keywords['age_max']) > 0) {
            $query->andWhere([
                '<=',
                'r.age',
                $keywords['age_max'],
            ]);
        }
        //工作年限
        if (($keywords['work_experience'] && mb_strlen($keywords['work_experience_min']) > 0) || ($keywords['work_experience'] && mb_strlen($keywords['work_experience_max']) > 0)) {
            throw new Exception('参数错误');
        }
        if (!empty($keywords['work_experience'])) {
            if ($keywords['work_experience'] == 1) {
                //应届生/在校生
                $query->andFilterWhere(['r.identity_type' => BaseResume::IDENTITY_TYPE_GRADUATE]);
            } else {
                $workYearInfo = BaseDictionary::WORK_YEARS_LIST[$keywords['work_experience']];
                $query->andFilterWhere([
                    'between',
                    'r.work_experience',
                    $workYearInfo['min'],
                    $workYearInfo['max'],
                ]);
            }
        }
        if (mb_strlen($keywords['work_experience_min']) > 0 && mb_strlen($keywords['work_experience_max']) > 0 && $keywords['work_experience_min'] > $keywords['work_experience_max']) {
            throw new Exception('自定义工作年限最小值不允许大于最大值');
        }
        //自定义工作年限
        if (mb_strlen($keywords['work_experience_min']) > 0) {
            $query->andFilterWhere([
                '>=',
                'r.work_experience',
                $keywords['work_experience_min'],
            ]);
        }
        if (mb_strlen($keywords['work_experience_max']) > 0) {
            $query->andFilterWhere([
                '<=',
                'r.work_experience',
                $keywords['work_experience_max'],
            ]);
        }

        //985/211
        if ($keywords['is_project_school']) {
            if ($keywords['is_project_school'] == 2) {
                $query->andFilterWhere([
                    'r.is_project_school' => 1,
                ]);
            } else {
                $query->andFilterWhere([
                    '<>',
                    'r.is_project_school',
                    1,
                ]);
            }
        }

        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'a.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'a.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }
        $count = $query->groupBy('a.id')
            ->count();

        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->select($select)
            ->groupBy('a.id')
            ->orderBy('a.add_time desc')
            ->asArray()
            ->all();

        // todo 返回数据处理
        $statusList = BaseJobApply::STATUS_LIST;
        foreach ($list as &$item) {

            # https://zentao.jugaocai.com/index.php?m=story&f=view&id=1180
            $item['announcementUrl'] = UrlHelper::createAnnouncementDetailPath($item['announcement_id']);
            $item['jobUrl'] = UrlHelper::createJobDetailPath($item['job_id']);

            $last_education_info    = BaseResumeEducation::findOne($item['last_education_id']);
            $item['tag']            = BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE;
            $last_major_name        = BaseMajor::getMajorName($last_education_info->major_id);
            $last_education_name    = BaseDictionary::getEducationName($last_education_info->education_id);
            $item['add_time']       = date('Y/m/d', strtotime($item['add_time']));
            $item['update_time']    = date('Y/m/d', strtotime($item['last_update_time']));
            $item['status_title']   = $statusList[$item['status']];
            $item['avatar']         = FileHelper::getFullUrl($item['avatar']) ?: \Yii::$app->params['defaultMemberAvatar'];
            $item['education_list'] = BaseResumeEducation::getLastRecord($item['resume_id'], 3);
            $item['work_list']      = BaseResumeWork::getLastRecord($item['resume_id'], 1);
            //简历类型便签
            $item['resume_type_tag']  = BaseResume::getResumeLevel($item['resume_id']);
            $item['resume_title_tag'] = BaseResume::getUserSpecialInfo($item['resume_id']);
            //活跃标签
            $item['active_tag'] = BaseMember::getUserActiveTime($item['member_id']);
            //获取30天内是否被查看
            $item['is_resume_check'] = BaseCompanyViewResume::getCheckStatus($item['resume_id'], $item['company_id']);
            //根据求职者身份，获取求职者工作经验或者毕业时间文案
            $identityExperienceText = BaseResume::getIdentityExperienceText($item['resume_id']);
            $resume_info_arr        = [
                ($item['age']) . '岁',
                $last_education_name,
                $last_major_name ?: $last_education_info->major_custom,
            ];
            if ($identityExperienceText) {
                $resume_info_arr[] = $identityExperienceText;
            }
            $item['user_info'] = implode('·', $resume_info_arr);
            $area_arr          = [];
            if ($item['area_id']) {
                $areaIds = explode(',', $item['area_id']);
                foreach ($areaIds as $areaId) {
                    array_push($area_arr, BaseArea::getAreaName($areaId));
                }
            }
            $item['area'] = implode(',', $area_arr);
            if (strlen($item['company_tag']) > 0) {
                $item['company_tag'] = explode(',', $item['company_tag']);
            }

            // 这里需要找出来在投递总表的id,用于给下载使用
            $item['jobApplyMainId'] = BaseJobApplyRecord::findOneVal(['apply_site_id' => $item['id']], 'id');
            //做一个PV统计
            BaseCompanyResumePvTotal::updateDailyTotalPv($item['resume_id']);
        }

        return [
            'list'                 => $list,
            'page'                 => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
            'companyAuthorityList' => $companyAuthorityList,
        ];
    }
}