<?php

namespace common\libs;

use common\base\models\BaseChatRoom;
use common\base\models\BaseCompanyPackageChangeDetailLog;
use common\base\models\BaseMember;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseSmsLog;
use common\helpers\DebugHelper;
use common\helpers\StringHelper;
use Overtrue\EasySms\EasySms;
use Overtrue\EasySms\PhoneNumber;
use Yii;
use yii\base\Exception;

class SmsQueue
{

    const TYPE_LOGIN                            = 1;              //登录
    const TYPE_REGISTER                         = 2;              //注册
    const TYPE_BIND_MOBILE                      = 3;              //绑定手机号
    const TYPE_CHANGE_PASSWORD                  = 4;              //修改密码
    const TYPE_VALIDATE_COMPANY_INFO            = 5;              //基本信息验证
    const TYPE_CHANGE_MOBILE                    = 6;              //修改手机号
    const TYPE_VERIFY_MOBILE                    = 7;              //验证手机号
    const COMPANY_INFO_AUDIT_PASS               = 8;              //单位资料审核通过
    const COMPANY_INFO_AUDIT_REJECT             = 9;              //单位资料审核拒绝
    const TYPE_RESUME_WX_BIND_MOBILE            = 10;              //微信绑定
    const TYPE_RESUME_ORDER_PAY_SUCCESS         = 11;              //求职者支付成功
    const TYPE_RESUME_EQUITY_SERVICE_ABOUT      = 12;              //求职者权益过期提醒
    const TYPE_RESUME_ORDER_PAY_UPGRADE_SUCCESS = 13;              //求职者升级支付成功
    const TYPE_RESUME_MEMBER_NAME_CHANGE        = 14;              //求职者会员名称变更
    const TYPE_COMPANY_JOB_INVITE               = 15;              //单位邀约弹框
    const TYPE_COMPANY_JOB_INVITE_CHAT          = 16;              //单位邀约直聊弹框
    const TYPE_COMPANY_JOB_INVITE_CHAT_TIPS     = 17;              //单位邀约直聊工作台信息提醒

    const TYPE_LIST = [
        self::TYPE_LOGIN                            => '登录',
        self::TYPE_REGISTER                         => '注册',
        self::TYPE_BIND_MOBILE                      => '绑定手机号',
        self::TYPE_CHANGE_PASSWORD                  => '修改密码',
        self::TYPE_VALIDATE_COMPANY_INFO            => '基本信息验证',
        self::TYPE_CHANGE_MOBILE                    => '修改手机号',
        self::TYPE_VERIFY_MOBILE                    => '验证手机号',
        self::COMPANY_INFO_AUDIT_PASS               => '单位资料审核通过',
        self::COMPANY_INFO_AUDIT_REJECT             => '单位资料审核拒绝',
        self::TYPE_RESUME_WX_BIND_MOBILE            => '微信绑定',
        self::TYPE_RESUME_ORDER_PAY_SUCCESS         => '求职者支付成功',
        self::TYPE_RESUME_EQUITY_SERVICE_ABOUT      => '求职者权益过期提醒',
        self::TYPE_RESUME_ORDER_PAY_UPGRADE_SUCCESS => '求职者升级支付成功',
        self::TYPE_RESUME_MEMBER_NAME_CHANGE        => '求职者会员名称变更',
        self::TYPE_COMPANY_JOB_INVITE               => '单位邀约弹框',
        self::TYPE_COMPANY_JOB_INVITE_CHAT          => '单位邀约直聊弹框',
        self::TYPE_COMPANY_JOB_INVITE_CHAT_TIPS     => '单位邀约直聊工作台信息提醒',
    ];

    /** @var int[] 需要延时进队列的类型 */
    const RELAY_SEND_TYPE = [
        self::TYPE_COMPANY_JOB_INVITE,
        self::TYPE_COMPANY_JOB_INVITE_CHAT,
        self::TYPE_COMPANY_JOB_INVITE_CHAT_TIPS,
    ];

    const CACHE_KEY_LIST = [
        self::TYPE_LOGIN                 => Cache::SMS_MEMBER_LOGIN_KEY,
        self::TYPE_REGISTER              => Cache::SMS_MEMBER_REGISTER_KEY,
        self::TYPE_BIND_MOBILE           => Cache::SMS_MEMBER_BIND_MOBILE_KEY,
        self::TYPE_CHANGE_PASSWORD       => Cache::SMS_MEMBER_CHANGE_PASSWORD_KEY,
        self::TYPE_VALIDATE_COMPANY_INFO => Cache::SMS_MEMBER_VALIDATE_COMPANY_INFO_KEY,
        self::TYPE_CHANGE_MOBILE         => Cache::SMS_MEMBER_CHANGE_MOBILE_KEY,
        self::TYPE_VERIFY_MOBILE         => Cache::SMS_MEMBER_VERIFY_MOBILE_KEY,
        self::TYPE_RESUME_WX_BIND_MOBILE => Cache::SMS_RESUME_WX_BIND_MOBILE,
    ];

    // 需要做时间限制的发送
    const LIMIT_TIME_TYPE = [
        self::TYPE_BIND_MOBILE     => 60,
        self::TYPE_CHANGE_PASSWORD => 60,
        self::TYPE_CHANGE_MOBILE   => 60,
    ];

    // 不需要做时间限制的发送
    const NO_LIMIT_TIME_TYPE = [
        self::TYPE_RESUME_ORDER_PAY_SUCCESS,
        self::TYPE_RESUME_EQUITY_SERVICE_ABOUT,
        self::TYPE_RESUME_ORDER_PAY_UPGRADE_SUCCESS,
        self::TYPE_RESUME_MEMBER_NAME_CHANGE,
        self::TYPE_COMPANY_JOB_INVITE,
        self::TYPE_COMPANY_JOB_INVITE_CHAT,
        self::TYPE_COMPANY_JOB_INVITE_CHAT_TIPS,
    ];

    /**
     * @var 这个type是用户类型
     */
    public  $userType;
    public  $mobile;
    public  $mobileCode;
    private $key;
    private $content;
    private $templateId;
    private $data;
    private $code;
    public  $smsType;
    // 扩张参数
    public $extParams;

    /** @var $smsDetail BaseSmsLog */
    public $smsDetail;

    public $gateway = ['qcloud'];

    public $blacklist = [
        13146969696,
        13201910840,
        13500815571,
        13753729101,
        13801175563,
        13911340661,
        13933876985,
        14776345457,
        15624259231,
        17302493193,
        17384245162,
        17515135916,
        18205195659,
        18306593959,
        18562156270,
        18652996268,
        19151345386,
    ];

    protected $easySms;

    public $sendReturnMessage = [];

    public function __construct($mobile, $userType, $smsType, $mobileCode = '', $extParams = '')
    {
        // 先看是否在黑名单里面
        if (in_array($mobile, $this->blacklist)) {
            Yii::error('黑名单手机号：' . $mobile);
            throw new Exception('该手机号码已被禁止发送短信');
        }

        $this->mobile     = $mobile;
        $this->mobileCode = $mobileCode;
        $this->smsType    = $smsType;
        $this->userType   = $userType;
        $this->extParams  = $extParams;
    }

    /**
     * @param $id
     * @param $data
     * @return BaseSmsLog|null
     */
    public function setSmsDetail($id, $data = null)
    {
        if ($this->smsDetail) {
            return $this->smsDetail;
        }

        if ($data) {
            $this->smsDetail = $data;
        }

        $this->smsDetail = BaseSmsLog::findOne(['id' => $id]);

        return $this->smsDetail;
    }

    public function add()
    {
        if (!in_array($this->smsType, self::NO_LIMIT_TIME_TYPE)) {
            // 首先检查一下这个手机号上一次发送是什么时候,

            // 去掉 +
            $mobileCode = str_replace('+', '', $this->mobileCode);
            $lastMobile = BaseSmsLog::find()
                              ->select('add_time')
                              ->where([
                                  'mobile'      => $this->mobile,
                                  'mobile_code' => $mobileCode,
                                  'status'      => 1,
                              ])
                              ->orderBy('id desc')
                              ->asArray()
                              ->one()['add_time'];
            if ($lastMobile) {
                $time = CUR_TIMESTAMP - strtotime($lastMobile);
                // 检查60s内不给多次发送
                if ($time <= 60) {
                    throw new Exception('同一个手机号在60s内只能发送一次短信');
                }
            }
        }

        $this->setKey();

        switch ($this->userType) {
            case self::TYPE_LOGIN:
            case self::TYPE_REGISTER:
            case self::TYPE_BIND_MOBILE:
            case self::TYPE_CHANGE_PASSWORD:
            case self::TYPE_VALIDATE_COMPANY_INFO:
            case self::TYPE_CHANGE_MOBILE:
            case self::TYPE_VERIFY_MOBILE:
            case self::COMPANY_INFO_AUDIT_PASS:
            case self::TYPE_RESUME_WX_BIND_MOBILE:
            case self::TYPE_RESUME_ORDER_PAY_SUCCESS:
            case self::TYPE_RESUME_EQUITY_SERVICE_ABOUT:
            case self::TYPE_RESUME_ORDER_PAY_UPGRADE_SUCCESS:
            case self::TYPE_RESUME_MEMBER_NAME_CHANGE:
            case self::TYPE_COMPANY_JOB_INVITE:
            case self::TYPE_COMPANY_JOB_INVITE_CHAT:
            case self::TYPE_COMPANY_JOB_INVITE_CHAT_TIPS:
                break;
            default:
                throw new Exception('不存在的类型');
        }

        // 前面都是一些检查和组合,下面这里就是实际的写入一个数据
        $model              = new BaseSmsLog();
        $model->content     = '';
        $model->mobile_code = StringHelper::getMobileCodeNumber($this->mobileCode) ?: '';
        $model->mobile      = $this->mobile;
        $model->type        = $this->smsType;
        $model->status      = BaseSmsLog::STATUS_WAIT;
        $model->ext_params  = $this->extParams ?: '';
        $model->save();

        return $model->id;
    }

    private function setKey()
    {
        switch ($this->userType) {
            case BaseMember::TYPE_PERSON:
                $userKey = 'PERSON';
                break;
            case BaseMember::TYPE_COMPANY:
                $userKey = 'COMPANY';
                break;
            default:
                $userKey = 'OTHER';
                break;
        }
        $this->key = self::CACHE_KEY_LIST[$this->smsType] . ':' . $userKey . ':' . $this->mobile;

        return $this->key;
    }

    /**
     * @throws Exception
     * @throws \Overtrue\EasySms\Exceptions\InvalidArgumentException
     */
    public function send()
    {
        $this->setKey();
        $this->setTemplate();

        try {
            if (!$this->easySms) {
                $config        = Yii::$app->params['easySmsConfig'];
                $this->easySms = new EasySms($config);
            }
            $number      = new PhoneNumber($this->mobile, $this->mobileCode);
            $environment = Yii::$app->params['environment'];
            $sendFlat    = true;
            switch ($environment) {
                case 'local':
                    // 本地环境就不需要发送了
                    $sendFlat = false;
                    break;
                case 'prod':
                    // 正式环境全部都发
                    $sendFlat = true;
                    break;
                default:
                    // 其他环境看配置,如果在配置里面就发,否则不发
                    if (!in_array($this->mobile, Yii::$app->params['smsTestMobile'])) {
                        $sendFlat = false;
                    }
                    break;
            }
            if ($sendFlat) {
                $params = ['template' => $this->templateId];
                if ($this->data) {
                    $params['data'] = $this->data;
                }
                $sendMessage = $this->easySms->send($number, $params, $this->gateway ?: []);

                $this->sendReturnMessage = $sendMessage;

                if ($this->code) {
                    $code = $this->code;
                }
            } else {
                $code       = '1234';
                $this->code = $code;
            }

            if ($this->code) {
                // 如果有验证码
                // 然后标记一下到缓存里面

                // 60s看看
                Cache::set($this->key, $code, 60 * 5);
            }

            return $this->content;
        } catch (\Overtrue\EasySms\Exceptions\NoGatewayAvailableException $exception) {
            $e = $exception->getLastException();
            WxWork::getInstance()
                ->robotMessageToSms('类型' . $this->smsType . ';手机号' . $this->mobile . ';错误:' . $e->getMessage());

            if (in_array($this->smsType, [
                self::TYPE_COMPANY_JOB_INVITE,
                self::TYPE_COMPANY_JOB_INVITE_CHAT,
                self::TYPE_COMPANY_JOB_INVITE_CHAT_TIPS,
            ])) {
                DebugHelper::sms([
                    'smsId' => $this->smsDetail->id,
                    'res'   => $exception->getResults(),
                ]);
            }
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public function validation($code): bool
    {
        $key       = $this->setKey();
        $cacheCode = Cache::get($key);

        if (!$cacheCode) {
            throw new Exception('短信验证码错误或已过期，请重新输入');
        }

        if ($cacheCode != $code) {
            throw new Exception('验证码错误');
        }

        // 这里应该销毁
        Cache::set($key, '');

        return true;
    }

    private function setTemplate()
    {
        $identity = '';
        if ($this->userType == BaseMember::TYPE_PERSON) {
            $identity = '求职者';
        } elseif ($this->userType == BaseMember::TYPE_COMPANY) {
            $identity = '企业招聘';
        }
        switch ($this->smsType) {
            case self::TYPE_LOGIN:
            case self::TYPE_REGISTER:
            case self::TYPE_RESUME_WX_BIND_MOBILE:
                // 这里有一个比较特殊的,就是需要根据用户有没有注册过来判断应该发的是注册验证码还是登录验证码
                $member = BaseMember::findOne([
                    'mobile' => $this->mobile,
                    'type'   => $this->userType,
                ]);
                if ($member) {
                    // 如果已经注册过了
                    $this->templateId = '1316950';
                    $this->code       = StringHelper::randNumber();
                    $this->data       = [
                        $this->code,
                        5,
                    ];;
                    $this->content = "【 $identity 】您的登录验证码：{$this->code}，5分钟内有效，切勿将验证码告知他人，如非本人操作请忽略。";
                } else {
                    $this->templateId = '1316940';
                    $this->code       = StringHelper::randNumber();
                    $this->data       = [
                        $this->code,
                        5,
                    ];
                    $this->content    = "【 $identity 】验证码为：{$this->code}，5分钟内有效，您正在注册高校人才网账号，切勿将验证码告知他人，如非本人操作请忽略。";
                }

                break;

            case self::COMPANY_INFO_AUDIT_PASS:
                // 暂时写死
                $this->templateId = '1317042';
                $this->content    = "【 $identity 】您的单位资质认证平台方已审核通过，欢迎您前往登录单位后台解锁更多功能。";
                break;
            case self::COMPANY_INFO_AUDIT_REJECT:
                // 暂时写死
                $this->templateId = '1317043';
                $this->content    = "【 $identity 】您的单位资质认证平台方审核不通过，请登录管理后台查看详情，按要求重新提交。";
                break;

            case self::TYPE_CHANGE_PASSWORD:
                // 暂时写死
                $this->templateId = '1316951';
                $this->code       = StringHelper::randNumber();
                $this->data       = [
                    $this->code,
                    5,
                ];
                $this->content    = "【 $identity 】验证码：{$this->code}，5分钟内有效，用于找回密码操作，如非本人操作，请注意账号安全。";
                break;
            case self::TYPE_BIND_MOBILE:
                // 暂时写死
                $this->templateId = '1316940';
                $this->code       = StringHelper::randNumber();
                $this->data       = [
                    $this->code,
                    5,
                ];
                $this->content    = '【' . $identity . '】' . $this->code . '为您的绑定手机号验证码，请于1分钟内填写，如非本人操作，请忽略本短信。';
                break;
            case self::TYPE_VALIDATE_COMPANY_INFO:
                // 暂时写死
                $this->templateId = '1316940';
                $this->code       = StringHelper::randNumber();
                $this->data       = [
                    $this->code,
                    5,
                ];
                $this->content    = '【' . $identity . '】' . $this->code . '为您验证单位基本信息验证码，请于1分钟内填写，如非本人操作，请忽略本短信。';
                break;
            case self::TYPE_CHANGE_MOBILE:
                // 暂时写死
                $this->templateId = '1316940';
                $this->code       = StringHelper::randNumber();
                $this->data       = [
                    $this->code,
                    5,
                ];
                $this->content    = '【' . $identity . '】' . $this->code . '为您的更改手机号验证码，请于1分钟内填写，如非本人操作，请忽略本短信。';
                break;
            case self::TYPE_VERIFY_MOBILE:
                // 暂时写死
                $this->templateId = '1316951';
                $this->code       = StringHelper::randNumber();
                $this->data       = [
                    $this->code,
                    5,
                ];
                $this->content    = '【' . $identity . '】验证码：' . $this->code . '5分钟内有效，用于找回密码操作，如非本人操作，请注意账号安全。';
                break;
            case self::TYPE_RESUME_ORDER_PAY_SUCCESS:
                $this->templateId = '1816886';
                // 通过扩展参数,获取产品名称
                $packageName   = json_decode($this->extParams, true)['package_name'] ?? '';
                $this->data    = [
                    $packageName,
                ];
                $this->content = '您已成功下单' . $packageName . '服务，请登录“高校人才网”使用！';
                break;
            case self::TYPE_RESUME_EQUITY_SERVICE_ABOUT:
                $this->templateId = '1983021';
                // 通过扩展参数,获取产品名称
                $extParams     = json_decode($this->extParams, true);
                $this->data    = [
                    $extParams['package_category_name'],
                    $extParams['day'],
                ];
                $this->content = '用户您好！您所购买的' . $extParams['package_category_name'] . $extParams['day'] . '天后将过期，请及时使用资源。';
                break;
            case self::TYPE_RESUME_ORDER_PAY_UPGRADE_SUCCESS:
                $this->templateId = '1983041';
                // 通过扩展参数,获取产品名称
                $extParams           = json_decode($this->extParams, true);
                $resume_package_info = BaseResumeEquityPackage::find()
                    ->where([
                        'resume_id'           => $extParams['resume_id'],
                        'package_category_id' => $extParams['equity_package_category_id'],
                        'expire_status'       => BaseResumeEquityPackage::STATUS_EXPIRE,
                    ])
                    ->orderBy('equity_id asc')
                    ->asArray()
                    ->one();
                $this->data          = [
                    $resume_package_info['expire_time'],
                ];
                $this->content       = '您已成功升级为钻石VIP，有效期至' . $resume_package_info['expire_time'] . '，请登录“高校人才网”使用！';
                break;
            case self::TYPE_RESUME_MEMBER_NAME_CHANGE:
                $this->templateId = '1983029';
                // 通过扩展参数,获取产品名称
                $this->data    = [];
                $this->content = '用户您好！因产品升级，您所购买的高才VIP套餐名称已变更为“黄金VIP”，您的原有权益不受影响，请放心使用。';
                break;
            case self::TYPE_COMPANY_JOB_INVITE:
                // 单位职位邀约
                $changeDetailLog                 = BaseCompanyPackageChangeDetailLog::getLogDetailBySmsId(BaseCompanyPackageChangeDetailLog::TYPE_COMPANY_JOB_INVITE,
                    $this->smsDetail->id);
                $changeDetailLog['company_name'] = $this->formatCompanyName($changeDetailLog['company_name']);

                $this->templateId = 'SMS_474955786';
                $this->content    = $changeDetailLog['company_name'] . '仔细查看了您提交的简历，对您很感兴趣，并向您发起投递邀请。前往[高才优聘]小程序可查看机会。拒收请回复R';
                $this->gateway    = ['aliyun'];
                $this->data       = [
                    'name' => $changeDetailLog['company_name'],
                ];
                break;
            case self::TYPE_COMPANY_JOB_INVITE_CHAT:
                // 单位直聊邀约
                $createRoomRes                   = json_decode($this->smsDetail->ext_params,
                    true)['is_create_room'] ?? [];
                $changeDetailLog                 = BaseCompanyPackageChangeDetailLog::getLogDetailBySmsId(BaseCompanyPackageChangeDetailLog::TYPE_COMPANY_JOB_INVITE_CHAT,
                    $this->smsDetail->id);
                $changeDetailLog['company_name'] = $this->formatCompanyName($changeDetailLog['company_name']);

                // （1）若B与C没有会话记录
                // （2）若B与C有会话记录，但C未向B发送过消息
                // （3）若B与C有会话记录，且C有向B发送过消息
                $isCreateRoom = $createRoomRes[$changeDetailLog['resume_id']] ?? 1;
                if ($isCreateRoom) {
                    $this->templateId = 'SMS_475235378';
                    $this->content    = $changeDetailLog['company_name'] . '向您发送了一条消息，点 gaoxiaojob.com/t/chat 至小程序速查。';
                } else {
                    // （1）
                    $roomDetail = BaseChatRoom::findOne([
                        'company_id' => $changeDetailLog['company_id'],
                        'resume_id'  => $changeDetailLog['resume_id'],
                    ]);

                    if ($roomDetail->talk_progress == BaseChatRoom::TALK_PROGRESS_ONE_WAY && $roomDetail->creator_type == BaseChatRoom::CREATOR_TYPE_COMPANY) {
                        //（2）
                        $this->templateId = 'SMS_475235378';
                        $this->content    = $changeDetailLog['company_name'] . '向您发送了一条消息，点 gaoxiaojob.com/t/chat 至小程序速查。';
                    } else {
                        //（3）
                        $this->templateId = 'SMS_475255362';
                        $this->content    = $changeDetailLog['company_name'] . '回复了您的消息，点 gaoxiaojob.com/t/chat 至高才优聘小程序速查。';
                    }
                }
                $this->gateway = ['aliyun'];
                $this->data    = [
                    'name' => $changeDetailLog['company_name'],
                ];
                break;
            case self::TYPE_COMPANY_JOB_INVITE_CHAT_TIPS:
                // 单位直聊框-直接发送短信
                $changeDetailLog                 = BaseCompanyPackageChangeDetailLog::getLogDetailBySmsId(BaseCompanyPackageChangeDetailLog::TYPE_COMPANY_JOB_INVITE_CHAT_TIPS,
                    $this->smsDetail->id);
                $changeDetailLog['company_name'] = $this->formatCompanyName($changeDetailLog['company_name']);

                $this->templateId = 'SMS_475235378';
                $this->content    = $changeDetailLog['company_name'] . '向您发送了一条消息，点 gaoxiaojob.com/t/chat 至小程序速查。';
                $this->gateway    = ['aliyun'];
                $this->data       = [
                    'name' => $changeDetailLog['company_name'],
                ];
                break;
            default:
                throw new Exception('不存在发送短信业务');
                break;
        }
    }

    /**
     * 修建单位名称
     * @param $input
     * @return mixed|string
     */
    private function formatCompanyName($input)
    {
        if (mb_strlen($input) > 20) {
            // 截取字符串到第18个字符
            $formattedString = mb_substr($input, 0, 18) . '..';

            return $formattedString;
        }

        // 如果长度不超过20，返回原字符串
        return $input;
    }

}