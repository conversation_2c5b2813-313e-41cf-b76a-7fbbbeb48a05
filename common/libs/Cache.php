<?php

namespace common\libs;

use Codeception\Module\Redis;
use Yii;

/**
 * 在这里维护缓存的相关配置,例如key
 */
class Cache
{
    /**
     * key的规则是,端:功能
     * 多端共用使用,ALL:功能
     * 某种用户使用端:用户:功能
     */

    public static $cacheDebug = false;

    const PC_MEMBER_LOGIN_INFO_KEY             = 'PC:MEMBER:LOGIN_INFO';
    const PC_RESUME_WX_QR_CODE_LOGIN_INFO_KEY  = 'PC:RESUME:WX_QR_CODE_LOGIN_INFO';
    const PC_COMPANY_WX_QR_CODE_LOGIN_INFO_KEY = 'PC:COMPANY:WX_QR_CODE_LOGIN_INFO';
    const PC_RESUME_WX_QR_CODE_BIND_INFO_KEY   = 'PC:RESUME:WX_QR_CODE_BIND_INFO';
    const PC_COMPANY_WX_QR_CODE_BIND_INFO_KEY  = 'PC:COMPANY:WX_QR_CODE_BIND_INFO';
    const PC_ALL_AREA_LIST_KEY                 = 'PC:ALL:AREA_LIST';
    const PC_ALL_AREA_TABLE_KEY                = 'PC:ALL:AREA_TABLE';
    const PC_ALL_RESUME_ID_KEY                 = 'PC:ALL:RESUME_ID';
    const PC_PERSON_REGISTER_PASSWORD_KEY      = 'PC:PERSON:REGISTER_PASSWORD';
    const PC_ADMIN_TABLE_STAGING_FIELD_KEY     = 'PC:ADMIN:TABLE_STAGING_FIELD';  // 用户在前端保存的表单列(选择了哪些用于显示)
    const PC_ALL_TRADE_LIST                    = 'PC:ALL:TRADE_LIST';
    const PC_CHAT_NOTICE_TYPE                  = 'PC:CHAT:NOTICE_TYPE';

    // 首页的精选职位,其实只是每天更新一下而已,这里就保存一下当天的
    const PC_HOME_POSITION_SELECT_JOB_KEY = 'PC:HOME:POSITION:SELECT_JOB';
    //政府事业单位栏目页的精选职位模块
    const PC_COLUMN_GOVERNMENT_SELECT_JOB_KEY = 'PC:COLUMN:GOVERNMENT:SELECT_JOB';
    //省区栏目页的精选职位模块
    const PC_COLUMN_AREA_SELECT_JOB_KEY = 'PC:COLUMN:AREA:SELECT_JOB';
    //其余一级栏目页面的精选职位模块
    const PC_FIRST_COLUMN_SELECT_JOB_KEY = 'PC:COLUMN:FIRST_LEVEL:SELECT_JOB';
    // job的内容也很多,这里也是放在缓存,3小时更新一次算了
    const PC_HOME_POSITION_SELECT_JOB_DETAIL = 'PC:HOME:POSITION:SELECT_JOB_DETAIL';
    //地区栏目页和其余一级栏目页通用的分页缓存key
    const PC_COLUMN_SELECT_JOB_SEARCH_KEY = 'PC:COLUMN:SELECT_JOB:SEARCH';

    const PC_HOME_JOB_LIST_PARAMS_KEY     = 'PC:HOME:JOB_LIST_PARAMS';
    const PC_HOME_COMPANY_LIST_PARAMS_KEY = 'PC:HOME:COMPANY_LIST_PARAMS';
    //小程序首页未登录无求职意向职位列表
    const MINI_HOME_RECOMMEND_JOB_KEY = 'MINI:HOME:RECOMMEND:JOB_LIST';
    // 地区榜单
    const MINI_REGION_LIST_KEY = 'MINI:REGION:LIST';
    //友情链接
    const PC_HOME_FRIEND_LINK_KEY = 'PC:HOME:FRIEND_LINK';

    // 小程序码
    const MINI_CODE_KEY = 'MINI:CODE';

    // 这一堆都是给手机短信用的

    const SMS_MEMBER_LOGIN_KEY                 = 'SMS:MEMBER_LOGIN';
    const SMS_MEMBER_REGISTER_KEY              = 'SMS:MEMBER_REGISTER';
    const SMS_MEMBER_BIND_MOBILE_KEY           = 'SMS:MEMBER_BIND_MOBILE';
    const SMS_MEMBER_CHANGE_PASSWORD_KEY       = 'SMS:MEMBER_CHANGE_PASSWORD';
    const SMS_MEMBER_VALIDATE_COMPANY_INFO_KEY = 'SMS:MEMBER_VALIDATE_COMPANY_INFO';
    const SMS_MEMBER_CHANGE_MOBILE_KEY         = 'SMS:MEMBER_CHANGE_MOBILE';
    const SMS_MEMBER_VERIFY_MOBILE_KEY         = 'SMS:MEMBER_VERIFY_MOBILE';
    const SMS_RESUME_WX_BIND_MOBILE            = 'SMS:RESUME_WX_BIND_MOBILE';

    //发送邮件的key
    const SMTP_MEMBER_REGISTER_KEY        = 'SMTP:MEMBER_REGISTER';
    const SMTP_MEMBER_LOGIN_KEY           = 'SMTP:MEMBER_LOGIN';
    const SMTP_MEMBER_CHANGE_PASSWORD_KEY = 'SMTP:MEMBER_CHANGE_PASSWORD';
    const SMTP_MEMBER_CHANGE_EMAIL_KEY    = 'SMTP:MEMBER_CHANGE_EMAIL';
    const SMTP_MEMBER_BIND_EMAIL_KEY      = 'SMTP:MEMBER_BIND_EMAIL';
    const SMTP_MEMBER_OFF_SITE_JOB_APPLY  = 'SMTP:MEMBER_OFF_SITE_JOB_APPLY';

    // PC端广告位缓存信息
    const PC_HOME_POSITION_SHOWCASE_KEY = 'PC:HOME_POSITION:SHOWCASE';

    // PC端管理员企业微信扫码登录
    const PC_ADMIN_WXWORK_LOGIN_KEY = 'PC:ADMIN:WXWORK:LOGIN';

    // 三日热门公告
    const ALL_ANNOUNCEMENT_HOT_THREE_DAY_KEY = 'ALL:ANNOUNCEMENT:HOT:THREE_DAY';
    // 七日热门公告
    const ALL_ANNOUNCEMENT_HOT_WEEK_KEY = 'ALL:ANNOUNCEMENT:HOT:WEEK';
    // 30日热门公告
    const ALL_ANNOUNCEMENT_HOT_MONTH_KEY = 'ALL:ANNOUNCEMENT:HOT:MONTH';
    // 15天热门公告
    const ALL_ANNOUNCEMENT_HOT_HALF_MONTH_KEY = 'ALL:ANNOUNCEMENT:HOT:HALF_MONTH';

    // 所有的栏目保存一下,不用每次去查询
    const ALL_HOME_COLUMN_LIST_KEY = 'ALL:HOME:COLUMN:LIST';

    const H5_HOME_ANNOUNCEMENT_LIST_KEY = 'H5:HOME:ANNOUNCEMENT:LIST';

    const PC_COLUMN_HTML_KEY = 'HTML:PC:COLUMN';
    const PC_HOME_HTML_KEY   = 'HTML:PC:HOME';
    const H5_COLUMN_HTML_KEY = 'HTML:H5:COLUMN';
    const H5_HOME_HTML_KEY   = 'HTML:H5:HOME';

    // 单位主页缓存一下
    const ALL_COMPANY_DETAIL_KEY              = 'ALL:COMPANY:DETAIL';
    const ALL_COMPANY_DETAIL_UN_RECOMMEND_KEY = 'ALL:COMPANY:DETAIL:UN_RECOMMEND';
    //公告详情缓存
    const PC_ANNOUNCEMENT_DETAIL_KEY   = 'PC:ANNOUNCEMENT:DETAIL';
    const H5_ANNOUNCEMENT_DETAIL_KEY   = 'H5:ANNOUNCEMENT:DETAIL';
    const MINI_ANNOUNCEMENT_DETAIL_KEY = 'MINI:ANNOUNCEMENT:DETAIL';
    const ALL_ANNOUNCEMENT_DETAIL_KEY  = 'ALL:ANNOUNCEMENT:DETAIL';
    //职位详情缓存
    const ALL_JOB_DETAIL_KEY  = 'ALL:JOB:DETAIL';
    const MINI_JOB_DETAIL_KEY = 'MINI:JOB:DETAIL';
    // 记录临时生成的批量下载的在线投递的值
    const ALL_COMPANY_JOB_APPLY_BATCH_DOWNLOAD_KEY = 'ALL:COMPANY:JOB_APPLY:BATCH_DOWNLOAD';

    // 在线的专业数量
    const PC_HOME_ONLINE_MAJOR_AMOUNT_KEY = 'PC:HOME:ONLINE_MAJOR:AMOUNT';

    // 首页话题缓存
    const PC_HOME_TOPIC_KEY = 'PC:HOME:TOPIC';

    const PC_HOME_TALK_KEY = 'PC:HOME:TALK';
    //单位人才库的访问数量限制
    const PC_COMPANY_RESUME_LIBRARY_LIMIT_KEY = 'PC:COMPANY:RESUME_LIBRARY:LIMIT';
    //单位人才库的总数
    const PC_COMPANY_RESUME_LIBRARY_COUNT_NUMBER = 'PC:COMPANY:RESUME_LIBRARY:COUNT_NUMBER';
    const PC_COMPANY_PERSON_COUNT_NUMBER         = 'PC:COMPANY:PERSON:COUNT_NUMBER';

    // 高才情报局
    const PC_HOME_CIA_KEY = 'PC:HOME:CIA';

    // 首页热门单位
    const PC_HOME_HOT_COMPANY_KEY = 'PC:HOME:HOT:COMPANY';

    const WX_WORK_ALL_USER_LIST_KEY = 'WX:WORK:ALL:USER:LIST';

    // 运营后台一些统计信息暂存
    const ADMIN_JOB_LIST_STATISTICS_KEY = 'ADMIN:JOB:LIST:STATISTICS';

    // 运营后台所有栏目的子栏目缓存信息,不用每次去查询
    const ADMIN_HOME_COLUMN_LIST_KEY = 'ADMIN:HOME:COLUMN:LIST';

    // 运营后台所有学科ai识别缓存信息,不用每次去查询
    const ADMIN_MAJOR_AI_TEXT_LIST_KEY = 'ADMIN:MAJOR:AI_TEXT:LIST';

    // 记录百度站长的推送记录
    const ALL_BAIDUZZ_PUSH_PC_RECORD_KEY = 'ALL:BAIDUZZ:PUSH:PC:RECORD';
    const ALL_BAIDUZZ_PUSH_H5_RECORD_KEY = 'ALL:BAIDUZZ:PUSH:H5:RECORD';

    // 记录indexNow的推送记录
    const ALL_INDEXNOW_PUSH_PC_RECORD_KEY = 'ALL:INDEXNOW:PUSH:PC:RECORD';
    const ALL_INDEXNOW_PUSH_H5_RECORD_KEY = 'ALL:INDEXNOW:PUSH:H5:RECORD';

    const ALL_SYSTEM_CONFIG_KEY = 'ALL:SYSTEM:CONFIG';

    //缓存dictionary表
    const ALL_TABLE_DICTIONARY_TYPE_KEY = 'ALL:TABLE:DICTIONARY:TYPE';
    //缓存area表
    const ALL_TABLE_AREA_KEY = 'ALL:TABLE:AREA';
    //缓存major表
    const ALL_TABLE_MAJOR_KEY = 'ALL:TABLE:MAJOR';
    //缓存category_job表
    const ALL_TABLE_CATEGORY_JOB_KEY = 'ALL:TABLE:CATEGORY_JOB';
    //缓存certificate表
    const ALL_TABLE_CERTIFICATE_KEY = 'ALL:TABLE:CERTIFICATE';
    //缓存skill表
    const ALL_TABLE_SKILL_KEY = 'ALL:TABLE:SKILL';
    //缓存engine_search_type表
    const ALL_TABLE_ENGINE_SEARCH_TYPE_KEY = 'ALL:TABLE:ENGINE_SEARCH_TYPE';
    //缓存engine_search_source表
    const ALL_TABLE_ENGINE_SEARCH_SOURCE_KEY = 'ALL:TABLE:ENGINE_SEARCH_SOURCE';
    //缓存engine_search_rule表
    const ALL_TABLE_ENGINE_SEARCH_RULE_KEY = 'ALL:TABLE:ENGINE_SEARCH_RULE';
    //缓存admin_statement_builder_title_config表
    const PC_ALL_REPORT_BUILDER_TITLE_CONFIG_TABLE_KEY = 'ALL:TABLE:STATEMENT_REPORT_BUILDER_TITLE_CONFIG_TABLE';

    const ALL_JOB_LIST_PARAMS_LIST_KEY = 'ALL:JOB:LIST:PARAMS:LIST';

    const ALL_NEW_JOB_LIST_PARAMS_LIST_KEY = 'ALL:NEW:JOB:LIST:PARAMS:LIST';

    const ALL_ANNOUNCEMENT_LIST_PARAMS_LIST_KEY = 'ALL:ANNOUNCEMENT:LIST:PARAMS:LIST';

    const ALL_COMPANY_LIST_PARAMS_LIST_KEY = 'ALL:COMPANY:LIST:PARAMS:LIST';

    const H5_COMPANY_LIST_PARAMS_LIST_KEY = 'H5:COMPANY:LIST:PARAMS:LIST';

    // 附件简历识别保存缓存
    const ALL_RESUME_ATTACHMENT_IDENTIFY_KEY = 'ALL:RESUME:ATTACHMENT:IDENTIFY';

    // 用于职位列表搜索次数的白名单
    const ALL_IP_WHITE_LIST_JOB_SEARCH_KEY = 'ALL:IP:WHITE_LIST_JOB_SEARCH';
    // 用于单位列表搜索次数的白名单
    const ALL_IP_WHITE_LIST_COMPANY_SEARCH_KEY = 'ALL:IP:WHITE_LIST_COMPANY_SEARCH';
    const ALL_CHAT_PUSH_WX_MESSAGE             = 'ALL:CHAT_PUSH_WX_MESSAGE';

    /**
     * 缓存一下查询过的多个ids下面的全部3级ids
     */
    const ALL_MAJOR_SEARCH_IDS_TO_LEVEL3_KEY = 'ALL:MAJOR:SEARCH:IDS:TO:LEVEL3';

    //栏目列表页面和公共详情页面的底部seo关键词
    const PC_FOOTER_INTERNAL_LINK_SEO_KEYWORD = 'PC:FOOTER:INTERNAL_LINK:SEO_KEYWORD';
    //单位页面职位列表获取
    const ALL_COMPANY_JOB_LIST = 'ALL:COMPANY:JOB:LIST';
    //单位页面公告列表获取
    const ALL_COMPANY_ANNOUNCEMENT_LIST = 'ALL:COMPANY:ANNOUNCEMENT:LIST';
    //单位页面职位的地区列表
    const ALL_COMPANY_JOB_AREA_LIST = 'ALL:COMPANY:JOB:AREA:LIST';
    //单位页面职位的专业列表
    const ALL_COMPANY_JOB_MAJOR_LIST = 'ALL:COMPANY:JOB:MAJOR:LIST';
    //单位页面职位的职位类型列表
    const ALL_COMPANY_JOB_CATEGORY_LIST = 'ALL:COMPANY:JOB:CATEGORY:LIST';

    const PC_WX_SCAN_REGISTER_TOKEN_KEY = 'PC:WX:SCAN:REGISTER:TOKEN';
    const ALL_DB1_DATA_KEY              = 'ALL:DB1:DATA';

    // 存放求职者的活动,记录一下求职者的action/controller(暂时用于测试)
    const ALL_RESUME_ACTION_CONTROLLER_KEY = 'ALL:RESUME:ACTION:CONTROLLER';
    // 邮件已读的记录
    const ALL_EMAIL_READED_KEY = 'ALL:EMAIL:READED';

    // pc首页职位nav缓存
    const PC_HOME_JOB_NAV_WIDGET_KEY = 'PC:HOME:JOB:NAV:WIDGET';

    //手机绑定验证
    const PC_MEMBER_MOBILE_BIND_KEY = 'PC:MEMBER_MOBILE:BIND_KEY';

    // pc专题页缓存
    const PC_HOME_ZT_KEY = 'PC:HOME:ZT';

    // 职位列表没有参数缓存
    const ALL_JOB_LIST_NO_PARAMS_LIST_KEY      = 'ALL:JOB:LIST:NO:PARAMS:LIST';
    const ALL_JOB_LIST_MINI_NO_PARAMS_LIST_KEY = 'ALL:JOB:LIST:MINI:NO:PARAMS:LIST';

    // 单位列表没有参数缓存
    const ALL_COMPANY_LIST_NO_PARAMS_LIST_KEY = 'ALL:COMPANY:LIST:NO:PARAMS:LIST';
    // 公告列表没有参数缓存
    const ALL_ANNOUNCEMENT_LIST_NO_PARAMS_LIST_KEY = 'ALL:ANNOUNCEMENT:LIST:NO:PARAMS:LIST';

    //搜索引擎的缓存
    const ENGINE_RECOMMEND_CATEGORY        = 'ENGINE:RECOMMEND:CATEGORY';//热门岗位
    const ENGINE_RECOMMEND_JOB             = 'ENGINE:RECOMMEND:JOB';//热门职位
    const ENGINE_RECOMMEND_SEARCH_HOT      = 'ENGINE:RECOMMEND:SEARCH_HOT';//热搜职位
    const ENGINE_RECOMMEND_HOT_WORD        = 'ENGINE:RECOMMEND:HOT_WORD';//热门关键词
    const ENGINE_RECOMMEND_WIKI            = 'ENGINE:RECOMMEND:WIKI';//职位百科
    const ENGINE_JOB_LIST                  = 'ENGINE:JOB_LIST';//职位列表
    const ENGINE_SEARCH_LIST               = 'ENGINE:SEARCH_LIST';//筛选数据列表
    const ENGINE_SEARCH_HOT_QUEUE          = 'ENGINE:SEARCH_HOT:NOT_AREA_QUEUE';//热搜职位队列-不含地区
    const ENGINE_SEARCH_HOT_JOB_QUEUE      = 'ENGINE:SEARCH_HOT:NOT_AREA_JOB_QUEUE';//热搜职位名称队列-不含地区
    const ENGINE_SEARCH_HOT_AREA_QUEUE     = 'ENGINE:SEARCH_HOT:AREA_QUEUE';//热搜职位名称队列-含地区
    const ENGINE_SEARCH_HOT_AREA_JOB_QUEUE = 'ENGINE:SEARCH_HOT:AREA_JOB_QUEUE';//热搜职位队列-含地区

    //关于meilisearch的缓存key
    const MEILISEARCH_JOB_UPDATE_ID = 'MEILISEARCH:JOB_UPDATE';//职位更新ID
    const MEILISEARCH_JOB_LOOP_ID   = 'MEILISEARCH:JOB_LOOP';//职位循环ID

    //个人主页
    const MATCH_PERSONAL_JOb_RECOMMEND_LIST = 'MATCH:PERSONAL_TO_JOB:RECOMMEND_LIST';//推荐列表
    const MATCH_PERSONAL_JOb_NEW_LIST       = 'MATCH:PERSONAL_TO_JOB:NEW_LIST';//最新列表

    //单位推荐人才
    const MATCH_JOB_PERSONAL_RECOMMEND_LIST = 'MATCH:JOB_TO_PERSONAL:RECOMMEND_LIST';//推荐列表
    const MATCH_JOB_PERSONAL_NEW_LIST       = 'MATCH:JOB_TO_PERSONAL:NEW_LIST';//最新列表

    const ENGINE_RULE_GROUP_LIST_KEY      = 'ENGINE:RULE:GROUP:LIST';//规则组列表
    const ENGINE_RULE_SOURCE_INFO_KEY     = 'ENGINE:RULE:SOURCE:INFO';//规则来源信息
    const ENGINE_RULE_SOURCE_LIST_KEY     = 'ENGINE:RULE:SOURCE:LIST';//规则二列表
    const ENGINE_SOURCE_CATEGORY_LIST_KEY = 'ENGINE:SOURCE:CATEGORY_LIST';//规则来源信息

    const COMPANY_JOB_ONLINE_SORT       = 'COMPANY:JOB:ONLINE_SORT';//单位端 在线职位的制定特殊排序缓存
    const COMPANY_RESUME_LIBRARY_SEARCH = 'COMPANY:RESUME_LIBRARY:SEARCH';//单位端搜索人才

    const ALL_RESUME_ORDER_KEY                  = 'ALL:RESUME:ORDER'; // 求职者支付订单
    const ALL_RESUME_ORDER_CREATE_LIMIT_KEY     = 'ALL:RESUME:ORDER:CREATE:LIMIT'; // 求职者预支付订单限制数量
    const RESUME_TODAY_GLOBAL_POPOVER_CLICK_KEY = 'ALL:RESUME:TODAY:GLOBAL:POPOVER:CLICK';//求职者今日是否点击全局弹窗

    const MINI_JOB_SEARCH_PARAMS_KEY        = 'MINI:JOB:SEARCH:PARAMS';//小程序职位搜索参数缓存
    const MINI_COMPANY_LIST_PARAMS_LIST_KEY = 'MINI:COMPANY:LIST:PARAMS:LIST';

    // 小程序临时下载附件简历token
    const MINI_TMP_RESUME_DOWNLOAD_TOKEN_KEY = 'MINI_TMP_RESUME_DOWNLOAD_TOKEN';
    // 小程序全部配置数据
    const MINI_ALL_CONFIG_KEY = 'MINI:ALL:CONFIG';

    //PV显示缓存
    const ALL_RESUME_PV_EXPOSURE = 'ALL:RESUME:PV:EXPOSURE';
    // 单位端每天对人才库发起新直聊的次数
    const COMPANY_CHAT_RESUME_COUNT_KEY = 'COMPANY:CHAT:RESUME:COUNT';

    // 单位端人才库的一些数据，缓存 1 小时
    const  COMPANY_TALENT_CARD_KEY = 'COMPANY:TALENT:CARD';

    // 春招活动
    const SPRING_RECRUITMENT_ACTIVITY_KEY = 'PC:ACTIVITY:RECRUITMENT:SPRING';
    // 综合场活动
    const COMPREHENSIVE_ACTIVITY_KEY = 'PC:ACTIVITY:RECRUITMENT:COMPREHENSIVE';
    //凌云英才
    const LINGYUN_YINGCE_KEY               = 'PC:ACTIVITY:RECRUITMENT:LINGYUN_YINGCE';
    const LINGYUN_GLOBAL_RECRUIT           = 'PC:ACTIVITY:RECRUITMENT:GLOBAL_RECRUIT';
    const LINGYUN_GLOBAL_RECRUIT_BY_SKILL  = 'PC:ACTIVITY:RECRUITMENT:GLOBAL_RECRUIT_BY_SKILL';
    const LINGYUN_GLOBAL_RECRUIT_BY_ABROAD = 'PC:ACTIVITY:RECRUITMENT:GLOBAL_RECRUIT_BY_ABROAD';
    //热门关键词搜索
    const SEO_HOT_SEARCH_KEYWORD_KEY_JOB_LIST  = 'SEO:HOT:SEARCH_KEYWORD:JOB_LIST';
    const SEO_HOT_SEARCH_KEYWORD_KEY_RECOMMEND = 'SEO:HOT:SEARCH_KEYWORD:HOT_RECOMMEND';
    const SEO_HOT_KEYWORD_KEY_JOB_WIKI         = 'SEO:HOT:SEARCH_KEYWORD:JOB_WIKI';

    //公告点击数量
    const ALL_ANNOUNCEMENT_HEAT_NUMBER = 'ALL:ANNOUNCEMENT:HEAT:NUMBER';
    //单位工作台
    const COMPANY_STATISTICS_KEY            = 'COMPANY:STATISTICS';//单位工作台统计数据
    const COMPANY_EQUITY_CONSULT_NOTICE_KEY = 'COMPANY:EQUITY_CONSULT_NOTICE';//单位权益咨询通知

    //求职者简历的缓存文件夹前缀LEY
    const RESUME_CACHE_KEY_PREFIX = 'RESUME:';//简历缓存前缀

    //报告页面热度趋势
    const ALL_ANNOUNCEMENT_REPORT_HEAT_TREND = 'ALL:ANNOUNCEMENT:REPORT:HEAT_TREND';
    const ALL_JOB_REPORT_HEAT_TREND          = 'ALL:JOB:REPORT:HEAT_TREND';

    //公告投递热度
    const ALL_ANNOUNCEMENT_APPLY_HEAT = 'ALL:ANNOUNCEMENT:APPLY:HEAT';

    const RESUME_EQUITY_CLOSE_KEY = 'RESUME:EQUITY:CLOSE';//求职者权益关闭缓存

    //pc个人中心底部vip广告，被用户关闭缓存记录
    const PC_PERSON_CENTER_VIP_SHOWCASE_CLOSE = 'PC:PERSON:CENTER:VIP:SHOWCASE:CLOSE';
    //pc个人中心底部vip广告，每天首次点击记录
    const PC_PERSON_CENTER_VIP_SHOWCASE_DAILY = 'PC:PERSON:CENTER:VIP:SHOWCASE:DAILY';
    //PC我的服务页面，每天首次vip过期提醒
    const PC_MY_SERVICES_EXPIRE_TIPS_DAILY = 'PC:MY:SERVICES:EXPIRE:TIPS:DAILY';

    // 个人中心简历完善弹窗（还h5和PC)
    const RESUME_PERFECT_POP_KEY = 'RESUME:PERFECT:POP';

    // 拉新活动
    const ALL_NEW_RESUME_ACTIVITY_KEY = 'ALL:NEW_RESUME:ACTIVITY';
    //小程序关注弹窗
    const MINI_BIND_QRCODE_POP_KEY = 'MINI:BIND:QRCODE:POP:KEY';

    // 个人中心

    //登录弹窗
    const PC_LOGIN_TIPS_POP_AMOUNT    = 'PC:LOGIN:TIPS:POP:AMOUNT';
    const PC_LOGIN_TIPS_POP_LAST_TIME = 'PC:LOGIN:TIPS:POP:LAST:TIME';

    //pc扫小程序码登录
    const PC_RESUME_MINI_QR_CODE_LOGIN_INFO_KEY = 'PC:RESUME:MINI_QR_CODE_LOGIN_INFO';

    // 执行中的匹配公告(用来避免执行after中的公告再次去执行,剩下资源)
    const ALL_ANNOUNCEMENT_AFTER_RUN_ID_KEY = 'ALL:ANNOUNCEMENT_AFTER_RUN_ID';
    const ALL_JOB_AFTER_RUN_ID_KEY          = 'ALL:JOB_AFTER_RUN_ID';

    // 海外求贤公告对应的匹配职位
    const ALL_ABROAD_QIUXIAN_ANNOUNCEMENT_MATCH_JOB_KEY = 'ALL:ABROAD:QIUXIAN:ANNOUNCEMENT:MATCH:JOB';
    //公告招聘人数
    const ALL_ANNOUNCEMENT_JOB_AMOUNT_KEY = 'ALL:ANNOUNCEMENT_JOB_AMOUNT';

    /** ============================海外-出海引才-start============================================ */
    //海外地区缓存
    const HAIWAI_CHUHAI_AREA_KEY     = 'HAIWAI:CHUHAI:SEARCH_AREA';
    const HAIWAI_CHUHAI_AREA_ALL_KEY = 'HAIWAI:CHUHAI:SEARCH_AREA_ALL_KEY';
    /** ============================海外-出海引才-end============================================== */

    /** ============================海外-归国活动-start============================================ */
    //海归地区缓存
    const HAIWAI_GUIGUO_AREA_KEY             = 'HAIWAI:GUIGUO:SEARCH_AREA';
    const HAIWAI_GUIGUO_COMPANY_CATEGORY_KEY = 'HAIWAI:GUIGUO:COMPANY_CATEGORY';
    /** ============================海外-归国活动-end============================================== */

    //海外页面缓存，主要用于存放几个页面的初始数据
    const HAIWAI_PAGE_KEY = 'HAIWAI:PAGE';

    //博士后单位页面缓存
    const BOSHIHOU_COMPANY_PAGE_KEY                            = 'BOSHIHOU:PAGE';
    const BOSHIHOU_ANNOUNCEMENT_JOB_SEARCH_HOT_AREA_KEY        = 'BOSHIHOU:ANNOUNCEMENT_JOB_SEARCH:HOT_AREA';
    const BOSHIHOU_ANNOUNCEMENT_JOB_SEARCH_AREA_KEY            = 'BOSHIHOU:ANNOUNCEMENT_JOB_SEARCH:AREA';
    const BOSHIHOU_ANNOUNCEMENT_JOB_SEARCH_MAJOR_SPECIALTY_KEY = 'BOSHIHOU:ANNOUNCEMENT_JOB_SEARCH:MAJOR_SPECIALTY';
    const BOSHIHOU_ANNOUNCEMENT_JOB_SEARCH_COMPANY_TYPE_KEY    = 'BOSHIHOU:ANNOUNCEMENT_JOB_SEARCH:COMPANY_TYPE';
    const BOSHIHOU_HOME_ANNOUNCEMENT_TOUTIAO                   = 'BOSHIHOU:HOME:ANNOUNCEMENT_TOUTIAO';
    const BOSHIHOU_HOME_SPECIALTY_RECOMMEND                    = 'BOSHIHOU:HOME:SPECIALTY_RECOMMEND';
    const BOSHIHOU_HOME_ANNOUNCEMENT_HOT                       = 'BOSHIHOU:HOME:ANNOUNCEMENT_HOT';
    const BOSHIHOU_HOME_ANNOUNCEMENT_NEW                       = 'BOSHIHOU:HOME:ANNOUNCEMENT_NEW';
    const BOSHIHOU_HOME_AREA_HOT                               = 'BOSHIHOU:HOME:AREA_HOT';
    const BOSHIHOU_HOME_SELECTED_JOB_NEW                       = 'BOSHIHOU:HOME:SELECTED_JOB:JOB_NEW';
    const BOSHIHOU_HOME_SELECTED_JOB_ONLINE                    = 'BOSHIHOU:HOME:SELECTED_JOB:JOB_ONLINE';
    const BOSHIHOU_HOME_BRAND_COMPANY                          = 'BOSHIHOU:HOME:BRAND_COMPANY';
    const BOSHIHOU_HOME_BOHOU_ACTIVITY                         = 'BOSHIHOU:HOME:BOHOU_ACTIVITY ';
    const BOSHIHOU_HOME_BOHOU_RECOMMEND_ACTIVITY               = 'BOSHIHOU:HOME:BOHOU_RECOMMEND_ACTIVITY ';
    const BOSHIHOU_HOME_GUESS_LIKE_KEY                         = 'BOSHIHOU:HOME:GUESS_LIKE';
    const BOSHIHOU_JOB_LIST_PAGE_ONE                           = 'BOSHIHOU:JOB:LIST_PAGE_ONE';
    const BOSHIHOU_JOB_RANKING                                 = 'BOSHIHOU:JOB:RANKING';
    const BOSHIHOU_ANNOUNCEMENT_RANKING                        = 'BOSHIHOU:ANNOUNCEMENT:RANKING';
    const BOSHIHOU_ANNOUNCEMENT_LIST_PAGE_ONE                  = 'BOSHIHOU:ANNOUNCEMENT:LIST_PAGE_ONE';
    const UPDATE_COMPANY_STAT                                  = 'UPDATE_COMPANY_STAT:%s';
    const JOB_SEARCH_KEYWORDS                                  = 'JOB_SEARCH_KEYWORDS'; //预热关键字
    const JOB_SEARCH_KEYWORDS_JOB_IDS                          = 'JOB_SEARCH_KEYWORDS_JOB_IDS'; //预热关键字职位id
    const SEARCH_PARAMS_KEY                                    = 'SEARCH:PARAMS';//搜索参数缓存
    const SPECIAL_ACTIVITY_RELATION_ACTIVITY_SORT              = 'ZHAOPINHUI:SPECIAL_ACTIVITY_RELATION_ACTIVIRY:%s';// 专场活动关联单位的排序
    const ACTIVITY_COMPANY_AREA_PARAMS                         = 'ZHAOPINHUI:SPECIAL_ACTIVITY_COMPANY_AREA_PARAMS:%s:%s'; // 活动对应查询条件
    const ACTIVITY_COMPANY_TYPE_PARAMS                         = 'ZHAOPINHUI:SPECIAL_ACTIVITY_COMPANY_TYPE_PARAMS:%s:%s'; // 活动对应查询条件
    const ACTIVITY_COMPANY_MAJOR_PARAMS                        = 'ZHAOPINHUI:SPECIAL_ACTIVITY_COMPANY_MAJOR_PARAMS:%s:%s'; // 活动对应查询条件
    const ZHAOPINHUI_HOME_AREA_KEY                             = 'ZHAOPINHUI:HOME:AREA';
    const ZHAOPINHUI_HOME_STATISTICAL_DATA                     = 'ZHAOPINHUI:HOME:STATISTICAL_DATA';
    const ZHAOPINHUI_HOME_ACTIVITY_NEWS                        = 'ZHAOPINHUI:HOME:ACTIVITY_NEWS';
    const ZHAOPINHUI_HOME_ACTIVITY_HOT                         = 'ZHAOPINHUI:HOME:ACTIVITY_HOT';
    const MINI_ACTIVITY_SHARE_LINK                             = 'ZHAOPINHUI:MINI_ACTIVITY_SHARE_LINK:%s';   //小程序活动分享二维码
    const MINI_SPECIAL_ACTIVITY_SHARE_LINK                     = 'ZHAOPINHUI:MINI_SPECIAL_ACTIVITY_SHARE_LINK:%s';   //小程序专场活动分享二维码
    const COMPANY_TIME_FACTOR                                  = 'COMPANY_TIME_FACTOR:%s'; //单位时间因子
    const ACTIVITY_COMPANY_LIST                                = 'ACTIVITY_COMPANY_LIST:%s'; // 关联活动单位列表
    const SEO_JOB_WIKI_DETAIL                                  = 'SEO:SEO_JOB_WIKI_DETAIL:%s'; // 职位百科详情

    // 特殊需求配置缓存相关常量
    const SPECIAL_NEED_CONFIG_KEY_PREFIX = 'special_need_config'; // 特殊需求配置缓存键前缀
    const SPECIAL_NEED_CONFIG_TTL        = 86400; // 特殊需求配置缓存时间：24小时
    const SPECIAL_NEED_CONFIG_EMPTY_TTL  = 300; // 特殊需求配置空结果缓存时间：5分钟
    const SPECIAL_NEED_CONFIG_TAG        = 'special_need_config_tag'; // 特殊需求配置缓存标签

    /**
     * @param        $key
     * @param        $value
     * @param string $time
     */
    public static function set($key, $value, $time = '')
    {
        $redis = Yii::$app->redis;
        $redis->set($key, $value);
        if ($time) {
            $redis->expire($key, $time);
        }
    }

    /**
     * @param $key
     * @return mixed
     */
    public static function get($key)
    {
        $redis       = Yii::$app->redis;
        $environment = Yii::$app->params['environment'];
        if ($environment == 'prod' || !self::$cacheDebug) {//正式服或者debug关掉都正常返回缓存
            return $redis->get($key);
        } else {
            return '';
        }
    }

    /**
     * @param $key
     * @return mixed
     */
    public static function delete($key)
    {
        $redis = Yii::$app->redis;

        return $redis->del($key);
    }

    public static function incr($key)
    {
        $redis = Yii::$app->redis;

        return $redis->incr($key);
    }

    /**
     * @param        $key
     * @param        $value
     * @param        $index
     * @param string $time
     */
    public static function zadd($key, $index, $value)
    {
        $redis = Yii::$app->redis;
        $redis->zadd($key, $index, $value);
    }

    /**
     * 设置带有效时间的缓存
     * @param $key
     * @param $expire
     * @param $value
     * @return mixed
     */
    public static function setex($key, $expire, $value)
    {
        // new Connection();
        return Yii::$app->redis->setex($key, $expire, $value);
    }

    /**
     * @param        $key
     * @param        $value
     * @param        $index
     * @param string $time
     */
    public static function zaddex($key, $index, $value, $time = '')
    {
        $redis = Yii::$app->redis;
        $redis->zadd($key, $index, $value);
        if ($time) {
            $redis->expire($key, $time);
        }
    }

    /**
     * zrange
     */
    public static function zrange($key, $start, $end)
    {
        $redis = Yii::$app->redis;

        return $redis->zrange($key, $start, $end);
    }

    /**
     * zscore
     */
    public static function zscore($key, $member)
    {
        $redis = Yii::$app->redis;

        return $redis->zscore($key, $member);
    }

    /**
     * zrem
     */
    public static function zrem($key, $member)
    {
        $redis = Yii::$app->redis;

        return $redis->zrem($key, $member);
    }

    /**
     * @param        $key
     * @param        $value
     * @param string $time
     */
    public static function setnx($key, $value = 1)
    {
        $redis = Yii::$app->redis;

        return $redis->setnx($key, $value);
    }

    /**
     * 获取redis缓存key
     * @param string     $key      缓存key
     * @param bool       $global   是否全局缓存 false:私有缓存 true:全局缓存
     * @param string|int $menderID 用户ID
     * @return mixed    返回缓存key
     */
    public static function getRdKey($key, $global = false, $menderID = 0)
    {
        //$key = 'gaoxiaojob_' . $key;
        if ($global) {
            $realKey = $key . '_' . $menderID;
        } else {
            $realKey = $key;
        }

        return $realKey;
    }

    /**
     * 哈希：添加元素
     * @param string     $key      缓存key
     * @param string|int $field    缓存字段
     * @param string     $value    缓存值
     * @param false      $global   是否全局缓存 false:私有缓存 true:全局缓存
     * @param int        $menderID 用户ID
     * @return bool|int 返回值：1:添加成功 0:添加失败
     */
    public static function hSet($key, $field, $value, $global = false, $menderID = 0)
    {
        $redis = Yii::$app->redis;
        $key   = self::getRdKey($key, $global, $menderID);

        return $redis->hset($key, $field, $value);
    }

    /**
     * 哈希：获取元素
     * @param string     $key      缓存key
     * @param string|int $field    缓存字段
     * @param false      $global   是否全局缓存 false:私有缓存 true:全局缓存
     * @param int        $menderID 用户ID
     * @return bool|string 返回值：缓存值
     */
    public static function hGet($key, $field, $global = false, $menderID = 0)
    {
        $redis = Yii::$app->redis;
        $key   = self::getRdKey($key, $global, $menderID);

        return $redis->hget($key, $field);
    }

    /**
     * 哈希：删除元素
     * @param string     $key      缓存key
     * @param string|int $field    缓存字段
     * @param false      $global   是否全局缓存 false:私有缓存 true:全局缓存
     * @param int        $menderID 用户ID
     * @return bool|int 返回值：1:删除成功 0:删除失败
     */
    public static function hDel($key, $field, $global = false, $menderID = 0)
    {
        $redis = Yii::$app->redis;
        $key   = self::getRdKey($key, $global, $menderID);

        return $redis->hdel($key, $field);
    }

    /**
     * 哈希：获取所有元素
     * @param string $key      缓存key
     * @param false  $global   是否全局缓存 false:私有缓存 true:全局缓存
     * @param int    $menderID 用户ID
     * @return bool|array 返回值：所有元素
     */
    public static function hGetAll($key, $global = false, $menderID = 0)
    {
        $redis = Yii::$app->redis;
        $key   = self::getRdKey($key, $global, $menderID);
        $data  = $redis->hgetall($key);
        if (!empty($data)) {
            foreach ($data as $k => &$v) {
                $v = json_decode($v, true);
            }
            sort($data);
        }

        return $data;
    }

    /**
     * 哈希：获取所有值
     * @param string $key      缓存key
     * @param false  $global   是否全局缓存 false:私有缓存 true:全局缓存
     * @param int    $menderID 用户ID
     * @return bool|array 返回值：所有元素
     */
    public static function hvals($key, $global = false, $menderID = 0)
    {
        $redis = Yii::$app->redis;
        $key   = self::getRdKey($key, $global, $menderID);
        $data  = $redis->hvals($key);
        if (!empty($data)) {
            foreach ($data as $k => &$v) {
                $data[$k] = json_decode($v, true);
            }
            sort($data);
        }

        return $data;
    }

    /**
     * 哈希：判断元素是否存在
     * @param string     $key      缓存key
     * @param string|int $field    缓存字段
     * @param false      $global   是否全局缓存 false:私有缓存 true:全局缓存
     * @param int        $menderID 用户ID
     * @return bool|int 返回值：1:存在 0:不存在
     */
    public static function hExists($key, $field, $global = false, $menderID = 0)
    {
        $redis = Yii::$app->redis;
        $key   = self::getRdKey($key, $global, $menderID);

        return $redis->hexists($key, $field);
    }

    /**
     * 哈希：设置哈希表新增行或修改行中的元素
     * @param string $key      缓存key
     * @param array  $data     缓存数据
     * @param false  $global   是否全局缓存 false:私有缓存 true:全局缓存
     * @param int    $menderID 用户ID
     * @return bool|int 返回值：1:成功 0:失败
     */
    public static function hMSet($key, $data, $global = false, $menderID = 0)
    {
        $redis = Yii::$app->redis;
        $key   = self::getRdKey($key, $global, $menderID);
        foreach ($data as $k => $v) {
            $redis->hmset($key, $k, json_encode($v));
        }

        return true;
    }

    /**
     * 哈希：获取哈希表的元素值
     * @param string $key      缓存key
     * @param array  $fields   缓存字段
     * @param false  $global   是否全局缓存 false:私有缓存 true:全局缓存
     * @param int    $menderID 用户ID
     * @return bool|array 返回值：元素的值
     *                         $redis->hmGet('h', array('field1', 'field2'));
     *                         returns array('field1' => 'value1','field2' => 'value2')
     */
    public static function hMGet($key, $fields, $global = false, $menderID = 0)
    {
        $redis = Yii::$app->redis;
        $key   = self::getRdKey($key, $global, $menderID);
        $data  = [];
        foreach ($fields as $v) {
            $cache_item = $redis->hmget($key, $v);
            $data[$v]   = json_decode($cache_item[0], true);
        }

        return $data;
    }

    public static function lPush($key, $value)
    {
        $redis = Yii::$app->redis;
        $redis->lpush($key, $value);

        return true;
    }

    public static function lPop($key)
    {
        $redis = Yii::$app->redis;
        $value = $redis->lpop($key);

        return $value;
    }

    public static function rPush($key, $value)
    {
        $redis = Yii::$app->redis;
        $redis->rpush($key, $value);

        return true;
    }

    public static function rPop($key)
    {
        $redis = Yii::$app->redis;
        $value = $redis->rpop($key);

        return $value;
    }

    /**
     * 返回列表指定区间内的元素，区间以偏移量 START 和 END 指定。
     * @param $key
     * @param $start
     * @param $end
     * @return mixed
     */
    public static function lRange($key, $start, $end)
    {
        $redis = Yii::$app->redis;
        $value = $redis->lrange($key, $start, $end);

        return $value;
    }

    /**
     * 返回列表key的长度
     * @param $key
     * @return mixed
     */
    public static function lLen($key)
    {
        $redis = Yii::$app->redis;
        $value = $redis->llen($key);

        return $value;
    }

    /**
     * 返回对应的队列下标值
     */
    public static function lIndex($key, $index)
    {
        $redis = Yii::$app->redis;
        $value = $redis->lindex($key, $index);

        return $value;
    }

    /**
     * 设置过期时间
     */
    public static function expire($key, $time)
    {
        if ($time <= 0) {
            return;
        }
        $redis = Yii::$app->redis;
        $redis->expire($key, $time);
    }

    /**
     * 集合分数增加
     * @param $key
     * @param $num
     * @param $value
     * @return void
     */
    public static function zIncrBy($key, $num, $value)
    {
        $redis = Yii::$app->redis;
        $redis->zincrby($key, $num, $value);
    }

    /**
     * 获取集合内容
     * 可以使用数组函数organizeZRevRangeData转一下
     * @param $key
     * @param $num
     * @param $value
     * @return array
     */
    public static function zRevRange($key, $start, $end)
    {
        $redis = Yii::$app->redis;

        return $redis->zrevrange($key, $start, $end, 'WITHSCORES');
    }

    /**
     * 获取集合长度
     * @param $key
     * @return mixed
     */
    public static function zCard($key)
    {
        $redis = Yii::$app->redis;

        return $redis->zcard($key);
    }

    /**
     * 获取有序集合分数范围的成员
     * @param $key
     * @param $min
     * @param $max
     * @return mixed
     */
    public static function zRangeByScore($key, $min, $max)
    {
        $redis = Yii::$app->redis;

        return $redis->zrangebyscore($key, $min, $max, 'WITHSCORES');
    }

    /**
     * 返回集合key中的所有成员 [array | '']
     * @param $key
     * @return mixed
     */
    public static function sMembers($key)
    {
        $redis = Yii::$app->redis;

        return $redis->sMembers($key);
    }
}