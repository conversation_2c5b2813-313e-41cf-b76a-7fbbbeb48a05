<?php
/**
 * create user：shannon
 * create time：2024/4/17 17:37
 */
namespace common\service\companyAuth;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseResume;
use common\base\models\BaseResumeLibraryCollect;
use common\helpers\TimeHelper;
use Faker\Provider\Base;
use Yii;
use yii\base\Exception;

/**
 * 按钮权限组权限
 */
class ButtonGroupAuthService extends BaseButtonService
{
    /** 按钮权限组类型 */
    const TYPE_JOB           = 1;//职位按钮组
    const TYPE_ANNOUNCEMENT  = 2;//公告简章按钮组
    const TYPE_APPLY         = 3;//求职者求职状态按钮组
    const TYPE_RESUME        = 4;//简历操作按钮组
    const TYPE_RESUME_CENTER = 5;//简历-工作台操作按钮组
    const TYPE_LIST          = [
        self::TYPE_JOB,
        self::TYPE_ANNOUNCEMENT,
        self::TYPE_APPLY,
        self::TYPE_RESUME,
        self::TYPE_RESUME_CENTER,
    ];

    /** 职位按钮权限配置 */
    const JOB_BUTTON_GROUP = [
        self::BUTTON_JOB_COPY,
        self::BUTTON_JOB_OFFLINE,
        self::BUTTON_JOB_EDIT,
        self::BUTTON_JOB_PUBLISHING,
        self::BUTTON_JOB_REFRESH,
        self::BUTTON_JOB_REFRESHED,
    ];

    /** 公告按钮权限配置 */
    const ANNOUNCEMENT_BUTTON_GROUP = [
        self::BUTTON_ANNOUNCEMENT_OFFLINE,
        self::BUTTON_ANNOUNCEMENT_EDIT,
        self::BUTTON_ANNOUNCEMENT_PUBLISHING,
    ];

    /** 求职状态按钮组配置-站内投递(平台应聘) */
    const APPLY_INSIDE_BUTTON_GROUP = [
        //在线沟通
        self::BUTTON_ONLINE_COMMUNICATION,
        self::BUTTON_PASS_SCREENING,
        self::BUTTON_INVITE_INTERVIEW,
        self::BUTTON_REINVITE_INTERVIEW,
        self::BUTTON_NOT_SUITABLE,
        self::BUTTON_EMPLOYED,
        self::BUTTON_NOT_CONNECTED,
        self::BUTTON_NO_INTENTION,
        self::BUTTON_NOT_INTERVIEW,
        self::BUTTON_WAIT_EMPLOY,
        self::BUTTON_CANCEL_EMPLOY,
        self::BUTTON_ONBOARD,
        self::BUTTON_NOT_ONBOARD,
        self::BUTTON_CANCEL_ONBOARD,
        self::BUTTON_CANCEL_NOT_ONBOARD,
        self::BUTTON_CANCEL_NOT_SUITABLE,
        self::BUTTON_SHARE,
    ];

    /** 简历操作按钮组配置 */
    const RESUME_BUTTON_GROUP = [
        //在线沟通
        self::BUTTON_ONLINE_COMMUNICATION,
        self::BUTTON_INVITE_DELIVERY,
        self::BUTTON_DOWNLOAD,
        self::BUTTON_COLLECT,
        self::BUTTON_CANCEL_COLLECT,
        self::BUTTON_SHARE,
    ];

    /** 简历操作按钮组配置--工作台 */
    const RESUME_CENTER_BUTTON_GROUP = [
        self::BUTTON_ONLINE_COMMUNICATION_SPECIAL,
        self::BUTTON_INVITE_DELIVERY_SPECIAL,
    ];

    /** 初始化一下登录的账号信息 */
    private $memberId;
    /** 按钮组类型  */
    private $type;
    /**  @var BaseCompanyMemberInfo 单位账号信息 */
    private $companyMemberInfo;

    /**
     * 设置获取按钮组类型
     * @param $type
     * @return $this
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * 执行程序
     * @param $id int 职位ID/公告ID/投递记录ID(BaseJobApplyRecord表ID)/简历ID
     * @throws Exception
     */
    public function run(int $id)
    {
        if (!in_array($this->type, self::TYPE_LIST)) {
            throw new Exception('请设置获取按钮组类型');
        }
        if ($id <= 0) {
            throw new Exception('参数错误');
        }
        //获取一下的登录的单位用户ID
        $this->memberId = Yii::$app->user->getId();
        //初始化一些单位信息与账号权限信息
        $this->companyMemberInfo = BaseCompanyMemberInfo::findOne(['member_id' => $this->memberId]);
        switch ($this->type) {
            case self::TYPE_JOB:
                $buttonGroupAuth = $this->getJobButtonGroup($id);
                break;
            case self::TYPE_ANNOUNCEMENT:
                $buttonGroupAuth = $this->getAnnouncementButtonGroup($id);
                break;
            case self::TYPE_APPLY:
                $buttonGroupAuth = $this->getApplyButtonGroup($id);
                //如果需要处理特殊按钮的type 再处理
                break;
            case self::TYPE_RESUME:
                $buttonGroupAuth = $this->getResumeButtonGroup($id);
                break;
            case self::TYPE_RESUME_CENTER:
                $buttonGroupAuth = $this->getResumeCenterButtonGroup($id);
                break;
            default:
                throw new Exception('参数错误');
        }

        return $buttonGroupAuth;
    }

    /**
     * 职位按钮组权限
     */
    private function getJobButtonGroup($id)
    {
        //这里标识是职位
        //那获取一下职位信息
        $jobInfo = BaseJob::findOne($id);
        if (!$jobInfo) {
            throw new Exception('职位不存在');
        }
        if ($this->companyMemberInfo->company_id != $jobInfo->company_id) {
            throw new Exception('没有权限获该职位按钮组权限');
        }
        $jobRefreshIntervalDay = BaseCompanyPackageConfig::findOneVal(['company_id' => $jobInfo->company_id],
            'job_refresh_interval_day');
        if (empty($jobRefreshIntervalDay)) {
            $jobRefreshIntervalDay = 0;
        }
        $isRefresh       = TimeHelper::reduceDates(date('Y-m-d H:i:s'),
                $jobInfo->real_refresh_time) >= $jobRefreshIntervalDay;
        $getButtonGroup  = self::JOB_BUTTON_GROUP;
        $buttonGroupAuth = [];
        foreach ($getButtonGroup as $buttonId) {
            $item              = self::BUTTON_GROUP[$buttonId];
            $item              = self::getJobButtonAuth($item, $jobInfo->status, $jobInfo->audit_status, $isRefresh);
            $buttonGroupAuth[] = $item;
        }

        return $buttonGroupAuth;
    }

    /**
     * 按钮的权限处理
     */
    private function getJobButtonAuth($button, $status, $auditStatus, $isRefresh = true)
    {
        switch (true) {
            case $status == BaseJob::STATUS_ONLINE && $auditStatus == BaseJob::AUDIT_STATUS_PASS_AUDIT:
                //职位状态-在线-审核通过
                if ($button['id'] == self::BUTTON_JOB_REFRESH) {
                    $isShow     = $isRefresh;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_REFRESHED) {
                    $isShow     = !$isRefresh;
                    $isDisabled = true;
                }
                if ($button['id'] == self::BUTTON_JOB_EDIT) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_OFFLINE) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_COPY) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case $status == BaseJob::STATUS_ONLINE && $auditStatus == BaseJob::AUDIT_STATUS_WAIT_AUDIT:
                //职位状态-在线-审核通过
                if ($button['id'] == self::BUTTON_JOB_REFRESH) {
                    $isShow     = $isRefresh;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_REFRESHED) {
                    $isShow     = !$isRefresh;
                    $isDisabled = true;
                }
                if ($button['id'] == self::BUTTON_JOB_EDIT) {
                    $isShow     = true;
                    $isDisabled = true;
                }
                if ($button['id'] == self::BUTTON_JOB_OFFLINE) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_COPY) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case $status == BaseJob::STATUS_ONLINE && $auditStatus == BaseJob::AUDIT_STATUS_REFUSE_AUDIT:
                //职位状态-在线-审核拒绝
                if ($button['id'] == self::BUTTON_JOB_REFRESH) {
                    $isShow     = $isRefresh;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_REFRESHED) {
                    $isShow     = !$isRefresh;
                    $isDisabled = true;
                }
                if ($button['id'] == self::BUTTON_JOB_EDIT) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_OFFLINE) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_COPY) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case $status == BaseJob::STATUS_WAIT && $auditStatus == BaseJob::AUDIT_STATUS_WAIT:
                //职位状态-等待发布、编辑、保存-编辑中
                if ($button['id'] == self::BUTTON_JOB_REFRESH) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_REFRESHED) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_EDIT) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_OFFLINE) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_COPY) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case ($status == BaseJob::STATUS_WAIT || $status == BaseJob::AUDIT_STATUS_WAIT_AUDIT) && $auditStatus == BaseJob::AUDIT_STATUS_WAIT_AUDIT:
                //职位状态-等待发布、编辑、保存-等待审核
                if ($button['id'] == self::BUTTON_JOB_REFRESH) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_REFRESHED) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_EDIT) {
                    $isShow     = true;
                    $isDisabled = true;
                }
                if ($button['id'] == self::BUTTON_JOB_OFFLINE) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_COPY) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case ($status == BaseJob::STATUS_WAIT || $status == BaseJob::AUDIT_STATUS_WAIT_AUDIT || $status == BaseJob::STATUS_REFUSE_AUDIT) && $auditStatus == BaseJob::AUDIT_STATUS_REFUSE_AUDIT:
                //职位状态-等待发布、编辑、保存-审核拒绝
                if ($button['id'] == self::BUTTON_JOB_REFRESH) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_REFRESHED) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_EDIT) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_OFFLINE) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_COPY) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case $status == BaseJob::STATUS_OFFLINE:
                //职位状态-下线-已下线
                if ($button['id'] == self::BUTTON_JOB_REFRESH) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_REFRESHED) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_EDIT) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_OFFLINE) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_COPY) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_JOB_PUBLISHING) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                break;
            default:
                $isShow     = false;
                $isDisabled = false;
                break;
        }

        $button['isShow']     = $isShow;
        $button['isDisabled'] = $isDisabled;

        return $button;
    }

    /**
     * 公告按钮组权限
     */
    private function getAnnouncementButtonGroup($id)
    {
        //这里标识是公告
        //那获取一下公告信息
        $announcementInfo = BaseAnnouncement::findOne($id);
        if (!$announcementInfo) {
            throw new Exception('公告不存在');
        }
        if ($this->companyMemberInfo->company_id != $announcementInfo->company_id) {
            throw new Exception('没有权限获该公告按钮组权限');
        }
        //获取一下文章信息
        $articleInfo     = BaseArticle::findOne($announcementInfo->article_id);
        $getButtonGroup  = self::ANNOUNCEMENT_BUTTON_GROUP;
        $buttonGroupAuth = [];
        foreach ($getButtonGroup as $buttonId) {
            $item              = self::BUTTON_GROUP[$buttonId];
            $item              = self::getAnnouncementButtonAuth($item, $articleInfo->status,
                $announcementInfo->audit_status);
            $buttonGroupAuth[] = $item;
        }

        return $buttonGroupAuth;
    }

    /**
     * 公告按钮的权限处理
     */
    private function getAnnouncementButtonAuth($button, $status, $auditStatus)
    {
        switch (true) {
            case $status == BaseArticle::STATUS_ONLINE && $auditStatus == BaseAnnouncement::STATUS_AUDIT_PASS:
                //公告状态-在线-审核通过
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_EDIT) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_OFFLINE) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case $status == BaseArticle::STATUS_ONLINE && $auditStatus == BaseAnnouncement::STATUS_AUDIT_AWAIT:
                //公告状态-在线-待审核
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_EDIT) {
                    $isShow     = true;
                    $isDisabled = true;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_OFFLINE) {
                    $isShow     = true;
                    $isDisabled = true;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case $status == BaseArticle::STATUS_ONLINE && $auditStatus == BaseAnnouncement::STATUS_AUDIT_REFUSE:
                //公告状态-在线-审核拒绝
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_EDIT) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_OFFLINE) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case $status == BaseArticle::STATUS_STAGING && $auditStatus == BaseAnnouncement::STATUS_AUDIT_STAGING:
                //公告状态-等待发布、编辑、保存-编辑中
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_EDIT) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_OFFLINE) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case $status == BaseArticle::STATUS_STAGING && $auditStatus == BaseAnnouncement::STATUS_AUDIT_AWAIT:
                //公告状态-等待发布、编辑、保存-等待审核
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_EDIT) {
                    $isShow     = true;
                    $isDisabled = true;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_OFFLINE) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case $status == BaseArticle::STATUS_STAGING && $auditStatus == BaseAnnouncement::STATUS_AUDIT_REFUSE:
                //公告状态-等待发布、编辑、保存-审核拒绝
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_EDIT) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_OFFLINE) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_PUBLISHING) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                break;
            case $status == BaseArticle::STATUS_OFFLINE:
                //公告状态-下线-已下线
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_EDIT) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_OFFLINE) {
                    $isShow     = false;
                    $isDisabled = false;
                }
                if ($button['id'] == self::BUTTON_ANNOUNCEMENT_PUBLISHING) {
                    $isShow     = true;
                    $isDisabled = false;
                }
                break;
            default:
                $isShow     = false;
                $isDisabled = false;
                break;
        }

        $button['isShow']     = $isShow;
        $button['isDisabled'] = $isDisabled;

        return $button;
    }

    /**
     * 求职状态按钮组权限
     */
    private function getApplyButtonGroup($id)
    {
        //首先获取一下投递记录
        $applyInfo = BaseJobApplyRecord::findOne($id);
        //区别一下是站内投递还是站外投递
        if ($applyInfo->delivery_type == BaseJobApplyRecord::DELIVERY_TYPE_INSIDE) {
            //站内投递
            $buttonGroupAuth = $this->getApplyButtonGroupInside($applyInfo->apply_id);
        } else {
            //站外投递
            $buttonGroupAuth = $this->getApplyButtonGroupOutside($applyInfo->apply_site_id);
        }

        return $buttonGroupAuth;
    }

    /**
     * 站内投递按钮组权限
     */
    private function getApplyButtonGroupInside($id)
    {
        //获取站内投递记录
        $applyInfo = BaseJobApply::findOne($id);
        if (!$applyInfo) {
            throw new Exception('投递记录不存在');
        }
        $getButtonGroup  = self::APPLY_INSIDE_BUTTON_GROUP;
        $buttonGroupAuth = [];
        foreach ($getButtonGroup as $buttonId) {
            $item              = self::BUTTON_GROUP[$buttonId];
            $item              = $this->getApplyButtonAuthInside($item, $applyInfo->status,
                $applyInfo->company_mark_status);
            $buttonGroupAuth[] = $item;
        }

        return $buttonGroupAuth;
    }

    /**
     * 求职状态按钮的权限处理-站内
     */
    private function getApplyButtonAuthInside($button, $status, $companyMarkStatus)
    {
        //        const STATUS_HANDLE_WAIT     = 1;//已投递
        //        const STATUS_THROUGH_FIRST   = 2;//通过初筛
        //        const STATUS_SEND_INVITATION = 3;//邀请面试
        //        const STATUS_INAPPROPRIATE   = 4;//不合适
        //        const STATUS_EMPLOYED        = 5;//已录用

        //        const  COMPANY_MARK_STATUS_ORIGINAL        = 0;//原始状态
        //        const  COMPANY_MARK_STATUS_NOT_CONNECT     = 1;//未接通
        //        const  COMPANY_MARK_STATUS_NOT_INTENTION   = 2;//无意向
        //        const  COMPANY_MARK_STATUS_NOT_FACE        = 3;//未到面
        //        const  COMPANY_MARK_STATUS_WAIT_EMPLOYMENT = 4;//待录用
        //        const  COMPANY_MARK_STATUS_ENTRY           = 5;//已入职
        //        const  COMPANY_MARK_STATUS_NOT_ENTRY       = 6;//未入职
        $isShow = false;
        $type   = self::BUTTON_TYPE_DEFAULT;
        if ($button['id'] == self::BUTTON_ONLINE_COMMUNICATION) {
            $isShow = true;
        }
        if ($button['id'] == self::BUTTON_SHARE) {
            $isShow          = true;
            $button['event'] = BaseButtonService::BUTTON_COMPANY_RESUME_LIBRARY_SHARE_EVENT;
        }
        switch (true) {
            case $status == BaseJobApply::STATUS_HANDLE_WAIT://已投递
                if ($button['id'] == self::BUTTON_PASS_SCREENING) {
                    $isShow = true;
                    $type   = self::BUTTON_TYPE_PRIMARY;
                }
                if ($button['id'] == self::BUTTON_INVITE_INTERVIEW) {
                    $isShow = true;
                }
                if ($button['id'] == self::BUTTON_NOT_SUITABLE) {
                    $isShow = true;
                }
                if ($button['id'] == self::BUTTON_EMPLOYED) {
                    $isShow = true;
                }
                break;
            case $status == BaseJobApply::STATUS_THROUGH_FIRST://通过初筛

                if ($button['id'] == self::BUTTON_INVITE_INTERVIEW) {
                    $isShow = true;
                    $type   = self::BUTTON_TYPE_PRIMARY;
                }
                if ($button['id'] == self::BUTTON_NOT_SUITABLE) {
                    $isShow = true;
                }
                if ($button['id'] == self::BUTTON_EMPLOYED) {
                    $isShow = true;
                }
                switch ($companyMarkStatus) {
                    case BaseJobApply::COMPANY_MARK_STATUS_ORIGINAL:
                        if ($button['id'] == self::BUTTON_NOT_CONNECTED) {
                            $isShow = true;
                        }
                        if ($button['id'] == self::BUTTON_NO_INTENTION) {
                            $isShow = true;
                        }
                        break;
                    case BaseJobApply::COMPANY_MARK_STATUS_NOT_CONNECT:
                        if ($button['id'] == self::BUTTON_NO_INTENTION) {
                            $isShow = true;
                        }
                        break;
                }
                break;
            case $status == BaseJobApply::STATUS_SEND_INVITATION://邀请面试
                switch ($companyMarkStatus) {
                    case BaseJobApply::COMPANY_MARK_STATUS_ORIGINAL:
                        if ($button['id'] == self::BUTTON_REINVITE_INTERVIEW) {
                            $isShow = true;
                            $type   = self::BUTTON_TYPE_PRIMARY;
                        }
                        if ($button['id'] == self::BUTTON_NOT_SUITABLE) {
                            $isShow = true;
                        }
                        if ($button['id'] == self::BUTTON_EMPLOYED) {
                            $isShow = true;
                        }
                        if ($button['id'] == self::BUTTON_NOT_INTERVIEW) {
                            $isShow = true;
                        }
                        if ($button['id'] == self::BUTTON_WAIT_EMPLOY) {
                            $isShow = true;
                        }
                        break;
                    case BaseJobApply::COMPANY_MARK_STATUS_NOT_FACE:
                        if ($button['id'] == self::BUTTON_REINVITE_INTERVIEW) {
                            $isShow = true;
                            $type   = self::BUTTON_TYPE_PRIMARY;
                        }
                        if ($button['id'] == self::BUTTON_NOT_SUITABLE) {
                            $isShow = true;
                        }
                        if ($button['id'] == self::BUTTON_EMPLOYED) {
                            $isShow = true;
                        }
                        break;
                    case BaseJobApply::COMPANY_MARK_STATUS_WAIT_EMPLOYMENT:
                        if ($button['id'] == self::BUTTON_EMPLOYED) {
                            $isShow = true;
                            $type   = self::BUTTON_TYPE_PRIMARY;
                        }
                        if ($button['id'] == self::BUTTON_ONBOARD) {
                            $isShow = true;
                        }
                        if ($button['id'] == self::BUTTON_NOT_ONBOARD) {
                            $isShow = true;
                        }
                        break;
                    case BaseJobApply::COMPANY_MARK_STATUS_ENTRY:
                        if ($button['id'] == self::BUTTON_CANCEL_ONBOARD) {
                            $isShow = true;
                            $type   = self::BUTTON_TYPE_PRIMARY;
                        }
                        break;
                    case BaseJobApply::COMPANY_MARK_STATUS_NOT_ENTRY:
                        if ($button['id'] == self::BUTTON_CANCEL_NOT_ONBOARD) {
                            $isShow = true;
                            $type   = self::BUTTON_TYPE_PRIMARY;
                        }
                        break;
                }
                break;
            case $status == BaseJobApply::STATUS_INAPPROPRIATE && $companyMarkStatus == BaseJobApply::COMPANY_MARK_STATUS_ORIGINAL://不合适
                if ($button['id'] == self::BUTTON_CANCEL_NOT_SUITABLE) {
                    $isShow = true;
                    $type   = self::BUTTON_TYPE_PRIMARY;
                }
                break;
            case $status == BaseJobApply::STATUS_EMPLOYED://已录用
                switch ($companyMarkStatus) {
                    case BaseJobApply::COMPANY_MARK_STATUS_ORIGINAL:
                        if ($button['id'] == self::BUTTON_CANCEL_EMPLOY) {
                            $isShow = true;
                            $type   = self::BUTTON_TYPE_PRIMARY;
                        }
                        if ($button['id'] == self::BUTTON_ONBOARD) {
                            $isShow = true;
                        }
                        if ($button['id'] == self::BUTTON_NOT_ONBOARD) {
                            $isShow = true;
                        }
                        break;
                    case BaseJobApply::COMPANY_MARK_STATUS_ENTRY:
                        if ($button['id'] == self::BUTTON_CANCEL_ONBOARD) {
                            $isShow = true;
                            $type   = self::BUTTON_TYPE_PRIMARY;
                        }
                        break;
                    case BaseJobApply::COMPANY_MARK_STATUS_NOT_ENTRY:
                        if ($button['id'] == self::BUTTON_CANCEL_NOT_ONBOARD) {
                            $isShow = true;
                            $type   = self::BUTTON_TYPE_PRIMARY;
                        }
                        break;
                }
                break;
        }
        $button['isShow'] = $isShow;
        $button['type']   = $type;

        return $button;
    }

    /**
     * 站外投递按钮组权限
     */
    private function getApplyButtonGroupOutside($id)
    {
        return [];
    }

    /**
     * 简历操作按钮组权限
     */
    private function getResumeButtonGroup($id)
    {
        //这里标识是简历
        //那获取一下简历信息
        $resumeInfo = BaseResume::findOne($id);
        if (!$resumeInfo) {
            throw new Exception('简历不存在');
        }
        $getButtonGroup  = self::RESUME_BUTTON_GROUP;
        $buttonGroupAuth = [];
        $isCollect       = BaseResumeLibraryCollect::checkMemberCollectStatus($resumeInfo->id,
                $this->memberId) == BaseResumeLibraryCollect::IS_COLLECT_YES;
        //判断是否简历库
        $isCompanyResumeLibrary = BaseCompanyResumeLibrary::checkDownLoadStatus($id,
            $this->companyMemberInfo->company_id);
        foreach ($getButtonGroup as $buttonId) {
            $item = self::BUTTON_GROUP[$buttonId];
            if ($buttonId == BaseButtonService::BUTTON_SHARE) {
                $item['event'] = $isCompanyResumeLibrary == BaseCompanyResumeLibrary::HAS_RECORD_YES ? BaseButtonService::BUTTON_COMPANY_RESUME_LIBRARY_SHARE_EVENT : BaseButtonService::BUTTON_RESUME_LIBRARY_SHARE_EVENT;
            }
            if ($buttonId == BaseButtonService::BUTTON_DOWNLOAD) {
                $item['event'] = $isCompanyResumeLibrary == BaseCompanyResumeLibrary::HAS_RECORD_YES ? BaseButtonService::BUTTON_COMPANY_RESUME_LIBRARY_DOWNLOAD_EVENT : BaseButtonService::BUTTON_RESUME_LIBRARY_DOWNLOAD_EVENT;
            }
            $item              = $this->getResumeButtonAuth($item, $isCollect);
            $buttonGroupAuth[] = $item;
        }

        return $buttonGroupAuth;
    }

    /**
     * 简历操作按钮的权限处理
     */
    private function getResumeButtonAuth($button, $isCollect = false)
    {
        //只处理特殊的按钮
        //下载 触发事件不同

        //收藏 与 取消收藏
        if (($button['id'] == self::BUTTON_COLLECT && !$isCollect) || ($button['id'] == self::BUTTON_CANCEL_COLLECT && $isCollect)) {
            $button['isShow'] = true;
        }

        if ($button['id'] != self::BUTTON_COLLECT && $button['id'] != self::BUTTON_CANCEL_COLLECT) {
            $button['isShow'] = true;
        }

        return $button;
    }

    /**
     * 简历操作按钮组权限-工作台
     */
    private function getResumeCenterButtonGroup($id)
    {
        //这里标识是简历
        //那获取一下简历信息
        $resumeInfo = BaseResume::findOne($id);
        if (!$resumeInfo) {
            throw new Exception('简历不存在');
        }
        $getButtonGroup  = self::RESUME_CENTER_BUTTON_GROUP;
        $buttonGroupAuth = [];

        foreach ($getButtonGroup as $buttonId) {
            $item = self::BUTTON_GROUP[$buttonId];

            $item              = $this->getResumeCenterButtonAuth($item);
            $buttonGroupAuth[] = $item;
        }

        return $buttonGroupAuth;
    }

    /**
     * 简历操作按钮的权限处理-工作台
     */
    private function getResumeCenterButtonAuth($button)
    {
        $button['isShow'] = true;

        return $button;
    }
}