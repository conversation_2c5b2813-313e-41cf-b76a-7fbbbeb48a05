<?php

namespace common\service\companyResume;

// 查询服务
use common\base\BaseActiveRecord;
use common\base\models\BaseArea;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseCompanyViewResume;
use common\base\models\BaseDictionary;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyHandleLog;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeLibraryCollect;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseResumeWork;
use common\helpers\TimeHelper;
use common\libs\CompanyAuthority\CompanyAuthorityClassify;
use common\service\companyAuth\ButtonGroupAuthService;
use Yii;
use yii\base\Exception;

class SearchService extends BaseService
{
    private $searchParams;
    private $companyAuthorityList;

    private function getListQuery()
    {
        $params = $this->searchParams;
        $query  = BaseCompanyResumeLibrary::find()
            ->alias('cr')
            ->leftJoin(['r' => BaseResume::tableName()], 'r.id=cr.resume_id');
        //->where(['cr.company_id' => $params['companyId']]);

        $memberId          = Yii::$app->user->id;
        $companyMemberInfo = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);

        $query->where(['cr.company_id' => $params['companyId']]);
        if ($companyMemberInfo['company_member_type'] != BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN) {
            //职位协同
            $ids = BaseJobApply::find()
                ->alias('ja')
                ->leftJoin(['jcs' => BaseJobContactSynergy::tableName()], 'jcs.job_id = ja.job_id')
                ->leftJoin(['cmi' => BaseCompanyMemberInfo::tableName()], 'cmi.id = jcs.company_member_info_id')
                ->select([
                    'ja.resume_id',
                ])
                ->where([
                    'ja.company_id' => $companyMemberInfo['company_id'],
                    'cmi.member_id' => $memberId,
                ])
                ->groupBy([
                    'ja.resume_id',
                ])
                ->asArray()
                ->column() ?: [];

            $query->andWhere([
                'or',
                [
                    'cr.resume_id' => $ids,
                ],
                [
                    'cr.company_member_id' => $memberId,
                ],
            ]);
        }

        //关键词查询
        $resumeIds = [];
        if (!empty($params['keyword'])) {
            //关键词搜索支持以下字段模糊搜索：
            //【教育经历】-学校名称、 BaseResumeEducation -> school
            //【研究方向】、         BaseResumeResearchDirection -> content
            //【工作经历】-单位名称/职位名称/工作内容、 BaseResumeWork -> company  content  job_name
            //【科研项目】-项目名称/所属单位/项目描述、
            //【学术论文】-论文题目/论文描述、
            //【学术专利】-专利名称/专利描述、
            //【学术专著】-著作名称、
            //【资质证书】-证书名称、
            //【技能/语言】-技能/语言名称、
            //【个人优势】
            $resumeIds = array_merge($resumeIds, BaseResume::getIdsByKeywordCompanyLibrary($params['keyword']));

            // 这里还需要搜索姓名(但是这个有点特别,是从自己的简历库里面找的)
            $nameIds = BaseCompanyResumeLibrary::find()
                ->alias('cr')
                ->select('cr.resume_id')
                ->innerJoin(['r' => BaseResume::tableName()], 'r.id=cr.resume_id')
                ->where(['cr.company_id' => $params['companyId']])
                ->andWhere([
                    'like',
                    'r.name',
                    $params['keyword'],
                ])
                ->column();

            $resumeIds = array_merge($resumeIds, $nameIds);
        }

        //最近投递时间
        if (!empty($params['applyBeginTime']) && !empty($params['applyEndTime'])) {
            //查询该单位自己投递的记录
            $applyList = BaseJobApply::find()
                ->where(['company_id' => $params['companyId']])
                ->andWhere([
                    'between',
                    'add_time',
                    TimeHelper::dayToBeginTime($params['applyBeginTime']),
                    TimeHelper::dayToEndTime($params['applyEndTime']),
                ])
                ->select('resume_id')
                ->indexBy('resume_id')
                ->asArray()
                ->all();
            $resumeIds = array_merge($resumeIds, array_keys($applyList));
        }

        if (count($resumeIds) > 0) {
            //合并
            $resumeIds = array_flip($resumeIds);
            $resumeIds = array_keys($resumeIds);
        }

        //这里还得判断是否是传了参数，但是查不到
        if (!empty($params['keyword']) || (!empty($params['applyBeginTime']) && !empty($params['applyEndTime']))) {
            $query->andWhere(['cr.resume_id' => $resumeIds]);
        }
        //最高学历
        if (!empty($params['educationId'])) {
            $query->andWhere(['r.top_education_code' => $params['educationId']]);
        }

        if (!empty($params['majorId'])) {
            if ($params['educationId']) {
                $query->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.id=r.last_education_id');
            } else {
                $query->leftJoin(['e' => BaseResumeEducation::tableName()], 'e.resume_id=cr.resume_id');
            }
            //最高学历、学科专业、海外经历
            $query->andWhere(['e.status' => BaseResumeEducation::STATUS_ACTIVE]);
            $majorList = explode(',', $params['majorId']);
            $query->andWhere(['e.major_id' => $majorList]);
        }
        //海外经历查询
        if (!empty($params['isAbroad'])) {
            $query->andWhere(['r.is_abroad' => $params['isAbroad']]);
        }
        //985/211
        if ($params['isProjectSchool']) {
            if ($params['isProjectSchool'] == 2) {
                //字典表数据不对应，这里转换下
                $params['isProjectSchool'] = 1;
            }
            if ($params['isProjectSchool'] == 1) {
                $query->andFilterWhere([
                    'r.is_project_school' => 1,
                ]);
            } else {
                $query->andFilterWhere([
                    '<>',
                    'r.is_project_school',
                    1,
                ]);
            }
        }
        //现居住地查询
        if (!empty($params['residenceId'])) {
            $residenceIdArr = explode(',', $params['residenceId']);
            $query->andWhere(['r.residence' => $residenceIdArr]);
        }

        //求职意向查询
        if (!empty($params['cityId']) || !empty($params['jobCategoryId']) || !empty($params['wageId'])) {
            //意向城市、求职意向、专业学科
            $query->leftJoin(['i' => BaseResumeIntention::tableName()], 'i.resume_id=cr.resume_id');
            $query->andWhere(['i.status' => BaseResumeIntention::STATUS_ACTIVE]);
            if (!empty($params['cityId'])) {
                $cityArr   = explode(',', $params['cityId']);
                $cityQuery = BaseActiveRecord::formatFindInSetQuery($cityArr, 'i.area_id');
                $query->andWhere($cityQuery);
            }
            if (!empty($params['jobCategoryId'])) {
                $query->andWhere(['job_category_id' => $params['jobCategoryId']]);
            }
            if (!empty($params['wageId'])) {
                $wageInfo = BaseDictionary::getMinAndMaxWage($params['wageId']);

                //面议
                if ($wageInfo['min'] == 0 && $wageInfo['max'] == 0) {
                    $query->andWhere([
                        'i.min_wage' => 0,
                    ]);
                    $query->andWhere([
                        'i.max_wage' => 0,
                    ]);
                } else {
                    if ($wageInfo['min'] > 0) {
                        $query->andWhere([
                            '>=',
                            'i.min_wage',
                            (int)$wageInfo['min'],
                        ]);
                    }

                    if ($wageInfo['max'] > 0) {
                        $query->andWhere([
                            '<=',
                            'i.max_wage',
                            (int)$wageInfo['max'],
                        ]);
                    }
                    //除了以上条件，还必须保证两个不能同事为空
                    $query->andWhere([
                        'or',
                        [
                            '>',
                            'i.max_wage',
                            0,
                        ],
                        [
                            '>',
                            'i.min_wage',
                            0,
                        ],
                    ]);
                }
            }
        }

        //工作年限
        if (($params['workExperience'] && mb_strlen($params['workExperienceMin']) > 0) || ($params['workExperience'] && mb_strlen($params['workExperienceMax']) > 0)) {
            throw new Exception('参数错误');
        }
        if (!empty($params['workExperience'])) {
            if ($params['workExperience'] == 1) {
                //应届生/在校生
                $query->andFilterWhere(['r.identity_type' => BaseResume::IDENTITY_TYPE_GRADUATE]);
            } else {
                $workYearInfo = BaseDictionary::WORK_YEARS_LIST[$params['workExperience']];
                $query->andFilterWhere([
                    'between',
                    'r.work_experience',
                    $workYearInfo['min'],
                    $workYearInfo['max'],
                ]);
            }
        }
        if (mb_strlen($params['workExperienceMin']) > 0 && mb_strlen($params['workExperienceMax']) > 0 && $params['workExperienceMin'] > $params['workExperienceMax']) {
            throw new Exception('自定义工作年限最小值不允许大于最大值');
        }
        //自定义工作年限
        if (mb_strlen($params['workExperienceMin']) > 0) {
            $query->andFilterWhere([
                '>=',
                'r.work_experience',
                $params['workExperienceMin'],
            ]);
        }
        if (mb_strlen($params['workExperienceMax']) > 0) {
            $query->andFilterWhere([
                '<=',
                'r.work_experience',
                $params['workExperienceMax'],
            ]);
        }
        //求职状态
        if (!empty($params['workStatus'])) {
            $query->andWhere(['r.work_status' => $params['workStatus']]);
        }
        //到岗时间
        if (!empty($params['arriveDateType'])) {
            $query->andFilterWhere(['r.arrive_date_type' => $params['arriveDateType']]);
        }
        //职称
        if (!empty($params['titleId'])) {
            // $titleList = BaseDictionary::getAllChildTitle($params['titleId']);
            // $query->andWhere(['r.title_id' => $titleList]);
            $titleArray = explode(',', $params['titleId']);
            $titleList  = BaseDictionary::getAllChildTitleArray($titleArray);

            $titleCondition = ['or'];
            foreach ($titleList as $item) {
                $titleCondition[] = "find_in_set(" . $item . ",r.title_id)";
            }
            $query->andWhere($titleCondition);
        }
        //户籍国籍
        if (!empty($params['householdRegisterId'])) {
            $householdRegisterIdArr = explode(',', $params['householdRegisterId']);
            $query->andWhere(['r.household_register_id' => $householdRegisterIdArr]);
        }

        //政治面貌
        if (!empty($params['political'])) {
            $political = explode(',', $params['political']);
            $query->andWhere(['r.political_status_id' => $political]);
        }
        //年龄
        if (mb_strlen($params['ageMin']) > 0 && mb_strlen($params['ageMax']) > 0 && $params['ageMin'] > $params['ageMax']) {
            throw new Exception('年龄最小值不允许大于最大值');
        }
        if (mb_strlen($params['ageMin']) > 0) {
            $query->andWhere([
                '>=',
                'r.age',
                $params['ageMin'],
            ]);
        }
        if (mb_strlen($params['ageMax']) > 0) {
            $query->andWhere([
                '<=',
                'r.age',
                $params['ageMax'],
            ]);
        }

        //性别
        if (!empty($params['gender'])) {
            $query->andWhere(['r.gender' => $params['gender']]);
        }

        //简历下载时间
        if (!empty($params['downloadBeginTime']) && !empty($params['downloadEndTime'])) {
            //也就是来源是下载的，简历下载时间范围
            $query->andWhere(['source_type' => BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD,])
                ->andWhere([
                    'between',
                    'cr.download_time',
                    TimeHelper::dayToBeginTime($params['downloadBeginTime']),
                    TimeHelper::dayToEndTime($params['downloadEndTime']),
                ]);
        }

        //简历更新时间
        if (!empty($params['updateBeginTime']) && !empty($params['updateEndTime'])) {
            $query->andWhere([
                'between',
                'r.last_update_time',
                TimeHelper::dayToBeginTime($params['updateBeginTime']),
                TimeHelper::dayToEndTime($params['updateEndTime']),
            ]);
        }

        //求职者身份选择（-1缺失，1职场人，2应届生）
        if (!empty($params['identityType'])) {
            $query->andWhere(['r.identity_type' => $params['identityType']]);
        }

        //毕业时间
        if ($params['graduateBeginDate'] && $params['graduateEndDate']) {
            $query->leftJoin(['el' => BaseResumeEducation::tableName()], 'el.id=r.last_education_id');
            $query->andWhere([
                'between',
                'el.end_date',
                TimeHelper::dayToBeginTime($params['graduateBeginDate']),
                TimeHelper::dayToEndTime($params['graduateEndDate']),
            ]);
        }
        $query->groupBy([
            'cr.resume_id',
        ]);

        return $query;
    }

    private function getSourceListCount($type)
    {
        $query = self::getListQuery();
        switch ($type) {
            case BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD:
                $query->andWhere([
                    '!=',
                    'cr.download_time',
                    '0000-00-00 00:00:00',
                ])
                    ->andWhere([
                        'or',
                        [
                            '=',
                            'cr.apply_time',
                            '0000-00-00 00:00:00',
                        ],
                        [
                            'and',
                            [
                                '!=',
                                'cr.apply_time',
                                '0000-00-00 00:00:00',
                            ],
                            'cr.apply_time > cr.download_time',
                        ],
                    ]);
                break;
            case BaseCompanyResumeLibrary::SOURCE_TYPE_APPLY:
                $query->andWhere([
                    '!=',
                    'cr.apply_time',
                    '0000-00-00 00:00:00',
                ])
                    ->andWhere([
                        'or',
                        [
                            '=',
                            'cr.download_time',
                            '0000-00-00 00:00:00',
                        ],
                        [
                            'and',
                            [
                                '!=',
                                'cr.download_time',
                                '0000-00-00 00:00:00',
                            ],
                            'cr.apply_time < cr.download_time',
                        ],
                    ]);
                break;
        }

        return $query->count();
    }

    public function getList($params)
    {
        $this->searchParams = $params;
        $query              = self::getListQuery();
        //不管下面资料来源了，直接查三个count，用于前端显示
        $allCount      = $query->count();
        $downloadCount = self::getSourceListCount(BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD);
        $applyCount    = self::getSourceListCount(BaseCompanyResumeLibrary::SOURCE_TYPE_APPLY);
        //资料来源
        //这里会比较复杂，产品的意思是，筛选项会影响最终列表数量，但是切换tab不影响数据总量，这里虽然还是用了筛选项，但是会单独计算其余2个tab的总量
        if (!empty($params['sourceType'])) {
            switch ($params['sourceType']) {
                case BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD:
                    $query->andWhere([
                        '!=',
                        'cr.download_time',
                        '0000-00-00 00:00:00',
                    ])
                        ->andWhere([
                            'or',
                            [
                                '=',
                                'cr.apply_time',
                                '0000-00-00 00:00:00',
                            ],
                            [
                                'and',
                                [
                                    '!=',
                                    'cr.apply_time',
                                    '0000-00-00 00:00:00',
                                ],
                                'cr.apply_time > cr.download_time',
                            ],
                        ]);
                    $count = $downloadCount;
                    $query->orderBy('cr.download_time desc');
                    break;
                case BaseCompanyResumeLibrary::SOURCE_TYPE_APPLY:
                    $query->andWhere([
                        '!=',
                        'cr.apply_time',
                        '0000-00-00 00:00:00',
                    ])
                        ->andWhere([
                            'or',
                            [
                                '=',
                                'cr.download_time',
                                '0000-00-00 00:00:00',
                            ],
                            [
                                'and',
                                [
                                    '!=',
                                    'cr.download_time',
                                    '0000-00-00 00:00:00',
                                ],
                                'cr.apply_time < cr.download_time',
                            ],
                        ]);
                    $count = $applyCount;
                    $query->orderBy('cr.apply_time desc');
                    break;
            }
        } else {
            $count = $allCount;
            $query->orderBy('cr.apply_time desc,cr.download_time desc');
        }
        $pageSize = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $pages    = BaseActiveRecord::setPage($count, $params['page'], $pageSize);

        $list = $query->select([
            'cr.id',
            'cr.resume_id',
            'cr.company_id',
            'cr.source_type',
            'cr.download_time',
            'cr.apply_time',
            'r.name',
            'r.gender',
            'r.work_experience',
            'r.age',
            'r.last_education_id',
            'r.top_education_code',
            'r.member_id',
            'r.residence',
            'cr.tag',
            'cr.company_member_id',
        ])
            ->asArray()
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->all();

        foreach ($list as $k => &$item) {
            //获取现居住地
            $residence = BaseArea::getAreaName($item['residence']) ?: '';
            //获取最高学历
            $topEducation = BaseDictionary::getEducationName($item['top_education_code']);
            //获取最高学历的所学专业
            $topEducationInfo  = BaseResumeEducation::findOne($item['last_education_id']);
            $topEducationMajor = BaseMajor::getMajorName($topEducationInfo->major_id);
            //根据求职者身份，获取求职者工作经验或者毕业时间文案
            $identityExperienceText = BaseResume::getIdentityExperienceText($item['resume_id']);
            //简历基本信息
            $resume_info_arr = [
                ($item['age']) . '岁',
                $topEducation,
                $topEducationMajor ?: $topEducationInfo->major_custom,
            ];
            if ($identityExperienceText) {
                $resume_info_arr[] = $identityExperienceText;
            }
            $item['user_info'] = implode('·', $resume_info_arr);
            //获取头像
            $avatar         = BaseMember::findOneVal(['id' => $item['member_id']], 'avatar');
            $item['avatar'] = BaseMemberLoginForm::getAvatar($avatar, $item['gender']);

            //获取第一个求职意向，后面的用...表示？？
            $item['jobCategoryText']     = BaseResumeIntention::getResumeLibraryJobCategoryText($item['resume_id']);
            $item['jobCategoryFullText'] = BaseResumeIntention::getAllJobCategoryText($item['resume_id']);
            //获取最近2段教育经历
            $item['lastTwoEducationRecord'] = BaseResumeEducation::getLastRecord($item['resume_id'], 2);
            //获取最近1段工作经历,加个[0]转数组，方便前端使用
            $item['lastWorkRecord'] = BaseResumeWork::getLastRecord($item['resume_id'], 1);
            //获取邀约状态
            $item['isInvite'] = BaseResumeLibraryInviteLog::checkInviteStatus($item['resume_id'], $item['company_id']);
            //获取收藏状态
            $item['isCollect'] = BaseResumeLibraryCollect::checkMemberCollectStatus($item['resume_id'],
                $this->searchParams['memberId']);
            //获取下载状态
            $item['isDownload'] = BaseCompanyResumeLibrary::checkDownLoadStatus($item['resume_id'],
                $item['company_id']);
            //获取应聘状态
            $item['isApply'] = BaseJobApply::checkApplyRecord($item['resume_id'], $item['company_id']);
            //活跃标签
            $item['active_tag'] = BaseMember::getUserActiveTime($item['member_id']);
            //简历类型便签
            $item['resume_type_tag']  = BaseResume::getResumeLevel($item['resume_id']);
            $item['resume_title_tag'] = BaseResume::getUserSpecialInfo($item['resume_id']);
            //获取30天内是否被查看
            $item['is_resume_check'] = BaseCompanyViewResume::getCheckStatus($item['resume_id'], $item['company_id']);
            //获取收录时间：来自投递的简历，收录时间为：求职者首次投递该单位职位的时间；来自下载的简历，收录时间为：单位下载该简历的时间。
            $firstApplyTime = TimeHelper::formatDateByYear($item['apply_time'], '/');
            $downloadTime   = TimeHelper::formatDateByYear($item['download_time'], '/');
            if (!empty($item['source_type'])) {
                if ($item['source_type'] == BaseCompanyResumeLibrary::SOURCE_TYPE_APPLY) {
                    //来自投递，取首次投递时间
                    $item['sourceText']    = ['来自投递'];
                    $item['recordingTime'] = $firstApplyTime;
                } elseif ($item['source_type'] == BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD) {
                    //取下载简历时间
                    $item['sourceText']    = ['来自下载'];
                    $item['recordingTime'] = $downloadTime;
                } else {
                    if ($firstApplyTime < $downloadTime) {
                        $item['sourceText'] = ['来自投递'];
                        $item['sourceType'] = strval(BaseCompanyResumeLibrary::SOURCE_TYPE_APPLY);
                    } else {
                        $item['sourceText'] = ['来自下载'];
                        $item['sourceType'] = strval(BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD);
                    }
                    //否则的话，两种情况都有，判断哪个时间比较考前
                    if (!empty($item['apply_time'])) {
                        if (strtotime($item['apply_time']) < strtotime($item['download_time'])) {
                            $item['recordingTime'] = $firstApplyTime;
                        } else {
                            $item['recordingTime'] = $downloadTime;
                        }
                    } else {
                        $item['recordingTime'] = $downloadTime;
                    }
                }
            }

            if (!empty($item['tag'])) {
                $item['tag'] = explode(',', $item['tag']);
            } else {
                $item['tag'] = [];
            }
            //判断是否有附件简历
            $item['hasAttachmentFile'] = BaseResumeAttachment::HAS_FILE_NO;
            if (!empty(BaseResume::getCompanyResumeAttachmentAmount($item['resume_id'], $item['company_id']))) {
                $item['hasAttachmentFile'] = BaseResumeAttachment::HAS_FILE_YES;
            }
            //做一个PV统计
            BaseCompanyResumePvTotal::updateDailyTotalPv($item['resume_id']);
            //按钮组
            $item['buttonGroup'] = (new ButtonGroupAuthService())->setType(ButtonGroupAuthService::TYPE_RESUME)
                ->run($item['resume_id']);
        }

        return [
            'list'                 => $list,
            'page'                 => [
                'count' => (int)$count,
                'limit' => (int)$pages['limit'],
                'page'  => (int)$pages['page'],
            ],
            'allCount'             => (int)$allCount,
            'applyCount'           => (int)$applyCount,
            'downloadCount'        => (int)$downloadCount,
            'companyAuthorityList' => $this->companyAuthorityList,
        ];
    }

    /**
     * 获取人才详情
     * @param $params
     * @return array|void
     */
    public function getDetail($params)
    {
        $this->resumeId   = $params['resumeId'];
        $this->companyId  = $params['companyId'];
        $this->actionType = self::ACTION_TYPE_VIEW;
        $memberId         = BaseResume::findOneVal(['id' => $this->resumeId], 'member_id');
        $resumeInfo       = BaseResume::getInfo($memberId);
        //人才库这边要做特殊处理
        //手机、邮箱脱敏
        //        $resumeInfo['userInfo']['mobile'] = MaskHelper::getPhone($resumeInfo['userInfo']['mobile']);
        //        $resumeInfo['userInfo']['email']  = MaskHelper::getEmail($resumeInfo['userInfo']['email']);
        //姓名部分
        //        $resumeInfo['userInfo']['name'] = MaskHelper::getName($resumeInfo['userInfo']['name'],
        //            $resumeInfo['userInfo']['gender']);
        //获取活跃度
        $resumeInfo['userInfo']['activeTime'] = BaseMember::getUserActiveTime($memberId);
        //获取简历标签
        $companyResumeLibrary = BaseCompanyResumeLibrary::findOne([
            'company_id' => $this->companyId,
            'resume_id'  => $this->resumeId,
        ]);
        if (!empty($companyResumeLibrary['tag'])) {
            $resumeInfo['userInfo']['tagList'] = explode(',', $companyResumeLibrary['tag']);
        } else {
            $resumeInfo['userInfo']['tagList'] = [];
        }
        $resumeInfo['source_type'] = (string)$companyResumeLibrary['source_type'];
        $resumeInfo['source_bool'] = false;
        if ($companyResumeLibrary['source_type'] == BaseCompanyResumeLibrary::SOURCE_TYPE_APPLY) {
            //来自投递，取首次投递时间
            $resumeInfo['sourceText'] = ['来自投递'];
        } elseif ($companyResumeLibrary['source_type'] == BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD) {
            //取下载简历时间
            $resumeInfo['sourceText'] = ['来自下载'];
        } else {
            if ($companyResumeLibrary['download_time'] < $companyResumeLibrary['apply_time']) {
                $resumeInfo['source_type'] = strval(BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD);
                $resumeInfo['source_bool'] = true;
                $resumeInfo['sourceText']  = ['来自下载'];
            } else {
                $resumeInfo['source_type'] = strval(BaseCompanyResumeLibrary::SOURCE_TYPE_APPLY);
                $resumeInfo['sourceText']  = ['来自投递'];
            }
        }

        //判断是否有附件简历
        $resumeInfo['hasAttachmentFile'] = BaseResumeAttachment::HAS_FILE_NO;
        if (!empty(BaseResume::getCompanyResumeAttachmentAmount($this->resumeId, $this->companyId))) {
            $resumeInfo['hasAttachmentFile'] = BaseResumeAttachment::HAS_FILE_YES;
        }
        //判断是否有求职附件
        $resumeInfo['hasFile'] = BaseResumeAttachment::HAS_FILE_NO;
        if (!empty(BaseResume::getCompanyResumeFileAmount($this->resumeId, $this->companyId))) {
            $resumeInfo['hasFile'] = BaseResumeAttachment::HAS_FILE_YES;
        }

        $resumeInfo['companyResumeLibraryId'] = $companyResumeLibrary['id'];

        //获取简历操作日志
        $logParams             = [
            'resumeId'  => $this->resumeId,
            'companyId' => $this->companyId,
            'pageSize'  => 10,
        ];
        $resumeInfo['logList'] = $this->getHandleLog($logParams)['list'];
        //添加日志
        $this->log();
        //获取收藏状态
        $resumeInfo['isCollect'] = BaseResumeLibraryCollect::checkCollectStatus($this->resumeId, $this->companyId);
        // 之前的消息中心并入这里
        BaseCompanyViewResume::create($this->companyId, $this->resumeId);

        return $resumeInfo;
    }

    public function getHandleLog($params)
    {
        $query    = BaseJobApplyHandleLog::find()
            ->where([
                'resume_id'  => $params['resumeId'],
                'company_id' => $params['companyId'],
            ]);
        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: \Yii::$app->params['handleLogDefaultPageSize'];

        $pages = BaseJobApplyHandleLog::setPage($count, $params['page'], $pageSize);
        $list  = $query->select([
            'add_time',
            'content',
            'handle_id',
            'handler_name',
            'handler_type',
        ])
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            if ($item['handler_type'] == BaseJobApplyHandleLog::HANDLER_TYPE_COMPANY) {
                $companyMemberInfo  = BaseCompanyMemberInfo::findOne(['member_id' => $item['handle_id']]);
                $item['contact']    = $companyMemberInfo['contact'] ?: $item['handler_name'];
                $item['department'] = $companyMemberInfo['department'] ?: '';
            }
        }

        return [
            'list' => $list,
            'page' => [
                'count' => intval($count),
                'limit' => intval($pages['limit']),
                'page'  => intval($pages['page']),
            ],
        ];
    }

    private function connectUserInfo($data)
    {
        if (!empty($data)) {
            $text = '';
            foreach ($data as $k => $v) {
                if (!empty($v)) {
                    $text .= $v . ' | ';
                }
            }
            if (empty($text)) {
                return '';
            }

            return substr($text, 0, -3);
        } else {
            return '';
        }
    }

}
