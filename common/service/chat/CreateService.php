<?php

namespace common\service\chat;

/// https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=7703bb80-e4a9-4f33-98ad-da2157414a79&versionId=31e4ddaa-df6a-4026-8a35-492400db3af6&docId=3906055e-157b-4b6c-979d-7c2881ee385e&docType=axure&pageId=27ca56eabf654c138f38eaabc5e0a7e6&image_id=3906055e-157b-4b6c-979d-7c2881ee385e&parentId=72e2f564-139e-488e-b715-b33799f75cfe
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseChatCommonGreeting;
use common\base\models\BaseChatCommonGreetingSystem;
use common\base\models\BaseChatHistoryJob;
use common\base\models\BaseChatMessage;
use common\base\models\BaseChatMessageCard;
use common\base\models\BaseChatRoom;
use common\base\models\BaseChatRoomSession;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseMember;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseShieldCompany;
use common\helpers\DebugHelper;
use common\libs\Cache;
use common\service\CommonService;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\messageCenter\MessageCenterApplication;
use console\modules\chat\services\Events;
use Faker\Provider\Uuid;
use GatewayClient\Gateway;
use yii\db\Exception;
use yii\db\Expression;

class CreateService extends BaseService
{

    public $originalParams = [];

    public $isChatBatch = false;
    public $resumeId;
    public $resumeIds;
    public $allowedSourceResumeIds;
    public $notAllowedSourceResumeIds;
    public $notAllowedCreateRoomResumeIds;
    public $notAllowedCreateRoomResumeIdsAmount;
    public $chatDeductAmount;
    public $jobId;
    public $applyRecordId;
    public $companyMemberId;
    /** @var BaseMember */
    public $companyMemberModel;
    public $companyId;
    public $nowMemberId;
    public $fromMemberId;
    public $toMemberId;
    public $saveContent;
    public $messageId;
    public $chatUUId;
    public $messageType;
    public $historyJobRecordId;
    public $sendJobCardRoomModel;
    public $newRoomModel;

    public $actionType;

    /**
     * @var BaseResume
     */
    public $resumeModel;

    /**
     * @var BaseCompanyMemberInfo
     */
    public $companyMemberInfoModel;

    /**
     * @var BaseCompany
     */
    public $companyModel;

    /**
     * @var BaseJob
     */
    public $jobModel;

    /**
     * @var BaseJobApplyRecord
     */
    public $applyRecordModel;

    /**
     * @var BaseChatRoom
     */
    public $historyChatRoomModel;

    public $synergyjobIdList;

    public $chatAmount = 0;

    public $result = [];

    public $isCompanyResumeLibrary = false;

    public $isNeedConsume = false;

    public $actionCreatorType;
    public $actionCreatorMemberId;

    public $chatRoomeUUid;
    public $chatRoomId;

    public $currentMemberId;

    public $isCreateRoomRes = [];

    const ACTION_TYPE_COMPANY_CHECK  = 1;
    const ACTION_TYPE_COMPANY_CREATE = 2;
    const ACTION_TYPE_RESUME_CREATE  = 3;

    // 一个新的直聊所需的消费
    const NEW_CHAT_PRICE = 1;

    const COMPANY_DAILY_MAX_CHAT_AMOUNT = 100;
    const RESUME_DAILY_MAX_CHAT_AMOUNT  = 30;

    // params里面有可能有几个参数,第一个求职者id,一个是单位membeId,最后有可能是有jobId
    // 'resumeId'        => \Yii::$app->request->post('resumeId'),
    //                 'companyMemberId' => \Yii::$app->user->id,
    //                 'creatorType'     => BaseChatRoom::CREATOR_TYPE_COMPANY,
    //                 'jobId'           => \Yii::$app->request->post
    /**
     * 创建聊天室
     * @param $params
     * @return array|array[]|string[]
     * @throws Exception
     */
    public function companyCreate($params)
    {
        $this->originalParams    = $params;
        $this->actionType        = self::ACTION_TYPE_COMPANY_CREATE;
        $this->actionCreatorType = BaseChatRoom::CREATOR_TYPE_COMPANY;
        $this->createInitData();

        if ($this->historyChatRoomModel) {
            //存在历史聊天室，直接改掉删除状态
            $chatRoomId   = BaseChatRoom::findOneVal(['uuid' => $this->chatRoomeUUid], 'id');
            $sessionModel = BaseChatRoomSession::findOne([
                'chat_room_id' => $chatRoomId,
                'member_id'    => $params['companyMemberId'],
            ]);
            if ($sessionModel) {
                $sessionModel->is_delete = BaseChatRoomSession::IS_DELETE_NO;
                $sessionModel->save();
            }
        }
        //针对打开聊天弹窗逻辑修改
        if (isset($this->originalParams['isChatWindow']) && in_array($this->originalParams['isChatWindow'], [
                BaseMember::IS_CHAT_WINDOW_NO,
                BaseMember::IS_CHAT_WINDOW_YES,
            ])) {
            $this->companyMemberModel->is_chat_window = $this->originalParams['isChatWindow'];
            $this->companyMemberModel->save();
        }

        if (isset($this->originalParams['isRememberSmsChat']) && in_array($this->originalParams['isRememberSmsChat'], [
                BaseCompanyMemberInfo::IS_REMEMBER_YES,
                BaseCompanyMemberInfo::IS_REMEMBER_NO,
            ])) {
            $companyMemberInfoModel                       = BaseCompanyMemberInfo::find()
                ->where(['member_id' => $this->companyMemberId])
                ->one();
            $companyMemberInfoModel->is_remember_sms_chat = $this->originalParams['isRememberSmsChat'];
            $companyMemberInfoModel->save();
        }

        // 先看看是否有投递信息,如果有就是从投递过来的
        if ($this->applyRecordModel) {
            if ($this->historyChatRoomModel) {
                // 前端是没有做职位选择的,两者之间有历史聊天记录并且这个历史聊天记录里面的职位和投递的职位是可以联系上的
                if (!$this->jobId && ($this->historyChatRoomModel->current_job_id == $this->applyRecordModel->job_id)) {
                    $this->jobId = $this->applyRecordModel->job_id;
                    // 有过聊天,并且聊天是当前职位,那么就直接返回
                    $this->result                 = $this->getCompanySuccessReturn();
                    $this->result['isChatWindow'] = strval($this->companyMemberModel->is_chat_window);

                    return $this->result;
                }

                if (!$this->jobId) {
                    // // 有投递记录但是没有聊天记录,那么就是第一次聊天,并且也没有职位id,就返回给前端,让他选职位吧(一个是历史的职位,一个是投递对应的职位),需要有名字和职位id
                    $this->result                 = [
                        'jobSelectList' => [
                            'oldJob' => [
                                'jobId' => $this->historyChatRoomModel->current_job_id,
                                'name'  => BaseJob::findOneVal(['id' => $this->historyChatRoomModel->current_job_id],
                                    'name'),
                            ],
                            'newJob' => [
                                'jobId' => $this->applyRecordModel->job_id,
                                'name'  => BaseJob::findOneVal(['id' => $this->applyRecordModel->job_id], 'name'),
                            ],
                        ],
                    ];
                    $this->result['isChatWindow'] = strval($this->companyMemberModel->is_chat_window);

                    return $this->result;
                }
            }

            // 在设置之前,需要当前的jobId设置了,这样才好去创建
            if (!$this->historyChatRoomModel) {
                $this->jobId = $this->applyRecordModel->job_id;
                $this->createRoom();
            } else {
                if ($this->jobId && $this->historyChatRoomModel) {
                    if ($this->jobId != $this->historyChatRoomModel->current_job_id) {
                        $this->changeJob();
                    }
                }
            }

            // 返回聊天室信息
            $this->result                 = $this->getCompanySuccessReturn();
            $this->result['isChatWindow'] = strval($this->companyMemberModel->is_chat_window);

            return $this->result;
        }

        // 这里就是从人才库或者简历库过来的
        //
        // https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=7703bb80-e4a9-4f33-98ad-da2157414a79&versionId=d0ded1bd-e3b2-42e8-8f92-6b328201f527&docId=3906055e-157b-4b6c-979d-7c2881ee385e&docType=axure&pageId=d25eb18f2dd74b3e9408b49b1ad08844&image_id=3906055e-157b-4b6c-979d-7c2881ee385e&parentId=c35f393f-04f8-4d6f-8950-aeb434761084

        // 当前账号是否有
        // (协同)在线职位
        $this->getCompanyJobList();

        if (!$this->synergyjobIdList[0]) {
            $this->result = [
                'title'        => '提示',
                'tips'         => '您目前没有在招的职位，暂时无法发起聊天。建议立即发布职位。',
                'cancelBtnTxt' => '关闭',
                'type'         => 'error',
            ];

            return $this->result;
        }

        $this->checkChatAmount();

        // 单位简历库
        if ($this->isCompanyResumeLibrary) {
            if (!$this->jobId) {
                // 没有选职位,返回信息让他选咯
                $jobList      = $this->getJobListInfo($this->synergyjobIdList);
                $this->result = [
                    'isChatWindow' => strval($this->companyMemberModel->is_chat_window),
                    'jobList'      => $jobList,
                    'tips'         => '本次操作需扣除<span style="color: var(--color-primary)">0个</span>直聊点数（剩余：<span style="color: var(--color-primary)">' . $this->chatAmount . '点</span>），确定使用吗？',
                ];

                return $this->result;
            }

            if (!$this->historyChatRoomModel) {
                $this->createRoom();
            } else {
                if ($this->historyChatRoomModel->current_job_id != $this->jobId) {
                    $this->changeJob();
                } else {
                    //存在历史记录，且id相同的，推送卡片
                    $this->addJobCardRecord();
                    $this->handleSendJobCard();
                }
            }

            // 返回聊天室信息
            $this->result                 = $this->getCompanySuccessReturn();
            $this->result['isChatWindow'] = strval($this->companyMemberModel->is_chat_window);

            return $this->result;
        }

        // 其实到这里已经是单位的人才库人才了,下面如果需要发起直聊,是有可能需要扣点的了
        // 检查一下30天内是否有过聊天记录
        if ($this->historyChatRoomModel && strtotime($this->historyChatRoomModel->last_talk_time) > (time() - 30 * 24 * 3600)) {
            // 30天内有聊过,直接过去就可以了
            // 返回聊天室信息
            $this->jobId  = $this->historyChatRoomModel->current_job_id;
            $this->result = $this->getCompanySuccessReturn();

            return $this->result;
        }

        // 这里有两种情况,一种是返回给他选,一种是真的需要扣点啦,没有传职位就是返回给他选
        if (!$this->jobId) {
            if ($this->chatAmount <= 0) {
                $this->result = [
                    'title'        => '提示',
                    'tips'         => '您的直聊点数余量为0，请联系平台客服购买。',
                    'cancelBtnTxt' => '好的',
                    'type'         => 'error',
                ];
            } else {
                $jobList                      = $this->getJobListInfo($this->synergyjobIdList);
                $this->result                 = [
                    'jobList' => $jobList,
                    'tips'    => '本次操作需扣除<span style="color: var(--color-primary)">' . self::NEW_CHAT_PRICE . '个</span>直聊点数（剩余：<span style="color: var(--color-primary)">' . $this->chatAmount . '点</span>），确定使用吗？（超30天双方无聊天互动，需重新开聊。）',
                ];
                $this->result['isChatWindow'] = strval($this->companyMemberModel->is_chat_window);
            }

            // 创建一个聊天室用于聊天
            return $this->result;
        }

        // 实际要产生扣点了
        $this->isNeedConsume = true;

        // 这里其实还是有可能是切职位的
        if ($this->historyChatRoomModel && $this->historyChatRoomModel->current_job_id != $this->jobId) {
            $this->changeJob();
        } else {
            $this->createRoom();
        }

        // 返回聊天室信息
        $this->result                 = $this->getCompanySuccessReturn();
        $this->result['isChatWindow'] = strval($this->companyMemberModel->is_chat_window);

        return $this->result;
    }

    public function companyCreateBatch($params)
    {
        $this->originalParams    = $params;
        $this->isChatBatch       = true;
        $this->actionType        = self::ACTION_TYPE_COMPANY_CREATE;
        $this->actionCreatorType = BaseChatRoom::CREATOR_TYPE_COMPANY;
        // 初始化公共数据
        $this->createRoomBatchInit();
        // 设置直聊资源点数
        $this->checkChatAmount();
        // 一个接口当成两个接口的返回
        // 1、当没有职位ID的时候，返回聊天职位列表
        // 2、当有职位ID的时候，开始创建聊天室

        // 实现返回聊天职位列表
        if (!isset($this->originalParams['jobId']) || $this->originalParams['jobId'] <= 0) {
            // 此时返回职位列表
            // 验证是否有在线职位
            // (协同)在线职位
            $this->getCompanyJobList();
            if (!$this->synergyjobIdList[0]) {
                $this->result = [
                    'title'        => '提示',
                    'tips'         => '您目前没有在招的职位，暂时无法发起聊天。建议立即发布职位。',
                    'cancelBtnTxt' => '关闭',
                    'type'         => 'error',
                ];

                return $this->result;
            }

            if ($this->chatDeductAmount != 0 && ($this->chatAmount <= 0 || $this->chatAmount < $this->chatDeductAmount)) {
                //此时要扣除的直聊点数大于剩余资源
                $this->result = [
                    'title'        => '提示',
                    'tips'         => '本次操作需扣除<span style="color: var(--color-primary)">' . $this->chatDeductAmount . '</span>个直聊点数，您当前直聊点数余量为<span style="color: var(--color-primary)">' . $this->chatAmount . '</span>，请重新选择人才或联系平台客服购买。',
                    'cancelBtnTxt' => '关闭',
                    'type'         => 'error',
                ];

                return $this->result;
            }
            //通过了,返回职位列表，这里的职位列表排序
            $jobList      = $this->getJobListInfo($this->synergyjobIdList);
            $this->result = [
                'isChatWindow' => strval($this->companyMemberModel->is_chat_window),
                'jobList'      => $jobList,
                'tips'         => '本次操作需扣除<span style="color: var(--color-primary)">' . $this->chatDeductAmount . '个</span>直聊点数（剩余：<span style="color: var(--color-primary)">' . $this->chatAmount . '点</span>），确定使用吗？（超30天双方无聊天互动，需重新开聊。）',
            ];

            return $this->result;
        } else {
            if ($this->chatDeductAmount != 0 && ($this->chatAmount <= 0 || $this->chatAmount < $this->chatDeductAmount)) {
                throw new Exception('直聊点数余量不足');
            }
            //实现创建聊天室与切换职位的逻辑
            //需要消耗点数的简历ID，即创建直聊房间
            // 实际要产生扣点了
            $this->isNeedConsume = true;
            $myMaxCount          = $this->getMyNewCount();
            if ($myMaxCount >= self::COMPANY_DAILY_MAX_CHAT_AMOUNT) {
                throw new Exception('今日直聊次数已达上限，请明天再试！（每天最多可在人才库发起' . self::COMPANY_DAILY_MAX_CHAT_AMOUNT . '次新会话）');
            }
            if (($myMaxCount + $this->chatDeductAmount + $this->notAllowedCreateRoomResumeIdsAmount) > self::COMPANY_DAILY_MAX_CHAT_AMOUNT) {
                throw new Exception('今日直聊剩余' . (self::COMPANY_DAILY_MAX_CHAT_AMOUNT - $myMaxCount) . '次，请重新选择人才！（每天最多可在人才库发起' . self::COMPANY_DAILY_MAX_CHAT_AMOUNT . '次新会话）');
                //今日直聊剩余X次，请重新选择人才！
            }

            foreach ($this->allowedSourceResumeIds as $resumeId) {
                $this->createRoomBatchResumeInit($resumeId);
                if ($this->historyChatRoomModel && $this->historyChatRoomModel->current_job_id != $this->jobId) {
                    $this->changeJob();
                } else {
                    $this->createRoom();
                }
            }

            // 实际不需要扣除直聊点数
            $this->isNeedConsume = false;
            foreach ($this->notAllowedSourceResumeIds as $resumeId) {
                $this->createRoomBatchResumeInit($resumeId);
                // 是创建房间还是切换职位，还是直接跳过
                if ($this->historyChatRoomModel) {
                    //不是切换职位则直接跳过
                    if ($this->historyChatRoomModel->current_job_id != $this->jobId) {
                        $this->changeJob();
                    } else {
                        if (strtotime($this->historyChatRoomModel->last_talk_time) < (time() - 30 * 24 * 3600)) {
                            //存在历史记录，且id相同的，推送卡片
                            $this->addJobCardRecord();
                            $this->handleSendJobCard();
                        }
                    }
                } else {
                    $this->createRoom();
                }
            }

            //针对打开聊天弹窗逻辑修改
            if (isset($this->originalParams['isChatWindow']) && in_array($this->originalParams['isChatWindow'], [
                    BaseMember::IS_CHAT_WINDOW_NO,
                    BaseMember::IS_CHAT_WINDOW_YES,
                ])) {
                $this->companyMemberModel->is_chat_window = $this->originalParams['isChatWindow'];
                $this->companyMemberModel->save();
            }

            if (isset($this->originalParams['isRememberSmsChat']) && in_array($this->originalParams['isRememberSmsChat'],
                    [
                        BaseCompanyMemberInfo::IS_REMEMBER_YES,
                        BaseCompanyMemberInfo::IS_REMEMBER_NO,
                    ])) {
                $this->companyMemberInfoModel->is_remember_sms_chat = $this->originalParams['isRememberSmsChat'];
                $this->companyMemberInfoModel->save();
            }
        }
        $this->result                 = $this->getCompanySuccessReturn(true);
        $this->result['isChatWindow'] = strval($this->companyMemberModel->is_chat_window);

        return $this->result;
    }

    public function companyChangeJob($params)
    {
        $chatId          = $params['chatId'];
        $jobId           = $params['jobId'];
        $companyMemberId = $params['companyMemberId'];

        if (!$chatId || !$jobId || !$companyMemberId) {
            throw new \Exception('参数错误');
        }
        // 先检查
        $this->historyChatRoomModel = BaseChatRoom::find()
            ->where([
                'uuid'              => $chatId,
                'company_member_id' => $companyMemberId,
            ])
            ->one();

        if (!$this->historyChatRoomModel) {
            throw new \Exception('聊天室不存在');
        }

        // 找到单位信息,看看是不是自己的职位
        $this->companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $companyMemberId], 'company_id');

        if (!$this->companyId) {
            throw new \Exception('单位信息不存在');
        }

        $job = BaseJob::findOne(['id' => $jobId]);
        if (!$job) {
            throw new \Exception('职位信息不存在');
        }

        if ($job->company_id != $this->companyId) {
            throw new \Exception('职位信息不存在');
        }

        if ($this->historyChatRoomModel->current_job_id == $jobId) {
            throw new \Exception('同一职位无需切换');
        }

        $this->jobId                 = $jobId;
        $this->actionCreatorType     = BaseChatRoom::CREATOR_TYPE_COMPANY;
        $this->actionCreatorMemberId = $companyMemberId;

        // 所有的都校验通过了,可以处理切换了
        $this->changeJob();

        // 返回聊天室信息
        $this->result = $this->getCompanySuccessReturn();

        return $this->result;
    }

    public function resumeChangeJob($params)
    {
        $chatId   = $params['chatId'];
        $jobId    = $params['jobId'];
        $memberId = $params['memberId'];

        if (!$chatId || !$jobId || !$memberId) {
            throw new \Exception('参数错误');
        }
        // 先检查
        $this->historyChatRoomModel = BaseChatRoom::find()
            ->where([
                'uuid'             => $chatId,
                'resume_member_id' => $memberId,
            ])
            ->one();

        if (!$this->historyChatRoomModel) {
            throw new \Exception('聊天室不存在');
        }

        $job = BaseJob::findOne(['id' => $jobId]);
        if (!$job) {
            throw new \Exception('职位信息不存在');
        }

        if ($this->historyChatRoomModel->current_job_id == $jobId) {
            throw new \Exception('同一职位无需切换');
        }

        $this->jobId                 = $jobId;
        $this->actionCreatorType     = BaseChatRoom::CREATOR_TYPE_PERSON;
        $this->actionCreatorMemberId = $memberId;

        // 所有的都校验通过了,可以处理切换了
        $this->changeJob();

        return true;
    }

    public function getCompanySuccessReturn($isBatch = false)
    {
        // 成功后,单位其实就是返回uuid和对应的链接
        return [
            'chatId'          => $isBatch ? '' : $this->chatRoomeUUid,
            'jumpUrl'         => $isBatch ? '/chat' : "/chat?chatId={$this->chatRoomeUUid}&jobId={$this->jobId}",
            'create_room_res' => true,
        ];
    }

    public function resumeChange($params)
    {
        $chatId         = $params['chatId'];
        $jobId          = $params['jobId'];
        $resumeMemberId = $params['resumeMemberId'];
    }

    /**
     * @param $params
     * title: '',
     * tips1: '',
     * tips2: '',
     * jumpUrl: '',
     * jumpType: '',
     * confirmBtnTxt: '',
     * cancelBtnTxt: '',
     * params: '',
     * isConfirm: ''
     */
    public function resumeCreate($params)
    {
        $this->originalParams = $params;
        $this->actionType     = self::ACTION_TYPE_RESUME_CREATE;
        $jobId                = $this->originalParams['jobId'];
        $resumeId             = $this->originalParams['resumeId'];
        if (!$jobId) {
            throw new \Exception('职位信息错误');
        }

        // 这里有可能这个职位是不是合作单位的职位,如果不是,就不能发起聊天,所以需要先找这个职位的对应单位
        $companyId = BaseJob::findOneVal(['id' => $jobId], 'company_id');
        if (!$companyId) {
            throw new \Exception('职位信息错误');
        }

        $resumeMemberId        = BaseResume::findOneVal(['id' => $resumeId], 'member_id');
        $this->currentMemberId = $resumeMemberId;
        if (!$resumeMemberId) {
            throw new \Exception('求职者信息错误');
        }

        $isChat = BaseMember::findOneVal(['id' => $resumeMemberId], 'is_chat');
        if ($isChat == BaseMember::IS_CHAT_BAN) {
            return [
                'title'        => '提示',
                'isTips'       => true,
                'tips1'        => PLATFORM == 'MINI' ? '直聊功能已禁用，暂不支持发起沟通。' : '<span class="font-basic">直聊功能已禁用，暂不支持发起沟通。</span>',
                'tips2'        => PLATFORM == 'MINI' ? '(您可联系平台客服进行申诉：020-85611139)' : '<span>(您可联系平台客服进行申诉：020-85611139)</span>',
                'cancelBtnTxt' => '关闭',
            ];
        }

        $this->companyId = $companyId;
        $resumeId        = BaseResume::findOneVal(['member_id' => $resumeMemberId], 'id');

        //是否完善简历前三步
        $resumeStepNum = BaseResumeComplete::getResumeStep($resumeId);
        if ($resumeStepNum < 4) {
            //用户未完成前三步
            return [
                'title'         => '提示',
                'isTips'        => true,
                'tips1'         => PLATFORM == 'MINI' ? '系统检测到您的在线简历未完善，建议完善后再操作哦～' : '<span class="font-basic">系统检测到您的在线简历未完善，建议完善后再操作哦～</span>',
                'confirmBtnTxt' => '完善简历',
                'cancelBtnTxt'  => '关闭',
                'jumpUrl'       => '/member/person/resume',
            ];
        }

        //判断单位类型，如果是非合作单位，返回
        $companyIsCooperation = BaseCompany::findOneVal(['id' => $this->companyId], 'is_cooperation');
        if ($companyIsCooperation == BaseCompany::COOPERATIVE_UNIT_NO) {
            return [
                'title'        => '提示',
                'isTips'       => true,
                'tips1'        => PLATFORM == 'MINI' ? '该单位暂未开通直聊服务！' : '<span class="font-basic">该单位暂未开通直聊服务！</span>',
                'cancelBtnTxt' => '关闭',
            ];
        }

        $companyMemberInfoId = BaseJobContact::findOne(['job_id' => $jobId]);
        if (!$companyMemberInfoId) {
            throw new \Exception('职位联系人不存在');
        }
        $companyMember         = BaseCompanyMemberInfo::findOne(['id' => $companyMemberInfoId->company_member_info_id]);
        $this->companyMemberId = $companyMember->member_id;

        $this->actionCreatorType     = BaseChatRoom::CREATOR_TYPE_PERSON;
        $this->actionCreatorMemberId = $resumeMemberId;

        $this->createInitData();

        // 首先找到这个职位对应的联系人

        //是否已屏蔽该单位
        $shieldCompany = BaseShieldCompany::find()
            ->where([
                'resume_id'  => $this->resumeId,
                'company_id' => $this->companyId,
                'status'     => BaseShieldCompany::STATUS_ACTIVE,
            ])
            ->one();

        if (!empty($shieldCompany)) {
            $shieldCompanyTipData = [
                'title'        => '提示',
                'isTips'       => true,
                'cancelBtnTxt' => '关闭',
                'jumpUrl'      => '/member/person/setting?tab=privacy',
            ];
            if (!empty($params['platform']) && $params['platform'] == CommonService::PLATFORM_MINI) {
                $shieldCompanyTipData['tips1'] = PLATFORM == 'MINI' ? '您已屏蔽此单位！请前往PC端解除对该公司的屏蔽后再发起聊天～' : '<span class="font-basic">您已屏蔽此单位！请前往PC端解除对该公司的屏蔽后再发起聊天～</span>';
            } else {
                $shieldCompanyTipData['tips1']         = PLATFORM == 'MINI' ? '您已屏蔽此单位！请先解除对该公司的屏蔽后再发起聊天～' : '<span class="font-basic">您已屏蔽此单位！请先解除对该公司的屏蔽后再发起聊天～</span>';
                $shieldCompanyTipData['confirmBtnTxt'] = '去解除';
            }

            return $shieldCompanyTipData;
        }

        //判断投递方式是否是网址投递
        $jobDeliveryWay  = BaseJob::getDeliveryWay($this->jobId);
        $jobDeliveryType = $this->jobModel->delivery_type;

        if ($jobDeliveryWay == BaseJob::DELIVERY_WAY_LINK) {
            $appTxt = BaseJob::getDeliverTypeTxt($this->jobId);

            return [
                'title'        => '提示',
                'isTips'       => true,
                'tips1'        => PLATFORM == 'MINI' ? '该职位须通过' . $appTxt . '申请，请以用人单位公告/职位中提供的报名方式进行沟通确认！' : '<span class="font-basic">该职位须通过' . $appTxt . '申请，请以用人单位公告/职位中提供的报名方式进行沟通确认！</span>',
                'cancelBtnTxt' => '关闭',
            ];
        }

        // 找到求职者今天主动发起的聊天次数
        $count = BaseChatRoom::find()
            ->where([
                'resume_id'    => $this->resumeId,
                'creator_type' => BaseChatRoom::CREATOR_TYPE_PERSON,
            ])
            ->andWhere([
                '>=',
                'add_time',
                date('Y-m-d') . ' 00:00:00',
            ])
            ->count();

        if ($count >= self::RESUME_DAILY_MAX_CHAT_AMOUNT) {
            return [
                'title'        => '提示',
                'isTips'       => true,
                'tips1'        => PLATFORM == 'MINI' ? '今日直聊次数已达上限，请明天再试！' : '<span class="font-basic">今日直聊次数已达上限，请明天再试！</span>',
                'cancelBtnTxt' => '关闭',
            ];
        }
        $result           = [];
        $result['isTips'] = false;//默认不弹窗
        // 开始创建
        if ($this->historyChatRoomModel) {
            if ($this->historyChatRoomModel->current_job_id == $this->jobId) {
                // 继续原来的聊天
            } else {
                // 切换了职位
                $this->changeJob();
            }
            $uuid = $this->historyChatRoomModel->uuid;
        } else {
            $uuid = $this->createRoom();
            //提示
            if (!$this->historyChatRoomModel) {
                //                $rememberType    = Cache::get(Cache::PC_CHAT_NOTICE_TYPE . ':' . $this->resumeModel->member_id);
                //                $tipsInfo        = [
                //                    'tips'         => [
                //                        'content'            => '<span>已向对方发送消息！可至【我的直聊】继续沟通～</span>',
                //                        'subContent'         => '<span class="font-basic">如需修改打招呼语设置，请在【我的直聊 - 打招呼语设置】修改</span>',
                //                        'rememberText'       => '保持当前选择，7天不再提示',
                //                        'stayHereButtonText' => '停留此页',
                //                        'goOnButtonText'     => '继续聊',
                //                    ],
                //                    'rememberType' => $rememberType ?: '0',
                //                ];
                $result['title']         = '提示';
                $result['isTips']        = true;
                $result['tips1']         = PLATFORM == 'MINI' ? '已向对方发送消息！可至【我的直聊】继续沟通～' : '<span>已向对方发送消息！可至【我的直聊】继续沟通～</span>';
                $result['tips2']         = PLATFORM == 'MINI' ? '如需修改打招呼语设置，请在【我的直聊 - 打招呼语设置】修改' : '<span class="font-basic">如需修改打招呼语设置，请在【我的直聊 - 打招呼语设置】修改</span>';
                $result['cancelBtnTxt']  = '停留此页';
                $result['confirmBtnTxt'] = '继续聊';
            }
        }

        //无论什么情况，把房间删除状态清除
        $chatRoomId   = BaseChatRoom::findOneVal(['uuid' => $uuid], 'id');
        $sessionModel = BaseChatRoomSession::findOne([
            'chat_room_id' => $chatRoomId,
            'member_id'    => $resumeMemberId,
        ]);
        if (!empty($sessionModel)) {
            $sessionModel->is_delete = BaseChatRoomSession::IS_DELETE_NO;
            $sessionModel->save();
        }

        $result['chatId']         = $uuid;
        $result['chatButtonText'] = '继续聊';
        $result['jobId']          = $this->jobId;
        $result['target']         = '_blank';
        $result['jumpUrl']        = "/member/person/chat?chatId={$uuid}&jobId={$this->jobId}";

        return $result;
    }

    public function createInitData()
    {
        $params                = $this->originalParams;
        $this->resumeId        = $this->resumeId ?: $params['resumeId'];
        $this->jobId           = $this->jobId ?: $params['jobId'];
        $this->companyMemberId = $this->companyMemberId ?: $params['companyMemberId'];
        $this->applyRecordId   = $this->applyRecordId ?: $params['applyRecordId'];

        if (!$this->resumeId) {
            // 抛出异常,必须是需要有的参数
            throw new \Exception('求职者信息不能为空');
        }

        if (!$this->companyMemberId) {
            // 抛出异常,必须是需要有的参数
            throw new \Exception('单位端信息不能为空');
        }

        // 找单位信息(会员信息)
        $this->companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $this->companyMemberId], 'company_id');

        if (!$this->companyId) {
            throw new \Exception('单位信息不存在');
        }
        $this->companyMemberModel = BaseMember::findOne($this->companyMemberId);

        $this->companyMemberInfoModel = BaseCompanyMemberInfo::findOne(['member_id' => $this->companyMemberId]);
        if (!$this->companyMemberInfoModel) {
            throw new \Exception('单位信息不存在');
        }
        $this->companyModel = BaseCompany::findOne($this->companyId);
        $this->resumeModel  = BaseResume::findOne($this->resumeId);
        if (!$this->resumeModel) {
            throw new \Exception('求职者信息不存在');
        }

        if ($this->jobId) {
            $this->jobModel = BaseJob::findOne($this->jobId);
            if (!$this->jobModel) {
                throw new \Exception('职位信息不存在');
            }

            // 对职位和单位做一次强提醒
            if ($this->jobModel->company_id != $this->companyId) {
                throw new \Exception('职位信息不存在');
            }
        }

        if ($this->applyRecordId) {
            $this->applyRecordModel = BaseJobApplyRecord::findOne($this->applyRecordId);
            if (!$this->applyRecordModel) {
                throw new \Exception('投递记录不存在');
            }
            if ($this->applyRecordModel->company_id != $this->companyId) {
                throw new \Exception('投递记录不存在');
            }

            if ($this->applyRecordModel->resume_id != $this->resumeId) {
                throw new \Exception('投递记录不存在');
            }
        }

        if ($this->companyMemberId && $this->resumeId) {
            // 找一下有没有旧的聊天室
            $this->historyChatRoomModel = BaseChatRoom::findOne([
                'company_member_id' => $this->companyMemberId,
                'resume_id'         => $this->resumeId,
            ]);

            // 这里只有在非创建聊天室的时候才会有
            if ($this->actionType != self::ACTION_TYPE_COMPANY_CREATE && $this->actionType != self::ACTION_TYPE_RESUME_CREATE) {
                if (!$this->historyChatRoomModel) {
                    throw new \Exception('聊天室不存在');
                }
            }

            if ($this->historyChatRoomModel) {
                $this->chatRoomId    = $this->historyChatRoomModel->id;
                $this->chatRoomeUUid = $this->historyChatRoomModel->uuid;
            }
        }

        // 去找这个人是否在单位的简历库中
        $companyResumeLibrary = BaseCompanyResumeLibrary::findOne([
            'company_id' => $this->companyId,
            'resume_id'  => $this->resumeId,
        ]);

        if ($companyResumeLibrary) {
            $this->isCompanyResumeLibrary = true;
        }

        // 将新建房间记录一下
        if ($this->historyChatRoomModel) {
            $this->isCreateRoomRes[$this->resumeId] = 0;
        } else {
            $this->isCreateRoomRes[$this->resumeId] = 1;
        }

        $this->nowMemberId  = $this->actionCreatorType == 1 ? $this->resumeModel->member_id : $this->companyMemberId;
        $this->fromMemberId = $this->nowMemberId;
        $this->toMemberId   = $this->actionCreatorType == 1 ? $this->companyMemberId : $this->resumeModel->member_id;
    }

    /**
     * 批量创建聊天室初始化公共数据
     * 目前实现列表【推荐人才&搜索人才】
     * @return void
     * @throws \Exception
     */
    public function createRoomBatchInit()
    {
        $params                = $this->originalParams;
        $this->resumeIds       = $params['resumeId'] ? explode(',', $params['resumeId']) : [];
        $this->companyMemberId = $this->companyMemberId ?: $params['companyMemberId'];
        if (count($this->resumeIds) <= 1) {
            // 抛出异常,必须是需要有的参数
            throw new \Exception('求职者信息异常！');
        }

        if (!$this->companyMemberId) {
            // 抛出异常,必须是需要有的参数
            throw new \Exception('单位端信息不能为空');
        }

        // 找单位信息(会员信息)
        $this->companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $this->companyMemberId], 'company_id');

        if (!$this->companyId) {
            throw new \Exception('单位信息不存在');
        }
        $this->companyMemberModel = BaseMember::findOne($this->companyMemberId);

        $this->companyMemberInfoModel = BaseCompanyMemberInfo::findOne(['member_id' => $this->companyMemberId]);
        if (!$this->companyMemberInfoModel) {
            throw new \Exception('单位信息不存在');
        }
        $this->companyModel = BaseCompany::findOne($this->companyId);

        // 计算扣除的资源点数
        // 逐个简历ID去验证
        $allowedResumeIds              = [];
        $notAllowedResumeIds           = [];
        $notAllowedCreateRoomResumeIds = [];
        foreach ($this->resumeIds as $resumeId) {
            $chatRoomInfo = BaseChatRoom::find()
                ->andWhere([
                    'company_member_id' => $this->companyMemberId,
                    'resume_id'         => $resumeId,
                ])
                //                ->andWhere([
                //                    '>=',
                //                    'last_talk_time',
                //                    date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
                //                ])
                ->orderBy('last_talk_time desc')
                ->asArray()
                ->one();

            //这里就是不在简历库的人才，且最近一个聊天是否在近30天内，不在30天内则需要扣点
            if ((!BaseCompanyResumeLibrary::find()
                    ->where([
                        'company_id' => $this->companyId,
                        'resume_id'  => $resumeId,
                    ])
                    ->exists()) && (!$chatRoomInfo || strtotime($chatRoomInfo['last_talk_time']) < time() - 30 * 24 * 3600)) {
                $allowedResumeIds[] = $resumeId;
            } else {
                $notAllowedResumeIds[] = $resumeId;
                if (!$chatRoomInfo || strtotime($chatRoomInfo['last_talk_time']) < time() - 30 * 24 * 3600) {
                    $notAllowedCreateRoomResumeIds[] = $resumeId;
                }
            }
        }

        $this->allowedSourceResumeIds              = $allowedResumeIds;
        $this->notAllowedSourceResumeIds           = $notAllowedResumeIds;
        $this->chatDeductAmount                    = count($allowedResumeIds);
        $this->notAllowedCreateRoomResumeIds       = $notAllowedCreateRoomResumeIds;
        $this->notAllowedCreateRoomResumeIdsAmount = count($notAllowedCreateRoomResumeIds);
    }

    /**
     * 批量时候初始化意向先相关简历ID的信息
     * @param $resumeId
     * @return void
     */
    public function createRoomBatchResumeInit($resumeId)
    {
        $this->resumeId = $resumeId;
        $this->jobId    = $this->originalParams['jobId'];
        if ($this->jobId) {
            $this->jobModel = BaseJob::findOne($this->jobId);
            if (!$this->jobModel) {
                throw new \Exception('职位信息不存在');
            }

            // 对职位和单位做一次强提醒
            if ($this->jobModel->company_id != $this->companyId) {
                throw new \Exception('职位信息不存在');
            }
        }

        // 找一下有没有旧的聊天室
        $this->historyChatRoomModel = BaseChatRoom::findOne([
            'company_member_id' => $this->companyMemberId,
            'resume_id'         => $resumeId,
        ]);

        // 将新建房间记录一下
        if ($this->historyChatRoomModel) {
            $this->isCreateRoomRes[$resumeId] = 0;
        } else {
            $this->isCreateRoomRes[$resumeId] = 1;
        }

        if ($this->historyChatRoomModel) {
            $this->chatRoomId    = $this->historyChatRoomModel->id;
            $this->chatRoomeUUid = $this->historyChatRoomModel->uuid;
            //存在历史聊天室，直接改掉删除状态
            $chatRoomId   = BaseChatRoom::findOneVal(['uuid' => $this->chatRoomeUUid], 'id');
            $sessionModel = BaseChatRoomSession::findOne([
                'chat_room_id' => $chatRoomId,
                'member_id'    => $this->companyMemberId,
            ]);
            if ($sessionModel) {
                $sessionModel->is_delete = BaseChatRoomSession::IS_DELETE_NO;
                $sessionModel->save();
            }
        }
        $this->resumeModel  = BaseResume::findOne($resumeId);
        $this->nowMemberId  = $this->actionCreatorType == 1 ? $this->resumeModel->member_id : $this->companyMemberId;
        $this->fromMemberId = $this->nowMemberId;
        $this->toMemberId   = $this->actionCreatorType == 1 ? $this->companyMemberId : $this->resumeModel->member_id;
    }

    public function getCompanyJobList()
    {
        $this->synergyjobIdList = BaseJobContactSynergy::getSynergyJobIdList($this->companyMemberId) ?: [];
    }

    // 需要学历要求,名称,职位地区,用人部门,isTop的话,就需要表示是最近沟通的
    public function getJobListInfo($synergyJobIds, $isTop = false)
    {
        // 组成数组给前端用于显示,
        $query = BaseJob::find()
            ->where(['id' => $synergyJobIds])
            ->select([
                'id as jobId',
                'name as jobName',
                'education_type as educationType',
                'city_id as cityId',
                'department',
                'announcement_id as announcementId',
                'refresh_time as refreshTime',
            ]);
        $order = [];
        if ($this->historyChatRoomModel->current_job_id) {
            $order[] = new Expression('FIELD(id,' . $this->historyChatRoomModel->current_job_id . ') desc');
        }
        //当前筛选的职位ID
        if ($this->originalParams['currentJobId']) {
            $order[] = new Expression('FIELD(id,' . $this->originalParams['currentJobId'] . ') desc');
        }
        //获取一下当前单位最近一次沟通的职位ID
        $jobId = BaseChatRoom::find()
            ->select('current_job_id')
            ->where(['company_member_id' => $this->companyMemberId])
            ->orderBy('last_talk_time desc')
            ->scalar();

        if ($jobId) {
            $order[] = new Expression('FIELD(id,' . $jobId . ') desc');
        }

        $order['refresh_time'] = SORT_DESC;

        $list = $query->orderBy($order)
            ->asArray()
            ->all();

        if (!$list) {
            return [];
        }

        foreach ($list as &$item) {
            $job_intention = [
                BaseDictionary::getEducationName($item['educationType']),
                BaseArea::getAreaName($item['cityId']),
            ];
            //            if ($item['department']) {
            //                array_push($job_intention, $item['department']);
            //            }
            $item['job_intention']    = implode('·', $job_intention);
            $item['announcementName'] = BaseAnnouncement::findOneVal(['id' => $item['announcementId']], 'title');
            $item['areaName']         = BaseArea::getAreaName($item['cityId']) ?: '';
            $item['education']        = BaseDictionary::getEducationName($item['educationType']);
            $item['isLasTalk']        = false;
            if ($item['jobId'] == $this->historyChatRoomModel->current_job_id) {
                // 并且给他加上一个标记,让他知道这个是最近沟通过的(isCurrent)
                $item['isCurrent'] = true;
            }
        };
        if ($isTop) {
            $list[0]['isLasTalk'] = true;
        }

        return $list;
    }

    public function checkChatAmount()
    {
        $chatAmount = BaseCompanyPackageConfig::findOneVal([
            'company_id' => $this->companyId,
        ], 'chat_amount');
        if ($chatAmount <= 0) {
            return false;
        }

        $this->chatAmount = $chatAmount * 1;

        return true;
    }

    public function createRoom()
    {
        if ($this->isNeedConsume) {
            if ($this->chatAmount <= 0) {
                throw new Exception('直聊点数不足');
            }
            if (!$this->historyChatRoomModel) {
                $myMaxCount = $this->getMyNewCount();
                if ($myMaxCount >= self::COMPANY_DAILY_MAX_CHAT_AMOUNT) {
                    throw new Exception('今日直聊次数已达上限，请明天再试！（每天最多可在人才库发起' . self::COMPANY_DAILY_MAX_CHAT_AMOUNT . '次新会话）');
                }
            }
        }

        if (!$this->jobId) {
            throw new Exception('职位信息错误');
        }

        if (!$this->historyChatRoomModel) {
            $model                    = new BaseChatRoom();
            $model->resume_id         = $this->resumeId;
            $model->resume_member_id  = $this->resumeModel->member_id;
            $model->company_member_id = $this->companyMemberId;
            $model->company_id        = $this->companyId;
            $model->current_job_id    = $this->jobId;
            $model->talk_progress     = BaseChatRoom::TALK_PROGRESS_ONE_WAY;
            $model->is_agree_file     = BaseChatRoom::IS_AGREE_FILE_WAIT;
            $model->last_talk_time    = date('Y-m-d H:i:s');
            $model->uuid              = Uuid::uuid();
            $model->creator_type      = $this->actionCreatorType;
            $model->talk_job_amount   = 1;

            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }

            $this->newRoomModel = $model;
            $this->chatRoomId   = $model->id;

            //添加聊天消息表记录
            $this->addJobCardRecord();
        } else {
            // 这里是一个比较特殊的逻辑,超过30天了,但是又要消费
            $model       = $this->historyChatRoomModel;
            $this->jobId = $model->current_job_id;

            //----------------推送职位卡片-------------------
            $this->addJobCardRecord();
        }

        if ($this->isNeedConsume) {
            // 写消费逻辑
            $companyPackageApplication = new CompanyPackageApplication();
            $companyPackageApplication->resumeInitiateChat($this->companyId, self::NEW_CHAT_PRICE, $model->uuid);

            // 如果是新聊天,就扣个max
            if (!$this->historyChatRoomModel) {
                $this->setMyNewCount($myMaxCount + 1);
            }
        }

        if ($this->messageId) {
            $this->handleSendJobCard();
        }

        if (!$this->historyChatRoomModel) {
            //这里拿一下fromMemberId的信息
            $fromMemberInfo = BaseMember::findOne($this->fromMemberId);
            //创建完房间并且推送完职位片信息，那么现在开始推送首次的打招呼语与系统消息
            if ($fromMemberInfo->is_greeting == BaseMember::IS_GREETING_YES) {
                $this->addGreetingMessage();
                $this->addGreetingSysTemMessage();
            }
            DebugHelper::chat('推送消息：' . $this->chatRoomId);
            //推消息
            $app = MessageCenterApplication::getInstance();
            $app->ChatWayOneService($this->chatRoomId);
        }

        return $model->uuid;
    }

    /**
     * 添加发送职位卡片必须的表记录
     * @return void
     * @throws Exception
     */
    public function addJobCardRecord()
    {
        $currentRoomModel = $this->historyChatRoomModel ?: $this->newRoomModel;

        $this->saveContent            = [
            'jobId'       => $this->jobId,
            'nowMemberId' => $this->nowMemberId,
        ];
        $messageModel                 = new BaseChatMessage();
        $messageModel->content        = json_encode($this->saveContent);
        $messageModel->type           = Events::ACTION_TYPE_LIST['jobCard']['messageType'];
        $messageModel->main_id        = $this->jobId;
        $messageModel->is_read        = BaseChatMessage::IS_READ_NO;
        $messageModel->from_member_id = $this->fromMemberId;
        $messageModel->to_member_id   = $this->toMemberId;
        $messageModel->chat_room_id   = $currentRoomModel->id;
        $messageModel->job_id         = $this->jobId;
        $messageModel->add_time       = date('Y-m-d H:i:s');
        $messageModel->is_show_time   = BaseChatMessage::IS_SHOW_TIME_YES;

        if (!$messageModel->save()) {
            throw new \Exception('message保存失败');
        }
        //如果是历史的，更新时间
        if ($this->historyChatRoomModel) {
            //更新last_talk_time
            $this->historyChatRoomModel->last_talk_time = date('Y-m-d H:i:s');
            $this->historyChatRoomModel->save();
            if (!$this->historyChatRoomModel->save()) {
                throw new \Exception('更新历史房间最后聊天时间失败');
            }

            $isApply = BaseJobApplyRecord::find()
                ->where([
                    'job_id'    => $this->jobId,
                    'resume_id' => $this->resumeId,
                ])
                ->exists() ? BaseChatHistoryJob::IS_APPLY_YES : BaseChatHistoryJob::IS_APPLY_NO;
        }

        $this->actionCreatorMemberId = $this->nowMemberId;

        // 同时添加一个职位沟通历史
        $historyModel                    = new BaseChatHistoryJob();
        $historyModel->chat_room_id      = $currentRoomModel->id;
        $historyModel->type              = BaseChatHistoryJob::TYPE_CREATE;
        $historyModel->job_id            = $this->jobId;
        $historyModel->creator_type      = $this->actionCreatorType;
        $historyModel->creator_member_id = $this->actionCreatorMemberId;
        $historyModel->is_apply          = $isApply ?: BaseChatHistoryJob::IS_APPLY_NO;

        if (!$historyModel->save()) {
            throw new Exception($historyModel->getFirstErrorsMessage());
        }

        $this->chatRoomeUUid = $currentRoomModel->uuid;
        $this->messageId     = $messageModel->id;
    }

    /**
     * 处理打招呼语推送
     */
    public function addGreetingMessage()
    {
        $memberInfo = BaseMember::findOne($this->fromMemberId);
        //获取单位的默认招呼语
        if ($memberInfo->greeting_type == BaseMember::GREETING_TYPE_SYSTEM) {
            $messageText = BaseChatCommonGreetingSystem::findOne($memberInfo->greeting_default_id)->content;
        } else {
            $messageText = BaseChatCommonGreeting::findOne($memberInfo->greeting_default_id)->content;
        }
        //写入单位主动发送打招呼消息
        $content                      = [
            BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[BaseChatMessage::TYPE_TEXT] => $messageText,
        ];
        $messageModel                 = new BaseChatMessage();
        $messageModel->content        = json_encode($content);
        $messageModel->type           = Events::ACTION_TYPE_LIST[Events::ACTION_TYPE_TEXT]['messageType'];
        $messageModel->main_id        = 0;
        $messageModel->status         = BaseChatMessage::STATUS_DELIVERY;
        $messageModel->is_read        = BaseChatMessage::IS_READ_NO;
        $messageModel->from_member_id = $this->fromMemberId;
        $messageModel->to_member_id   = $this->toMemberId;
        $messageModel->chat_room_id   = $this->chatRoomId;
        $messageModel->job_id         = $this->jobId;
        $messageModel->add_time       = date('Y-m-d H:i:s');
        $messageModel->is_show_time   = BaseChatMessage::IS_SHOW_TIME_NO;

        if (!$messageModel->save()) {
            throw new \Exception('message保存失败');
        }
        $this->messageId   = $messageModel->id;
        $this->messageType = BaseChatMessage::TYPE_TEXT;
        //判断对方是否在线，推送消息、
        $this->updateRoomSession();
        $this->sendGreetingMessage($messageText);
    }

    /**
     * 发送打招呼语
     * @param $messageText
     * @return void
     * @throws Exception
     */
    public function sendGreetingMessage($messageText)
    {
        $myContent = [
            BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[BaseChatMessage::TYPE_TEXT] => $messageText,
        ];
        $arr       = [];
        //发给自己
        $arr['content']   = $myContent;
        $arr['status']    = BaseChatMessage::STATUS_DELIVERY;
        $arr['messageId'] = $this->messageId . '';
        // 这里有可能是卡片操作,所以返回去的类型要按照情况来判断
        $arr['type']     = BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[$this->messageType];
        $arr['chatId']   = $this->chatRoomeUUid;
        $arr['cuid']     = '';
        $arr['memberId'] = (string)$this->fromMemberId;
        $arr['time']     = date('H:i');
        $arr['avatar']   = BaseChatMessage::getMessageAvatar($this->fromMemberId);
        Gateway::sendToUid($this->fromMemberId, json_encode($arr));

        //再发一条侧边栏卡片信息
        if ($this->messageId != BaseChatMessage::REQUEST_FILE_MESSAGE_ID) {
            $sessionInfo                = BaseChatRoomSession::getSessionInfo($this->fromMemberId, $this->messageId);
            $sessionFullInfo['content'] = $sessionInfo;
            $sessionFullInfo['type']    = 'updateSession';
            Gateway::sendToUid($this->fromMemberId, json_encode($sessionFullInfo));
        }
        // 还多更新读取数量
        // 有三个数字,直聊未读总数
        $data = BaseMemberMessage::getUnreadData($this->fromMemberId);
        // 需要一个新的类型,unread
        $content = [
            'type'    => 'unread',
            'content' => $data,
        ];

        Gateway::sendToUid($this->fromMemberId, json_encode($content));

        //判断对方是否在线，发给对方
        $isOnline = Gateway::isUidOnline($this->toMemberId);
        if ($isOnline) {
            $targetArr = [];
            // 这里会有一个很奇怪的逻辑,需要知道当前接收人
            $toContent              = [
                'text' => $messageText,
            ];
            $targetArr['content']   = $toContent;
            $targetArr['status']    = BaseChatMessage::STATUS_DELIVERY;
            $targetArr['messageId'] = $this->messageId . '';
            $targetArr['type']      = BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[$this->messageType];
            $targetArr['chatId']    = $this->chatRoomeUUid;
            $targetArr['cuid']      = '';
            $targetArr['time']      = date('H:i');
            $targetArr['memberId']  = $this->fromMemberId;
            //判断接收用户是不是单位，如果是求职者发给单位，判断头像是否要模糊
            $toMemberRole        = BaseMember::findOneVal(['id' => $this->toMemberId], 'type');
            $companyId           = $toMemberRole == BaseMember::TYPE_COMPANY ? BaseCompanyMemberInfo::findOneVal(['member_id' => $this->toMemberId],
                'company_id') : '';
            $targetArr['avatar'] = BaseChatMessage::getMessageAvatar($this->fromMemberId, $companyId);
            Gateway::sendToUid($this->toMemberId, json_encode($targetArr));
            //发送多1条session信息
            if ($this->messageId != BaseChatMessage::REQUEST_FILE_MESSAGE_ID) {
                $sessionInfo = BaseChatRoomSession::getSessionInfo($this->toMemberId, $this->messageId);

                $sessionFullInfo['content'] = $sessionInfo;
                $sessionFullInfo['type']    = 'updateSession';
                Gateway::sendToUid($this->toMemberId, json_encode($sessionFullInfo));
            }

            // 这里如果对方已经把我们的session删除了,就要改为非删除的
            $sessionModel = BaseChatRoomSession::findOne([
                'chat_room_id' => $this->chatRoomId,
                'member_id'    => $this->toMemberId,
            ]);
            if ($sessionModel && $sessionModel->is_delete == BaseChatRoomSession::IS_DELETE_YES) {
                $sessionModel->is_delete = BaseChatRoomSession::IS_DELETE_NO;
                $sessionModel->save();
            }

            // 还多更新读取数量
            // 有三个数字,直聊未读总数
            $data = BaseMemberMessage::getUnreadData($this->toMemberId);
            // 需要一个新的类型,unread
            $content = [
                'type'    => 'unread',
                'content' => $data,
            ];

            Gateway::sendToUid($this->toMemberId, json_encode($content));
        }
    }

    /**
     * 处理打招呼语系统消息推送
     */
    public function addGreetingSystemMessage()
    {
        if ($this->actionCreatorType == BaseChatRoom::CREATOR_TYPE_COMPANY) {
            $messageText = CommonGreeting::COMPANY_DEFAULT_GREETING_SYSTEM_MESSAGE;
        } else {
            $messageText = CommonGreeting::RESUME_DEFAULT_GREETING_SYSTEM_MESSAGE;
        }
        //写入主动发送打招呼消息
        $content                      = [
            BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[BaseChatMessage::TYPE_TEXT] => $messageText,
            BaseChatMessage::TYPE_EVENT_KEY_TEXT                                => BaseChatMessage::TYPE_EVENT_KEY_SYSTEM_GREETING_EVENT,
        ];
        $messageModel                 = new BaseChatMessage();
        $messageModel->content        = json_encode($content);
        $messageModel->type           = Events::ACTION_TYPE_LIST[Events::ACTION_TYPE_SYSTEM]['messageType'];
        $messageModel->main_id        = 0;
        $messageModel->is_read        = BaseChatMessage::IS_READ_YES;
        $messageModel->status         = BaseChatMessage::STATUS_READ;
        $messageModel->from_member_id = $this->fromMemberId;
        $messageModel->to_member_id   = 0;
        $messageModel->chat_room_id   = $this->chatRoomId;
        $messageModel->job_id         = $this->jobId;
        $messageModel->add_time       = date('Y-m-d H:i:s');
        $messageModel->is_show_time   = BaseChatMessage::IS_SHOW_TIME_NO;

        if (!$messageModel->save()) {
            throw new \Exception('message保存失败');
        }
        $this->messageId   = $messageModel->id;
        $this->messageType = BaseChatMessage::TYPE_SYSTEM;
        //判断对方是否在线，推送消息、
        $this->sendGreetingSystemMessage($content);
    }

    /**
     * 发送打招呼语系统消息
     * @param $messageText
     * @return void
     * @throws Exception
     */
    public function sendGreetingSystemMessage($messageText)
    {
        $arr = [];
        //发给自己
        $arr['content']   = $messageText;
        $arr['status']    = BaseChatMessage::STATUS_READ;
        $arr['messageId'] = $this->messageId . '';
        // 这里有可能是卡片操作,所以返回去的类型要按照情况来判断
        $arr['type']     = BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[$this->messageType];
        $arr['chatId']   = $this->chatRoomeUUid;
        $arr['cuid']     = '';
        $arr['time']     = date('H:i');
        $arr['memberId'] = (string)$this->fromMemberId;
        $arr['avatar']   = BaseChatMessage::getMessageAvatar($this->fromMemberId);
        Gateway::sendToUid($this->fromMemberId, json_encode($arr));
        //        //判断对方是否在线，发给对方
        //        $isOnline = Gateway::isUidOnline($this->toMemberId);
        //        if ($isOnline) {
        //            $targetArr = [];
        //            // 这里会有一个很奇怪的逻辑,需要知道当前接收人
        //            $toContent              = [
        //                'text' => $messageText,
        //            ];
        //            $targetArr['content']   = $toContent;
        //            $targetArr['status']    = BaseChatMessage::STATUS_DELIVERY;
        //            $targetArr['messageId'] = $this->messageId . '';
        //            $targetArr['type']      = BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[$this->messageType];
        //            $targetArr['chatId']    = $this->chatRoomeUUid;
        //            $targetArr['cuid']      = '';
        //            $targetArr['time']      = date('H:i');
        //            $targetArr['memberId']  = $this->fromMemberId;
        //            //判断接收用户是不是单位，如果是求职者发给单位，判断头像是否要模糊
        //            $toMemberRole        = BaseMember::findOneVal(['id' => $this->toMemberId], 'type');
        //            $companyId           = $toMemberRole == BaseMember::TYPE_COMPANY ? BaseCompanyMemberInfo::findOneVal(['member_id' => $this->toMemberId],
        //                'company_id') : '';
        //            $targetArr['avatar'] = BaseChatMessage::getMessageAvatar($this->fromMemberId, $companyId);
        //            Gateway::sendToUid($this->toMemberId, json_encode($targetArr));
        //            //发送多1条session信息
        //            if ($this->messageId != BaseChatMessage::REQUEST_FILE_MESSAGE_ID) {
        //                $sessionInfo = BaseChatRoomSession::getSessionInfo($this->toMemberId, $this->messageId);
        //
        //                $sessionFullInfo['content'] = $sessionInfo;
        //                $sessionFullInfo['type']    = 'updateSession';
        //                Gateway::sendToUid($this->toMemberId, json_encode($sessionFullInfo));
        //            }
        //
        //            // 这里如果对方已经把我们的session删除了,就要改为非删除的
        //            $sessionModel = BaseChatRoomSession::findOne([
        //                'chat_room_id' => $this->chatRoomId,
        //                'member_id'    => $this->toMemberId,
        //            ]);
        //            if ($sessionModel && $sessionModel->is_delete == BaseChatRoomSession::IS_DELETE_YES) {
        //                $sessionModel->is_delete = BaseChatRoomSession::IS_DELETE_NO;
        //                $sessionModel->save();
        //            }
        //        }
    }

    /**
     * 处理职位卡片发送
     * @return void
     * @throws Exception
     */
    public function handleSendJobCard()
    {
        $this->messageType = BaseChatMessage::TYPE_JOB_CARD;

        //判断对方是否在线，推送消息、
        $this->updateRoomSession();
        $this->sendJobCardMessage();

        //如果当前消息是卡片类型，新增记录
        $cardModel                  = new BaseChatMessageCard();
        $cardModel->add_time        = date('Y-m-d H:i:s');
        $cardModel->type            = BaseChatMessageCard::CARD_TYPE_JOB;
        $cardModel->chat_room_id    = $this->chatRoomId;
        $cardModel->chat_message_id = $this->messageId;
        $cardModel->save();
    }

    /**
     * 更新聊天室session
     * @return void
     */
    public function updateRoomSession()
    {
        // 加两边的chatRoomSession
        $fromSessionModel = BaseChatRoomSession::findOne([
            'chat_room_id' => $this->chatRoomId,
            'member_id'    => $this->fromMemberId,
        ]);
        if (!$fromSessionModel) {
            $fromSessionModel               = new BaseChatRoomSession();
            $fromSessionModel->chat_room_id = $this->chatRoomId;
            $fromSessionModel->member_id    = $this->fromMemberId;
            $fromSessionModel->is_delete    = BaseChatRoomSession::IS_DELETE_NO;
            $fromSessionModel->is_top       = BaseChatRoomSession::IS_TOP_NO;
            $fromSessionModel->add_time     = date('Y-m-d H:i:s');
        } else {
            $fromSessionModel->update_time = date('Y-m-d H:i:s');
        }
        $fromSessionModel->last_talk_time = date('Y-m-d H:i:s');
        $fromSessionModel->save();

        //接受人的
        $toSessionModel = BaseChatRoomSession::findOne([
            'chat_room_id' => $this->chatRoomId,
            'member_id'    => $this->toMemberId,
        ]);

        if (!$toSessionModel) {
            $toSessionModel               = new BaseChatRoomSession();
            $toSessionModel->chat_room_id = $this->chatRoomId;
            $toSessionModel->member_id    = $this->toMemberId;
            $toSessionModel->is_delete    = BaseChatRoomSession::IS_DELETE_NO;
            $toSessionModel->is_top       = BaseChatRoomSession::IS_TOP_NO;
            $toSessionModel->add_time     = date('Y-m-d H:i:s');
        } else {
            $toSessionModel->update_time = date('Y-m-d H:i:s');
        }
        $toSessionModel->unread_amount  += 1;
        $toSessionModel->last_talk_time = date('Y-m-d H:i:s');
        $toSessionModel->save();
    }

    public function sendJobCardMessage()
    {
        // 这里一定要及时更新,要不职位卡片那边对应不上
        $lastChatJobModel                  = BaseChatHistoryJob::find()
            ->where(['chat_room_id' => $this->chatRoomId])
            ->orderBy(['id' => SORT_DESC])
            ->one();
        $lastChatJobModel->chat_message_id = $this->messageId;
        $lastChatJobModel->save();
        $arr = [];
        //发给自己
        $arr['content']   = BaseChatMessage::getRealContent($this->messageType, $this->actionCreatorType,
            $this->saveContent, $this->messageId) ?: [];
        $arr['status']    = BaseChatMessage::STATUS_DELIVERY;
        $arr['messageId'] = $this->messageId . '';
        // 这里有可能是卡片操作,所以返回去的类型要按照情况来判断
        $arr['type']     = BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[$this->messageType];
        $arr['chatId']   = $this->chatRoomeUUid;
        $arr['cuid']     = '';
        $arr['memberId'] = (string)$this->fromMemberId;
        $arr['avatar']   = BaseChatMessage::getMessageAvatar($this->fromMemberId);
        $arr['time']     = date('H:i');
        Gateway::sendToUid($this->fromMemberId, json_encode($arr));

        //再发一条侧边栏卡片信息
        if ($this->messageId != BaseChatMessage::REQUEST_FILE_MESSAGE_ID) {
            $sessionInfo                = BaseChatRoomSession::getSessionInfo($this->fromMemberId, $this->messageId);
            $sessionFullInfo['content'] = $sessionInfo;
            $sessionFullInfo['type']    = 'updateSession';
            Gateway::sendToUid($this->fromMemberId, json_encode($sessionFullInfo));
        }
        // 还多更新读取数量
        // 有三个数字,直聊未读总数
        $data = BaseMemberMessage::getUnreadData($this->fromMemberId);
        // 需要一个新的类型,unread
        $content = [
            'type'    => 'unread',
            'content' => $data,
        ];

        Gateway::sendToUid($this->fromMemberId, json_encode($content));

        //判断对方是否在线，发给对方
        $isOnline = Gateway::isUidOnline($this->toMemberId);
        if ($isOnline) {
            $targetArr = [];
            // 这里会有一个很奇怪的逻辑,需要知道当前接收人
            $toTargetSaveContent = [
                'jobId'       => $this->jobId,
                'nowMemberId' => $this->toMemberId,
            ];
            $toUserType          = $this->actionCreatorType == 1 ? 2 : 1;

            $targetArr['content']   = BaseChatMessage::getRealContent($this->messageType, $toUserType,
                $toTargetSaveContent, $this->messageId);
            $targetArr['status']    = BaseChatMessage::STATUS_DELIVERY;
            $targetArr['messageId'] = $this->messageId . '';
            $targetArr['type']      = BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[$this->messageType];
            $targetArr['chatId']    = $this->chatRoomeUUid;
            $targetArr['cuid']      = '';
            $targetArr['memberId']  = $this->fromMemberId;
            //判断接收用户是不是单位，如果是求职者发给单位，判断头像是否要模糊
            $toMemberRole        = BaseMember::findOneVal(['id' => $this->toMemberId], 'type');
            $companyId           = $toMemberRole == BaseMember::TYPE_COMPANY ? BaseCompanyMemberInfo::findOneVal(['member_id' => $this->toMemberId],
                'company_id') : '';
            $targetArr['avatar'] = BaseChatMessage::getMessageAvatar($this->fromMemberId, $companyId);
            $targetArr['time']   = date('H:i');
            Gateway::sendToUid($this->toMemberId, json_encode($targetArr));
            //发送多1条session信息
            if ($this->messageId != BaseChatMessage::REQUEST_FILE_MESSAGE_ID) {
                $sessionInfo = BaseChatRoomSession::getSessionInfo($this->toMemberId, $this->messageId);

                $sessionFullInfo['content'] = $sessionInfo;
                $sessionFullInfo['type']    = 'updateSession';
                Gateway::sendToUid($this->toMemberId, json_encode($sessionFullInfo));
            }

            // 这里如果对方已经把我们的session删除了,就要改为非删除的
            $sessionModel = BaseChatRoomSession::findOne([
                'chat_room_id' => $this->chatRoomId,
                'member_id'    => $this->toMemberId,
            ]);
            if ($sessionModel && $sessionModel->is_delete == BaseChatRoomSession::IS_DELETE_YES) {
                $sessionModel->is_delete = BaseChatRoomSession::IS_DELETE_NO;
                $sessionModel->save();
            }

            // 还多更新读取数量
            // 有三个数字,直聊未读总数
            $data = BaseMemberMessage::getUnreadData($this->toMemberId);
            // 需要一个新的类型,unread
            $content = [
                'type'    => 'unread',
                'content' => $data,
            ];

            Gateway::sendToUid($this->toMemberId, json_encode($content));
        }
    }

    public function changeJob()
    {
        $this->historyChatRoomModel->current_job_id  = $this->jobId;
        $this->historyChatRoomModel->talk_job_amount = $this->historyChatRoomModel->talk_job_amount + 1;
        $this->historyChatRoomModel->last_talk_time  = date('Y-m-d H:i:s');
        if (!$this->historyChatRoomModel->save()) {
            throw new Exception($this->historyChatRoomModel->getFirstErrorsMessage());
        }
        //定义参数
        $this->nowMemberId           = $this->actionCreatorType == 1 ? $this->resumeModel->member_id : $this->companyMemberId;
        $this->fromMemberId          = $this->nowMemberId;
        $this->toMemberId            = $this->actionCreatorType == 1 ? $this->companyMemberId : $this->resumeModel->member_id;
        $this->actionCreatorMemberId = $this->nowMemberId;

        // 同时添加一个职位沟通历史
        $historyModel                    = new BaseChatHistoryJob();
        $historyModel->chat_room_id      = $this->historyChatRoomModel->id;
        $historyModel->type              = BaseChatHistoryJob::TYPE_CHANGE;
        $historyModel->job_id            = $this->jobId;
        $historyModel->creator_type      = $this->actionCreatorType;
        $historyModel->creator_member_id = $this->actionCreatorMemberId;
        $historyModel->is_apply          = BaseChatHistoryJob::IS_APPLY_NO;
        $historyModel->save();

        if (!$historyModel->save()) {
            throw new Exception($historyModel->getFirstErrorsMessage());
        }
        $this->chatUUId = BaseChatRoom::findOneVal(['id' => $this->historyChatRoomModel->id], 'uuid');

        //添加聊天消息表记录
        $this->saveContent            = [
            'jobId'       => $this->jobId,
            'nowMemberId' => $this->nowMemberId,
        ];
        $messageModel                 = new BaseChatMessage();
        $messageModel->content        = json_encode($this->saveContent);
        $messageModel->type           = Events::ACTION_TYPE_LIST['changeJob']['messageType'];
        $messageModel->main_id        = $this->jobId;
        $messageModel->is_read        = BaseChatMessage::IS_READ_NO;
        $messageModel->from_member_id = $this->fromMemberId;
        $messageModel->to_member_id   = $this->toMemberId;
        $messageModel->chat_room_id   = $this->historyChatRoomModel->id;
        $messageModel->job_id         = $this->jobId;
        $messageModel->add_time       = date('Y-m-d H:i:s');
        $messageModel->is_show_time   = BaseChatMessage::IS_SHOW_TIME_YES;

        if (!$messageModel->save()) {
            throw new \Exception('message保存失败');
        }
        $this->messageId = $messageModel->id;

        $this->chatRoomeUUid = $this->historyChatRoomModel->uuid;
        $this->chatRoomId    = $this->historyChatRoomModel->id;
        $this->jobId         = $this->historyChatRoomModel->current_job_id;

        if ($this->isNeedConsume) {
            // 写消费逻辑
            $companyPackageApplication = new CompanyPackageApplication();
            $companyPackageApplication->resumeInitiateChat($this->companyId, self::NEW_CHAT_PRICE,
                $this->historyChatRoomModel->uuid);
        }
        $this->messageType = BaseChatMessage::TYPE_JOB_CARD;

        //发送消息
        $this->updateRoomSession();
        $this->sendJobCardMessage();

        //如果当前消息是卡片类型，新增记录
        $cardModel                  = new BaseChatMessageCard();
        $cardModel->add_time        = date('Y-m-d H:i:s');
        $cardModel->type            = BaseChatMessageCard::CARD_TYPE_JOB;
        $cardModel->chat_room_id    = $this->chatRoomId;
        $cardModel->chat_message_id = $this->messageId;
        $cardModel->save();

        return true;
    }

    public function getMyNewCount()
    {
        $companyMemberId = $this->companyMemberId;
        $baseKey         = Cache::COMPANY_CHAT_RESUME_COUNT_KEY;
        $key             = $baseKey . ':' . $companyMemberId;
        $json            = Cache::get($key);
        $day             = date('Ymd');
        if ($json) {
            $data = json_decode($json, true);
            if (isset($data[$day])) {
                return $data[$day];
            } else {
                return 0;
            }
        }
    }

    public function setMyNewCount($count = 1)
    {
        $companyMemberId = $this->companyMemberId;
        $baseKey         = Cache::COMPANY_CHAT_RESUME_COUNT_KEY;
        $key             = $baseKey . ':' . $companyMemberId;
        $data            = [
            date('Ymd') => $count,
        ];

        Cache::set($key, json_encode($data));

        return $count;
    }

    /**
     * 返回用户
     * @return string
     */
    public function getCompanyMemberIsRememberSmsChat()
    {
        return strval($this->companyMemberInfoModel->is_remember_sms_chat);
    }
}
