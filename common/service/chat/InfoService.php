<?php

namespace common\service\chat;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseChatHistoryJob;
use common\base\models\BaseChatMessage;
use common\base\models\BaseChatRoom;
use common\base\models\BaseChatRoomSession;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeWxBind;
use common\helpers\ArrayHelper;
use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\MaskHelper;
use common\helpers\TimeHelper;
use common\libs\WxPublic;
use common\libs\WxWork;
use Faker\Provider\Base;
use yii\base\Exception;
use yii\helpers\Url;

class InfoService extends BaseService
{
    /**
     * 获取求职者聊天列表
     * @param $memberId
     * @param $keyword
     * @param $readStatus
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getPersonChatList($memberId, $keyword = '', $readStatus = '')
    {
        $memberRole = BaseMember::findOneVal(['id' => $memberId], 'type');
        if ($memberRole != BaseMember::TYPE_PERSON) {
            throw new Exception('用户角色错误');
        }

        //获取聊天室列表
        $query = BaseChatRoomSession::find()
            ->alias('a')
            ->leftJoin(['b' => BaseChatRoom::tableName()], 'a.chat_room_id = b.id')
            ->where([
                'member_id'   => $memberId,
                'a.is_delete' => BaseChatRoomSession::IS_DELETE_NO,
            ])
            ->select([
                'b.id as roomId',
                'b.company_id as companyId',
                'b.company_member_id as companyMemberId',
                'b.uuid as chatId',
                'a.is_top as isTop',
                'a.unread_amount as unreadAmount',
                'b.current_job_id as jobId',
            ]);
        if ($keyword) {
            //带有关键词，寻找名称
            $query->leftJoin(['m' => BaseMember::tableName()], 'm.id = b.company_member_id')
                ->andWhere([
                    'like',
                    'm.name',
                    $keyword,
                ]);
        }
        if ($readStatus) {
            //如果是未读查询
            $query->andWhere([
                '>',
                'a.unread_amount',
                0,
            ]);
        }

        $chatRoomList = $query->asArray()
            ->all();

        if (empty($chatRoomList)) {
            return [];
        }

        //获取每个房间未读消息数量、最后一条聊天
        foreach ($chatRoomList as &$room) {
            $lastMessage = BaseChatMessage::find()
                ->where(['chat_room_id' => $room['roomId']])
                ->select([
                    'id as messageId',
                    'content as messageContent',
                    'status as messageStatus',
                    'add_time as trueTime',
                ])
                ->orderBy('add_time desc,id desc')
                ->asArray()
                ->one();

            $room                = array_merge($room, $lastMessage);
            $room['showTime']    = TimeHelper::formatMessageSessionShowTime($lastMessage['trueTime']);
            $room['listContent'] = BaseChatMessage::getListContent($lastMessage['messageId'], $memberId);
            //聊天对象的信息，单位看到的是职位，求职者看到的是单位+部门,还有头像
            //拼接单位信息
            $companyName  = BaseCompany::findOneVal(['id' => $room['companyId']], 'full_name');
            $department   = BaseCompanyMemberInfo::findOneVal(['member_id' => $room['companyMemberId']], 'department');
            $room['info'] = $companyName;
            $room['info'] = $department ? $room['info'] . '｜' . $department : $room['info'];
            //获取对方头像,如果单位用户没有头像，获取单位logo
            $room['avatarUrl'] = BaseChatMessage::getMessageAvatar($room['companyMemberId']);
            //获取用户名
            $room['username'] = BaseCompanyMemberInfo::findOneVal(['member_id' => $room['companyMemberId']], 'contact');

            $room['unreadAmount'] = (int)$room['unreadAmount'];
        }

        return $chatRoomList;
    }

    /**
     * 获取单位聊天列表
     * @param $memberId
     * @param $type
     * @param $keyword
     * @param $readStatus
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getCompanyChatList($memberId, $type = '', $keyword = '', $readStatus = '', $page = '')
    {
        //校验用户角色
        $memberRole = BaseMember::findOneVal(['id' => $memberId], 'type');
        if ($memberRole != BaseMember::TYPE_COMPANY) {
            throw new Exception('用户角色错误');
        }
        //获取聊天室列表
        $query = BaseChatRoomSession::find()
            ->alias('a')
            ->innerJoin(['b' => BaseChatRoom::tableName()], 'a.chat_room_id = b.id')
            ->where([
                'member_id'   => $memberId,
                'a.is_delete' => BaseChatRoomSession::IS_DELETE_NO,
            ])
            ->select([
                'b.id as roomId',
                'b.resume_member_id as resumeMemberId',
                'b.company_id as companyId',
                'b.resume_id as resumeId',
                'b.current_job_id as jobId',
                'b.uuid as chatId',
                'a.is_top as isTop',
                'a.unread_amount as unreadAmount',
                'creator_type as creatorType',
                'talk_progress as talkProgress',
            ]);
        if (!empty($keyword)) {
            $query->leftJoin(['j' => BaseJob::tableName()], 'j.id = b.current_job_id')
                ->leftJoin(['m' => BaseMember::tableName()], 'm.id = b.resume_member_id')
                ->andWhere([
                    'or',
                    [
                        'like',
                        'm.name',
                        $keyword,
                    ],
                    [
                        'like',
                        'j.name',
                        $keyword,
                    ],
                ]);
        }

        if (!empty($type)) {
            switch ($type) {
                case BaseChatRoom::ROOM_TYPE_GREET:
                    //人才发起的单向沟通会话
                    $query->andWhere([
                        'creator_type'  => BaseChatRoom::CREATOR_TYPE_PERSON,
                        'talk_progress' => BaseChatRoom::TALK_PROGRESS_ONE_WAY,
                    ]);
                    break;
                case BaseChatRoom::ROOM_TYPE_IN_CHAT:
                    //沟通中，双向沟通的会话
                    $query->andWhere(['talk_progress' => BaseChatRoom::TALK_PROGRESS_TWO_WAY]);
                    break;
                case BaseChatRoom::ROOM_TYPE_APPLY:
                    //已投递
                    $query->leftJoin(['hj' => BaseChatHistoryJob::tableName()],
                        'hj.chat_room_id = b.id and hj.job_id=b.current_job_id')
                        ->andWhere(['hj.is_apply' => BaseChatHistoryJob::IS_APPLY_YES]);
                    break;
            }
        }

        if ($readStatus) {
            //如果是未读查询
            $query->andWhere([
                '>',
                'a.unread_amount',
                0,
            ]);
        }

        if ($page) {
            $defaultPageSize = 50;
            $query->limit($defaultPageSize)
                ->orderBy('a.last_talk_time desc')
                ->offset(($page - 1) * $defaultPageSize);
        }

        $chatRoomList = $query->asArray()
            ->all();

        if (empty($chatRoomList)) {
            return [];
        }
        //获取每个房间未读消息数量、最后一条聊天
        foreach ($chatRoomList as &$room) {
            $lastMessage         = BaseChatMessage::find()
                ->where(['chat_room_id' => $room['roomId']])
                ->select([
                    'id as messageId',
                    'content as messageContent',
                    'status as messageStatus',
                    'add_time as trueTime',
                ])
                ->orderBy('add_time desc,id desc')
                ->asArray()
                ->one();
            $room                = ArrayHelper::merge($room, $lastMessage);
            $room['listContent'] = BaseChatMessage::getListContent($lastMessage['messageId'], $memberId);
            $room['showTime']    = TimeHelper::formatMessageSessionShowTime($lastMessage['trueTime']);
            //获取职位信息
            $room['info'] = BaseJob::findOneVal(['id' => $room['jobId']], 'name');
            //获取头像
            $room['avatarUrl'] = BaseChatMessage::getMessageAvatar($room['resumeMemberId'], $room['companyId']);
            //获取用户名
            $room['username']     = BaseResume::findOneVal(['member_id' => $room['resumeMemberId']], 'name');
            $companyResumeLibrary = BaseCompanyResumeLibrary::findOne([
                'company_id' => $room['companyId'],
                'resume_id'  => $room['resumeId'],
            ]);
            if (!$companyResumeLibrary) {
                $room['username'] = MaskHelper::getName($room['username']);
            }

            $room['unreadAmount'] = (int)$room['unreadAmount'];

            // 这里看含有几个标签[1,2,3]
            $typeList = [];
            if ($room['creatorType'] == BaseChatRoom::CREATOR_TYPE_PERSON && $room['talkProgress'] == BaseChatRoom::TALK_PROGRESS_ONE_WAY) {
                $typeList[] = '1';
            }
            if ($room['talkProgress'] == BaseChatRoom::TALK_PROGRESS_TWO_WAY) {
                $typeList[] = '2';
            }

            // 找最后一个历史职位是否已投递
            // $lastHistoryJob = BaseChatHistoryJob::find()
            //     ->where(['chat_room_id' => $room['roomId']])
            //     ->select([
            //         'is_apply',
            //     ])
            //     ->orderBy('add_time desc')
            //     ->asArray()
            //     ->one();

            // 找最后一个历史职位是否已投递
            $room['isDelivery'] = false;
            if (BaseJobApplyRecord::find()
                ->where([
                    'resume_id' => $room['resumeId'],
                    'job_id'    => $room['jobId'],
                ])
                ->exists()) {
                $typeList[]         = '3';
                $room['isDelivery'] = true;
            }
            $room['typeList']  = implode(',', $typeList);
            $room['resumeUrl'] = BaseChatRoom::getResumeUrl($room['roomId']);
        }

        return $chatRoomList;
    }

    /**
     * 获取聊天室内容
     * @param $chatId
     * @param $memberId
     * @return array
     */
    public function getChatInfo($chatId, $memberId)
    {
        $infoArr    = [];
        $chatModel  = BaseChatRoom::findOne(['uuid' => $chatId]);
        $memberRole = BaseMember::findOneVal(['id' => $memberId], 'type');

        $jobInfo            = BaseJob::findOne($chatModel->current_job_id);
        $infoArr['jobName'] = $jobInfo->name;
        $infoArr['jobId']   = $jobInfo->id;

        $infoArr['url'] = BaseJob::getDetailUrl($jobInfo->id);
        //根据用户类型，获取聊天室信息（对方的信息）
        if ($memberRole == BaseMember::TYPE_PERSON) {
            if ($jobInfo->status != BaseJob::STATUS_ACTIVE) {
                $jobStatus = '(停止招聘)';
            }

            //获取单位、部门
            $infoArr['companyName'] = BaseCompany::findOneVal(['id' => $chatModel->company_id], 'full_name');
            $infoArr['companyId']   = $chatModel->company_id;
            $infoArr['department']  = BaseCompanyMemberInfo::findOneVal(['member_id' => $chatModel->company_member_id],
                'department');
            $infoArr['memberName']  = BaseCompanyMemberInfo::findOneVal(['member_id' => $chatModel->company_member_id],
                'contact');
            $markInfo               = [];
            // 3、（新增规则）若该C用户未关注服务号，显示如下提示文案；
            //（1）点击，当前页面弹出【服务号关注 弹窗】；
            //（2）若已关注，则不再展示该提示条；
            //（3）点击“X”，关闭提醒，下次启动小程序时才提醒；
            //            开启消息通知，及时接收重要消息。 去开启＞
            $resumeId    = BaseResume::findOneVal(['member_id' => $memberId], 'id');
            $isSubscribe = BaseResumeWxBind::checkSubscribe($resumeId);
            if (PLATFORM == 'MINI' && $isSubscribe == BaseResumeWxBind::IS_SUBSCRIBE_NO) {
                $markInfo['text']     = '开启消息通知，及时接收重要消息。';
                $markInfo['btnText']  = '去开启';
                $markInfo['url']      = '';
                $markInfo['eventKey'] = 'createBindQrcode';
            }
            //2.判断求职者简历完成度
            $resumeComplete    = BaseResume::getComplete($memberId);
            $sysResumeComplete = \Yii::$app->params['completeResumePercent'];
            if ($resumeComplete < $sysResumeComplete) {
                $markInfo['text']     = '您的简历完善度较低，建议完善！';
                $markInfo['btnText']  = '去完善';
                $markInfo['url']      = '/member/person/resume';
                $markInfo['eventKey'] = 'resumeComplete';
            }

            //1.判断单位用户是否还对当前职位协作
            $isRecruiter = BaseJobContactSynergy::isMyJob($chatModel->company_member_id, $jobInfo->id);
            if (!$isRecruiter) {
                $markInfo['text']     = '该老师已不是该职位的招聘者，请以职位详情中的联系方式为准！';
                $markInfo['btnText']  = '';
                $markInfo['url']      = '';
                $markInfo['eventKey'] = 'jobStop';
            }
            if ($markInfo) {
                $infoArr['markInfo'] = $markInfo;
            }
        } elseif ($memberRole == BaseMember::TYPE_COMPANY) {
            //需要判断用户是否存在简历库，如果没有，名字要脱敏
            $infoArr['memberName'] = BaseResume::findOneVal(['member_id' => $chatModel->resume_member_id], 'name');
            $companyResumeLibrary  = BaseCompanyResumeLibrary::findOne([
                'company_id' => $chatModel->company_id,
                'resume_id'  => $chatModel->resume_id,
            ]);
            if (!$companyResumeLibrary) {
                $infoArr['memberName'] = MaskHelper::getName($infoArr['memberName']);
            }
            if ($jobInfo->status == BaseJob::STATUS_DELETE) {
                $jobStatus = '已删除';
            } elseif ($jobInfo->status == BaseJob::STATUS_OFFLINE) {
                $jobStatus = '已下线';
            }
            $isRecruiter = BaseJobContactSynergy::isMyJob($chatModel->company_member_id, $jobInfo->id);

            if (!$isRecruiter) {
                $infoArr['tips'] = '您已非该职位的协同者，建议切换职位进行沟通！';
            }
        }
        $infoArr['jobStatusText'] = $jobStatus ?: '';
        $infoArr['onlineStatus']  = $jobInfo->status == BaseJob::STATUS_ACTIVE;

        return $infoArr;
    }

    // 这里是方便卡片操作完了,返回给前端的数据
    public function getHistoryOne($messageId, $memberId)
    {
        //判断当前用户角色
        $memberRole = BaseMember::findOneVal(['id' => $memberId], 'type');
        $companyId  = $memberRole == BaseMember::TYPE_COMPANY ? BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId],
            'company_id') : '';
        $message    = BaseChatMessage::find()
            ->where(['id' => $messageId])
            ->select([
                'add_time as addTime',
                'id as messageId',
                'status',
                'type',
                'from_member_id as memberId',
                'to_member_id as toMemberId',
                'content',
                'job_id as jobId',
                'is_show_time as isShowTime',
                'chat_room_id as chatRoomId',
            ])
            ->asArray()
            ->one();

        if (!empty($message)) {
            $toMemberType   = BaseMember::findOneVal(['id' => $message['toMemberId']], 'type');
            $fromMemberType = BaseMember::findOneVal(['id' => $message['memberId']], 'type');
            if ($memberId == $message['memberId']) {
                // 是我的消息
                $memberType = $fromMemberType;
            } else {
                $memberType = $toMemberType;
            }
            $content                = json_decode($message['content'], true);
            $content['nowMemberId'] = $memberId;

            //每条消息还原成真实消息
            $message['content']   = BaseChatMessage::getRealContent($message['type'], $memberType, $content,
                $message['messageId']);
            $message['time']      = $message['isShowTime'] == BaseChatMessage::IS_SHOW_TIME_YES ? TimeHelper::formatMessageShowTime($message['addTime']) : '';
            $message['type']      = BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[$message['type']];
            $message['avatar']    = BaseChatMessage::getMessageAvatar($message['memberId'], $companyId);
            $message['chatId']    = BaseChatRoom::findOneVal(['id' => $message['chatRoomId']], 'uuid');
            $message['memberId']  = $message['memberId'] . '';
            $message['messageId'] = $message['messageId'] . '';
            unset($message['nowMemberId']);
        }
        if (!$message) {
            return [];
        }

        return $message;
    }

    public function getHistoryList($chatId, $memberId, $messageId = '', $pageLimit = '')
    {
        //判断用户角色，排除部分记录，例如附件申请消息专属单位端
        $memberRole = BaseMember::findOneVal(['id' => $memberId], 'type');
        //获取需要过滤的类型
        $filterType = $memberRole == BaseMember::TYPE_PERSON ? BaseChatMessage::MESSAGE_TYPE_BELONG_COMPANY : BaseChatMessage::MESSAGE_TYPE_BELONG_PERSON;

        //如果当前用户是单位,获取id
        $companyId = $memberRole == BaseMember::TYPE_COMPANY ? BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId],
            'company_id') : '';

        $chatRoomId = BaseChatRoom::findOneVal(['uuid' => $chatId], 'id');

        $messageTime = BaseChatMessage::findOneVal(['id' => $messageId], 'add_time');

        $query = BaseChatMessage::find()
            ->where(['chat_room_id' => $chatRoomId])
            ->select([
                'add_time as addTime',
                'id as messageId',
                'status',
                'type',
                'from_member_id as memberId',
                'to_member_id as toMemberId',
                'content',
                'job_id',
                'is_show_time as isShowTime',
            ])
            ->andFilterWhere([
                '<',
                'add_time',
                $messageTime,
            ])
            ->andFilterWhere([
                'not in',
                'type',
                $filterType,
            ])
            ->andWhere([
                'or',
                [
                    'and',
                    [
                        '!=',
                        'from_member_id',
                        0,
                    ],
                    [
                        '!=',
                        'to_member_id',
                        0,
                    ],
                ],
                [
                    'from_member_id' => $memberId,
                    'to_member_id'   => 0,
                ],
            ]);
        //        if ($memberRole == BaseMember::TYPE_PERSON) {
        //            $query->andWhere([
        //                '!=',
        //                'to_member_id',
        //                0,
        //            ]);
        //        }
        $list = $query->orderBy('add_time desc')
            ->limit($pageLimit ?: BaseChatMessage::DEFAULT_PAGE_LIMIT)
            ->asArray()
            ->all();

        if (!empty($list)) {
            foreach ($list as &$message) {
                $message['chatId'] = $chatId;
                $toMemberType      = BaseMember::findOneVal(['id' => $message['toMemberId']], 'type');
                $fromMemberType    = BaseMember::findOneVal(['id' => $message['memberId']], 'type');
                if ($memberId == $message['memberId']) {
                    // 是我的消息
                    $memberType        = $fromMemberType;
                    $message['avatar'] = BaseChatMessage::getMessageAvatar($message['memberId']);
                } else {
                    //我是接收的
                    $memberType        = $toMemberType;
                    $message['avatar'] = BaseChatMessage::getMessageAvatar($message['memberId'], $companyId);
                }
                $content                = json_decode($message['content'], true);
                $content['nowMemberId'] = $memberId;

                //每条消息还原成真实消息
                $message['content'] = BaseChatMessage::getRealContent($message['type'], $memberType, $content,
                    $message['messageId']);
                $message['time']    = $message['isShowTime'] == BaseChatMessage::IS_SHOW_TIME_YES ? TimeHelper::formatMessageShowTime($message['addTime']) : '';
                $message['type']    = BaseChatMessage::TYPE_TO_SHOW_TYPE_LIST[$message['type']];

                // 上面用完了nowMemberId,这里就不要了
                unset($message['nowMemberId']);
            }
        }
        if (!$list) {
            return [];
        }
        $sort = array_column($list, 'addTime');
        array_multisort($sort, SORT_ASC, $list);
        if ($memberType == BaseMember::TYPE_COMPANY) {
            //判断房间是否存在附件申请状态
            $isAgreeFile = BaseChatRoom::findOneVal(['id' => $chatRoomId], 'is_agree_file');
            if ($isAgreeFile == BaseChatRoom::IS_AGREE_FILE_WAITING && !$messageId) {
                //需要额外返回一条消息
                $content = BaseChatMessage::getRealContent(BaseChatMessage::TYPE_REQUEST_FILE, $memberRole, [],
                    BaseChatMessage::REQUEST_FILE_MESSAGE_ID);
                $list[]  = [
                    'messageId' => BaseChatMessage::REQUEST_FILE_MESSAGE_ID,
                    // 'content'   => $message['content'] = BaseChatMessage::getRealContent(BaseChatMessage::TYPE_REQUEST_FILE,
                    //     $toMemberType, $message['content'], $message['messageId']),
                    'content'   => $content,
                ];
            }
        }

        return $list;
    }

    public function getChatJobList($chatId, $memberId)
    {
        $chatModel = BaseChatRoom::findOne([
            'uuid'              => $chatId,
            'company_member_id' => $memberId,
        ]);
        if (!$chatModel) {
            throw new \Exception('聊天室不存在');
        }

        //获取当前单位用户的协作职位
        //判断当前用户是主账号，还是子账号
        $companyMemberType = BaseMember::findOneVal(['id' => $memberId], 'company_member_type');
        if ($companyMemberType == BaseMember::COMPANY_MEMBER_TYPE_MAIN) {
            //主账户，获取所有职位
            $jobIdList = BaseJob::find()
                ->where([
                    'company_id' => $chatModel->company_id,
                    'status'     => BaseJob::STATUS_ACTIVE,
                ])
                ->andWhere([
                    '<>',
                    'id',
                    $chatModel->current_job_id,
                ])
                ->select(['id'])
                ->asArray()
                ->column();
        } else {
            //子账号，获取协作职位
            $companyMemberInfoId   = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'id');
            $jobContactSynergyList = BaseJobContactSynergy::find()
                ->where(['company_member_info_id' => $companyMemberInfoId])
                ->select('job_id')
                ->asArray()
                ->column();
            //根据协同职位id列表，寻找职位列表
            $jobIdList = BaseJob::find()
                ->where([
                    'status' => BaseJob::STATUS_ACTIVE,
                    'id'     => $jobContactSynergyList,
                ])
                ->andWhere([
                    '<>',
                    'id',
                    $chatModel->current_job_id,
                ])
                ->select(['id'])
                ->asArray()
                ->column();
        }

        $jobList    = self::getJobFullInfo($jobIdList);
        $currentJob = self::getJobFullInfo($chatModel->current_job_id);
        if ($currentJob[0]['status'] == BaseJob::STATUS_ACTIVE) {
            $currentJob[0]['isCurrent'] = true;
            array_unshift($jobList, $currentJob[0]);
        }

        // 提示文案
        $returnInfo['jobList'] = $jobList;

        if ($jobIdList && !in_array($chatModel->current_job_id, $jobIdList)) {
            $returnInfo['jobInfo']['notice'] = '您已非该职位的协同者，建议切换职位进行沟通';
        } else {
            $returnInfo['jobInfo']['notice'] = '';
        }

        $returnInfo['changeInfo'] = [
            'title'     => '确定切换职位进行沟通？',
            'tis'       => '注：切换沟通职位将通知人才',
            'btnCancel' => '取消',
            'btnSubmit' => '确定',
        ];

        return $returnInfo;
    }

    private function getJobFullInfo($jobIdList)
    {
        if (!$jobIdList) {
            return [];
        }
        //搜索选择字段列表
        $selectArr = [
            'id as jobId',
            'name as jobName',
            'status',
            'education_type as educationType',
            'city_id as cityId',
            'department',
            'announcement_id as announcementId',
            'refresh_time as refreshTime',
        ];
        $jobList   = BaseJob::find()
            ->where(['id' => $jobIdList])
            ->select($selectArr)
            ->orderBy('refresh_time desc')
            ->asArray()
            ->all();
        foreach ($jobList as &$job) {
            $job['isCurrent']         = false;
            $job['url']               = BaseJob::getDetailUrl($job['jobId']);
            $job['area']              = BaseArea::getAreaName($job['cityId']);
            $job['announcementUrl']   = Url::toRoute([
                'announcement/detail',
                'id' => $job['announcementId'],
            ]);
            $job['announcementTitle'] = BaseAnnouncement::findOneVal(['id' => $job['announcementId']], 'title') ?: '';
            $job['education']         = BaseDictionary::getEducationName($job['educationType']);
            // 学历·地区·用人部门(有可能其中有空的,就要过滤点)
            $subTitleArray = [
                $job['education'],
                $job['area'],
                $job['department'],
            ];
            // 过滤掉空的
            $subTitleArray   = array_filter($subTitleArray);
            $job['subtitle'] = implode('·', $subTitleArray);
        }

        return $jobList;
    }

    /**
     * 设置聊天置顶状态
     * @param $chatId
     * @param $memberId
     * @param $isTop
     * @return void
     * @throws Exception
     */
    public function setTop($chatId, $memberId)
    {
        $memberType = BaseMember::findOneVal(['id' => $memberId], 'type');
        //判断聊天室
        $chatModel = BaseChatRoom::findOne([
            'uuid' => $chatId,
        ]);
        if (!$chatModel || ($memberType == BaseMember::TYPE_COMPANY && $chatModel->company_member_id != $memberId) || ($memberType == BaseMember::TYPE_PERSON && $chatModel->resume_member_id != $memberId)) {
            throw new \Exception('聊天室不存在');
        }
        //判断session记录
        $chatRoomSession = BaseChatRoomSession::findOne([
            'chat_room_id' => $chatModel->id,
            'member_id'    => $memberId,
        ]);
        if (!$chatRoomSession) {
            throw new \Exception('聊天室不存在');
        }
        //置顶取反保存
        $isTop                   = $chatRoomSession->is_top == BaseChatRoomSession::IS_TOP_YES ? BaseChatRoomSession::IS_TOP_NO : BaseChatRoomSession::IS_TOP_YES;
        $chatRoomSession->is_top = $isTop;
        if (!$chatRoomSession->save()) {
            throw new Exception('保存失败' . $chatRoomSession->getFirstErrorsMessage());
        }
    }

    /**
     * 删除房间
     * @param $chatId
     * @param $memberId
     * @return void
     * @throws Exception
     */
    public function delRoom($chatId, $memberId)
    {
        $memberType = BaseMember::findOneVal(['id' => $memberId], 'type');
        //判断聊天室
        $chatModel = BaseChatRoom::findOne([
            'uuid' => $chatId,
        ]);
        if (!$chatModel || ($memberType == BaseMember::TYPE_COMPANY && $chatModel->company_member_id != $memberId) || ($memberType == BaseMember::TYPE_PERSON && $chatModel->resume_member_id != $memberId)) {
            throw new \Exception('聊天室不存在');
        }

        //判断session记录
        $chatRoomSession = BaseChatRoomSession::findOne([
            'chat_room_id' => $chatModel->id,
            'member_id'    => $memberId,
        ]);
        if (!$chatRoomSession) {
            throw new \Exception('聊天室不存在');
        }

        $chatRoomSession->is_delete = BaseChatRoomSession::IS_DELETE_YES;
        if (!$chatRoomSession->save()) {
            throw new Exception('保存失败' . $chatRoomSession->getFirstErrorsMessage());
        }
    }

}

