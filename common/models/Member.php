<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "member".
 *
 * @property int $id id;主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态0删除,-1禁止登陆,1正常,9等待审核,-2非合作帐号
 * @property int $type 会员类型(1个人/2企业)
 * @property string $username 用户名
 * @property string $password 密码
 * @property string $email 邮箱
 * @property string $mobile_code 手机号区号
 * @property string $mobile 手机号
 * @property string $avatar 头像
 * @property string $last_login_time 最近一次登录时间
 * @property int $last_login_ip 最近一次登录的ip
 * @property int $email_register_status 邮箱登录状态（-1禁止登录，1正常）
 * @property int $source_type 注册来源（1web端  2H5）
 * @property string $last_active_time 最近活跃时间
 * @property int $company_member_type 单位账号类型 0主账号 1子账号类型
 * @property int $is_chat 是否开启直聊开关（1:是；2:否）
 * @property int $is_chat_window 是否打开聊天窗 1是 2否
 * @property int $is_greeting 是否启用招呼语 1启用 2关闭
 * @property int $greeting_type 默认招呼语类型 1系统 2自定义
 * @property int $greeting_default_id 默认招呼语ID
 */
class Member extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'member';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'last_login_time', 'last_active_time'], 'safe'],
            [['status', 'type', 'last_login_ip', 'email_register_status', 'source_type', 'company_member_type', 'is_chat', 'is_chat_window', 'is_greeting', 'greeting_type', 'greeting_default_id'], 'integer'],
            [['source_type'], 'required'],
            [['username'], 'string', 'max' => 32],
            [['password'], 'string', 'max' => 64],
            [['email'], 'string', 'max' => 256],
            [['mobile_code', 'mobile'], 'string', 'max' => 16],
            [['avatar'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'type' => 'Type',
            'username' => 'Username',
            'password' => 'Password',
            'email' => 'Email',
            'mobile_code' => 'Mobile Code',
            'mobile' => 'Mobile',
            'avatar' => 'Avatar',
            'last_login_time' => 'Last Login Time',
            'last_login_ip' => 'Last Login Ip',
            'email_register_status' => 'Email Register Status',
            'source_type' => 'Source Type',
            'last_active_time' => 'Last Active Time',
            'company_member_type' => 'Company Member Type',
            'is_chat' => 'Is Chat',
            'is_chat_window' => 'Is Chat Window',
            'is_greeting' => 'Is Greeting',
            'greeting_type' => 'Greeting Type',
            'greeting_default_id' => 'Greeting Default ID',
        ];
    }
}
