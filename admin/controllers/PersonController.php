<?php

namespace admin\controllers;

use admin\models\Admin;
use admin\models\JobApply;
use admin\models\Member;
use admin\models\OffSiteJobApply;
use admin\models\Person;
use admin\models\Resume;
use admin\models\ResumeAttachment;
use admin\models\ResumeDownloadLog;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseMemberSchedule;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\helpers\ArrayHelper;
use common\helpers\FormatConverter;
use common\helpers\IpHelper;
use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\libs\ResumeHandle;
use common\libs\ResumeSDK\ResumeIdentify;
use common\libs\WxWork;
use common\service\adminSearch\ResumeListService;
use common\service\jobSubscribe\JobSubscribeApplication;
use frontendPc\models\ResumeSetting;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class PersonController extends BaseAdminController
{
    /**
     * 人才搜索列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSearchList()
    {
        $searchData = Yii::$app->request->get();
        try {
            $app  = new ResumeListService();
            $data = $app->getList($searchData, Yii::$app->user->id);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取求职者统计信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetPersonStatisticsData()
    {
        try {
            return $this->success(Member::getPersonStatisticsData());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取站内投递列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetOnSiteApplyList()
    {
        $searchData = Yii::$app->request->get();
        try {
            $list = JobApply::getApplyList($searchData);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取站外投递列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetOffSiteApplyList()
    {
        $searchData = Yii::$app->request->get();
        try {
            $list = OffSiteJobApply::getOffSiteApplyList($searchData, true, true);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取用户简历详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetResumeInfo()
    {
        $memberId = Yii::$app->request->get('memberId');

        try {
            $info = Resume::checkResumeInfo($memberId);
            //订阅数据
            $subscribeInfo         = Person::getSubscribeInfo($info['userInfo']['resumeId']);
            $info['subscribeInfo'] = $subscribeInfo;
            // 在这里告诉一下系统级别,这个检查被查看了
            $adminId = Yii::$app->user->id;
            $wxWork  = WxWork::getInstance();
            // 找到操作人的信息
            $adminName = Admin::findOneVal(['id' => $adminId], 'name');
            // 找到附件的信息
            $name = $info['userInfo']['name'] ?: $info['userInfo']['username'];
            // ip和地址
            $ip        = IpHelper::getIp();
            $ipAddress = IpHelper::getCity($ip);
            $content   = "{$adminName}ip:{$ip}({$ipAddress})查看了{$name}的在线简历";

            $wxWork->messageToSystem($content);

            return $this->success($info);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    //    todo:日程暂时没有

    /**
     * 获取附件简历列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetResumeAttachmentList()
    {
        $searchData = Yii::$app->request->get();
        try {
            return $this->success(ResumeAttachment::getSearchList($searchData));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取求职者个人的附件简历
     * @return Response|\yii\web\Response
     */
    public function actionGetPersonResumeAttachmentList()
    {
        $memberId = Yii::$app->request->get('memberId');
        if (empty($memberId)) {
            $this->fail('用户信息不能为空');
        }
        try {
            return $this->success(ResumeAttachment::getPersonList($memberId));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改附件简历状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeResumeAttachmentStatus()
    {
        $id = Yii::$app->request->post('id');
        try {
            $id = UUIDHelper::decryption($id);
            ResumeAttachment::changeStatus($id);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改简历显示状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeShowStatus()
    {
        $memberId = \Yii::$app->request->post('memberId');
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        try {
            ResumeSetting::changeHideStatus($resumeId);
            $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改匿名显示状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeAnonymousStatus()
    {
        $memberId = \Yii::$app->request->post('memberId');

        try {
            ResumeSetting::changeAnonymousStatus($memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 修改简历代投状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeProxyDeliverStatus()
    {
        $memberId = \Yii::$app->request->post('memberId');

        try {
            ResumeSetting::changeProxyDeliverStatus($memberId);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取公司下载简历列表
     * @return Response|\yii\web\Response
     */
    public function actionGetCompanyDownloadList()
    {
        $searchData = Yii::$app->request->get();
        if (empty($searchData['memberId'])) {
            $this->fail('用户信息不能为空');
        }
        try {
            return $this->success(ResumeDownloadLog::getCompanyDownloadList($searchData));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取内部下载简历列表
     * @return Response|\yii\web\Response
     */
    public function actionGetInsideDownloadList()
    {
        $searchData = Yii::$app->request->get();
        try {
            return $this->success(ResumeDownloadLog::getInsideDownloadList($searchData));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionResumeAttachmentPreview()
    {
        $token    = Yii::$app->request->get('token');
        $resumeId = Yii::$app->request->get('resumeId');
        $userId   = Yii::$app->user->id;
        // 首先根据

        if (!$token || !$resumeId) {
            return $this->fail('参数错误');
        }

        try {
            $model = new ResumeHandle();
            $model->setOperator($userId, ResumeHandle::OPERATOR_TYPE_ADMIN)
                ->setData($resumeId, $token)
                ->preview();

            exit;
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionResumeAttachmentDownload()
    {
        $token    = Yii::$app->request->get('token');
        $resumeId = Yii::$app->request->get('resumeId');
        $userId   = Yii::$app->user->id;
        // 首先根据

        if (!$token || !$resumeId) {
            return $this->fail('参数错误');
        }

        $transaction = Yii::$app->db->beginTransaction();

        try {
            $model = new ResumeHandle();
            $model->setOperator($userId, ResumeHandle::OPERATOR_TYPE_ADMIN)
                ->setData($resumeId, $token)
                ->download();

            $transaction->commit();
            exit();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取月度日程
     */
    public function actionGetSchedule()
    {
        $memberId = Yii::$app->request->get('memberId');

        return $this->success(BaseMemberSchedule::getMySchedule([], $memberId));
    }

    /**
     * 获取用户日程（通过传递memberId、日期等参数筛选）
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetMemberSchedule()
    {
        $data = Yii::$app->request->get();
        if (empty($data['memberId'])) {
            throw new Exception('用户id不能为空');
        }

        return $this->success(BaseMemberSchedule::getMySchedule(FormatConverter::convertHump($data),
            $data['memberId']));
    }

    /**
     * 这是一个实验性功能
     */
    public function actionResumeAttachmentIdentify()
    {
        $token    = Yii::$app->request->get('token');
        $resumeId = Yii::$app->request->get('resumeId');
        $userId   = Yii::$app->user->id;

        // 只对部分用户开放这个功能
        $adminIds = [
            1,
            2,
            116,
        ];

        if (!in_array($userId, $adminIds)) {
            return $this->fail('您没有权限');
        }

        if (!$token || !$resumeId) {
            return $this->fail('参数错误');
        }

        // 首先找到简历的信息
        $rs = BaseResumeAttachment::findOne([
            'resume_id' => $resumeId,
            'token'     => $token,
        ]);

        $realPath = Yii::getAlias('@resume') . '/' . $rs->file_url;

        if (!file_exists($realPath)) {
            return $this->fail('文件不存在');
        }

        $key = Cache::ALL_RESUME_ATTACHMENT_IDENTIFY_KEY . ':' . $token;

        $data = Cache::get($key);

        if ($data) {
            $result = json_decode($data, true);
        } else {
            $identifySDK = new ResumeIdentify();
            if ($identifySDK->getInfo($realPath)) {
                // 识别成功,放缓存里面,以免后面又去识别了一次
                $result = $identifySDK->result;

                Cache::set($key, json_encode($result));
            }
        }

        unset($result['status']);

        $result['url'] = 'http://www.resumesdk.com/docs/rs-parser.html';

        return $this->success($result);
    }

    /**
     * 返回人才应聘详情界面的报名方式筛选项
     * @return array
     */
    public function actionGetDeliveryWaySelect()
    {
        $type = Yii::$app->request->get('type');
        if ($type == 1) {
            $result = [
                'delivery_way' => ArrayHelper::obj2Arr(BaseJobApplyRecord::OUTER_DELIVERY_WAY),
            ];
        } elseif ($type == 2) {
            $result = [
                'delivery_way' => ArrayHelper::obj2Arr(BaseJobApplyRecord::OUTSIDE_DELIVERY_WAY),
            ];
        } else {
            $result = [
                'delivery_way' => ArrayHelper::obj2Arr(BaseJobApplyRecord::DELIVERY_WAY_NAME),
            ];;
        }
        $this->success($result);
    }

    /**
     * 获取会员信息
     * @return Response|\yii\web\Response
     */
    public function actionGetVipInfo()
    {
        try {
            //获取简历ID
            $resume_id = Yii::$app->request->get('id');

            return $this->success(Resume::getVipInfo($resume_id));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}